/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum ModeloColetorEnum {

    MODELO_COLETOR_TRIXSTANDARD("<PERSON><PERSON><PERSON><PERSON>_COLETOR_TRIXSTANDARD", "Trix Standard"),
    <PERSON><PERSON><PERSON><PERSON>_COLETOR_SERIALPACTO("M<PERSON><PERSON><PERSON>_COLETOR_SERIALPACTO", "Serial Pacto"),
    M<PERSON><PERSON><PERSON>_COLETOR_DESCONHECIDO("MODELO_COLETOR_DESCONHECIDO", " Não Usa Catraca"),
    <PERSON><PERSON><PERSON><PERSON>_COLETOR_INNERTOPDATA("MODELO_COLETOR_INNERTOPDATA", "Inner TopData Serial"), // o nome deveria conter "SERIAL" mas está assim para manter compatibilidade
    MODELO_COLETOR_PARALELAHENRY("MODELO_COLETOR_PARALELAHENRY", "<PERSON><PERSON><PERSON> <PERSON>"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>COLETOR_TCPINNERTOPDATA("M<PERSON><PERSON><PERSON>_COLETOR_TCPINNERTOPDATA", "Inner TopData TCP/IP"),
    <PERSON><PERSON><PERSON><PERSON>_COLETOR_NEOKOROSNKFP2("MODELO_COLETOR_NEOKOROSNKFP2", "Neokoros NK-FP2"),
    MODELO_COLETOR_NEOKOROSFP730("MODELO_COLETOR_NEOKOROSFP730", "Neokoros FP-730"),
    MODELO_COLETOR_PARALELATECNIBRA("MODELO_COLETOR_PARALELATECNIBRA", "Paralela Tecnibra"),
    MODELO_COLETOR_SERIALACTUAR("MODELO_COLETOR_SERIALACTUAR", "Serial Actuar"),
    MODELO_COLETOR_TCPTECNIBRA("MODELO_COLETOR_TCPTECNIBRA", "Tecnibra TCP/IP"),
    MODELO_COLETOR_SERIALTECNIBRA("MODELO_COLETOR_SERIALTECNIBRA", "Tecnibra Serial"),
    MODELO_COLETOR_NEOKOROSNKFP3("MODELO_COLETOR_NEOKOROSNKFP3", "Neokoros NK-FP3"),
    MODELO_COLETOR_HENRYTCPIP("MODELO_COLETOR_HENRYTCPIP", "Henry 7x TCP/IP"),
    MODELO_COLETOR_ALMITEC("MODELO_COLETOR_ALMITEC", "Almitec 306-310"),
    MODELO_COLETOR_ALMITECMAC400("MODELO_COLETOR_ALMITECMAC400","Almitec Mac-400"),
    MODELO_LEITOR_NITGEN("MODELO_LEITOR_NITGEN","Leitor Nitgen"),
    MODELO_COLETOR_ALMITECTCPIPSECDS("MODELO_COLETOR_ALMITECTCPIPSECDS","Almitec DS"),
    MODELO_COLETOR_HENRY8x("MODELO_COLETOR_HENRY8x","Henry PrimeSf - 8X"),
    MODELO_COLETOR_HENRY8xLV("MODELO_COLETOR_HENRY8xLV","Henry PrimeLV - 8X"),
    MODELO_COLETOR_TRIXXPBLOCK("MODELO_COLETOR_TRIXXPBLOCK","Trix Xpblock "),
    MODELO_COLETOR_HENRY8xPRIMME("MODELO_COLETOR_HENRY8xPRIMME","Henry PrimeAcesso - 8X"),
    MODELO_COLETOR_MA100("MODELO_COLETOR_MA100","COMM5 MA100"),
    MODELO_COLETOR_INNER_EVENTOS("MODELO_COLETOR_INNER_EVENTOS","Inner TCP/IP Múltiplas conexões"),
    MODELO_COLETOR_HENRY7xV2("MODELO_COLETOR_HENRY7xV2","Henry 7x v.2 TCP/IP"),
    MODELO_COLETOR_ACTUARTCPIP("MODELO_COLETOR_ACTUARTCPIP", "Actuar TCP/IP"),
    MODELO_COLETOR_HENRY8XFS("MODELO_COLETOR_HENRY8XFS","Henry 8x SF Leitor B1000"),
    MODELO_COLETOR_BIOMTECH("MODELO_COLETOR_BIOMTECH","Biomtech"),
    MODELO_COLETOR_INOVACESSO("MODELO_COLETOR_INOVACESSO", "Inovacesso Fit TCP/IP" ),
    MODELO_COLETOR_SERIALNATSO("MODELO_COLETOR_SERIALNATSO","Serial Natso"),
    MODELO_COLETOR_INTEGRA_FACIL("MODELO_COLETOR_INTEGRA_FACIL","Integra Facil TPC/IP" ),
    MODELO_COLETOR_TUPA("MODELO_COLETOR_TUPA", "Tupa CPBIO TPC/IP"),
    MODELO_COLETOR_SYSTEMTECV4("MODELO_COLETOR_SYSTEMTECV4", "SystemTec TCP/IP"),
    MODELO_COLETOR_USBSERIAL("MODELO_COLETOR_USBSERIAL", "USB Serial"),
    MODELO_CAMERA_ACIONAMENTO("MODELO_CAMERA_ACIONAMENTO","Câmera de Acionamento"),
    MODELO_COLETOR_INTERLAKEN("MODELO_COLETOR_INTERLAKEN", "Interlaken TPC/IP"),
    MODELO_COLETOR_ZK_TF1700("MODELO_COLETOR_ZK_TF1700", "Coletor ZK TF1700"),
    MODELO_COLETOR_IDBLOCK("MODELO_COLETOR_IDBLOCK", "ControlId IdBlock"),
    MODELO_COLETOR_IDFLEX("MODELO_COLETOR_IDFLEX", "ControlId IdFlex"),
    MODELO_COLETOR_HENRYSERIAL("MODELO_COLETOR_HENRYSERIAL", "Henry 7x SERIAL"),
    MODELO_COLETOR_INNER_EVENTOS_LC("MODELO_COLETOR_INNER_EVENTOS_LC", "Inner Catraca Fit LC"),
    MODELO_COLETOR_TECNEW_SERIAL("MODELO_COLETOR_TECNEW_SERIAL", "TecNew Serial"),
    MODELO_COLETOR_ZK_TECO("MODELO_COLETOR_ZK_TECO", "Leitor ZK Teco"),
    MODELO_COLETOR_TCA_SERIAL("MODELO_COLETOR_TCA_SERIAL", "Tca Cqs Serial"),
    MODELO_COLETOR_ZUCHIMZ4("MODELO_COLETOR_ZUCHIMZ4", "Zuchi Mz4 Usb Serial"),
    MODELO_COLETOR_DIMEP_MICROPOINT("MODELO_COLETOR_DIMEP_MICROPOINT", "Dimep Micropoint TCP/IP"),
    MODELO_COLETOR_SERIALFOCA("MODELO_COLETOR_SERIALFOCA", "Serial Foca"),
    MODELO_COLETOR_DIMEP_DGATE("MODELO_COLETOR_DIMEP_DGATE", "Dimep DGate TCP/IP"),
    MODELO_COLETOR_HENRY8xFLAP("MODELO_COLETOR_HENRY8xFLAP","Henry Flap 8x"),
    MODELO_COLETOR_ACTUAR_LITENET2("MODELO_COLETOR_ACTUAR_LITENET2", "Actuar Litenet 2"),
    MODELO_COLETOR_INTELBRAS_SS3530MF("MODELO_COLETOR_INTELBRAS_SS3530MF", "Intelbras SS 3530 MF"),
    MODELO_COLETOR_HIKVISION_ISAPI("MODELO_COLETOR_HIKVISION_ISAPI", "Hikvision Isapi"),
    MODELO_COLETOR_IDFACE("MODELO_COLETOR_IDFACE", "ControlId IdFace"),
    MODELO_COLETOR_ZFACE("MODELO_COLETOR_ZFACE", "Zenite Zface"),
    MODELO_COLETOR_INNERACESSO3("MODELO_COLETOR_INNERACESSO3", "InnerAcesso3 TopData"),
    MODELO_COLETOR_IDBLOCKNEXT("MODELO_COLETOR_IDBLOCKNEXT", "ControlId IdBlock Next"),
    MODELO_COLETOR_IDACCESSPRO("MODELO_COLETOR_IDACCESSPRO", "ControlId IdAccessPro"),
    MODELO_COLETOR_HOLLEMAX("MODELO_COLETOR_HOLLEMAX", "Hollemax Catracas"),
    MODELO_COLETOR_INTELBRAS_SS5530MF("MODELO_COLETOR_INTELBRAS_SS5530MF", "Intelbras SS 5530 MF"),
    MODELO_COLETOR_INTELBRAS_SS5531MF("MODELO_COLETOR_INTELBRAS_SS5531MF", "Intelbras SS 5531 MF"),
    MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX("MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX", "Hikivision Ds_K1t342mfwx"),
    MODELO_COLETOR_HIKIVISION_DS_K1T673DX("MODELO_COLETOR_HIKIVISION_DS_K1T673DX", "Hikivision Ds_K1t673dx"),
    MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR("MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR", "Hikivision Ds_K1t342mwx-Br"),
    MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR("MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR", "Hikivision Ds_K1t673dx_Br"),
    MODELO_COLETOR_HIKIVISION_DS_K1T671M_L("MODELO_COLETOR_HIKIVISION_DS_K1T671M_L", "Hikivision Ds_K1t671m_L"),
    MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF("MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF", "Hikivision Ds-K1t671tm-3xf")
    ;

    private String id;
    private String descricao;

    private ModeloColetorEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
