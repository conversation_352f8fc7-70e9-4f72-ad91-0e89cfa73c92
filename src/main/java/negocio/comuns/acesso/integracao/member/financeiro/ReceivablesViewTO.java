package negocio.comuns.acesso.integracao.member.financeiro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.json.JSONObject;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReceivablesViewTO {

    private Integer idReceivable;
    private String description;
    private String registrationDate;
    private String dueDate;
    private String receivingDate;
    private String competenceDate;
    private String cancellationDate;
    private Double ammount;
    private Double ammountPaid;
    private StatusViewTO status;
    private Integer currentInstallment;
    private Integer totalInstallments;
    private String authorization;
    private String payerName;
    private Integer idMemberPayer;
    private Integer idProspectPayer;
    private Integer idBranchMember;
    private Integer idSale;
    private BankAccountViewTO bankAccount;
    private PaymentTypeViewTO paymentType;
    private List<InvoiceDetailViewTO> invoiceDetails;
    private Integer fees;
    private boolean conciliated;
    private String tid;
    private String nsu;
    private String updateDate;
    private String chargeDate;
    private Integer idReceivableFrom;
    private String cardAcquirer;
    private String cardFlag;
    private List<CreditDetailViewTO> creditDetails;
    private String cancellationDescription;
    private String source;
    private String saleDate;
    private JSONObject dataJson;

    public Integer getIdReceivable() {
        return idReceivable;
    }

    public void setIdReceivable(Integer idReceivable) {
        this.idReceivable = idReceivable;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(String registrationDate) {
        this.registrationDate = registrationDate;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public String getReceivingDate() {
        return receivingDate;
    }

    public void setReceivingDate(String receivingDate) {
        this.receivingDate = receivingDate;
    }

    public String getCompetenceDate() {
        return competenceDate;
    }

    public void setCompetenceDate(String competenceDate) {
        this.competenceDate = competenceDate;
    }

    public String getCancellationDate() {
        return cancellationDate;
    }

    public void setCancellationDate(String cancellationDate) {
        this.cancellationDate = cancellationDate;
    }

    public Double getAmmount() {
        return ammount;
    }

    public void setAmmount(Double ammount) {
        this.ammount = ammount;
    }

    public Double getAmmountPaid() {
        return ammountPaid;
    }

    public void setAmmountPaid(Double ammountPaid) {
        this.ammountPaid = ammountPaid;
    }

    public StatusViewTO getStatus() {
        return status;
    }

    public void setStatus(StatusViewTO status) {
        this.status = status;
    }

    public Integer getCurrentInstallment() {
        return currentInstallment;
    }

    public void setCurrentInstallment(Integer currentInstallment) {
        this.currentInstallment = currentInstallment;
    }

    public Integer getTotalInstallments() {
        return totalInstallments;
    }

    public void setTotalInstallments(Integer totalInstallments) {
        this.totalInstallments = totalInstallments;
    }

    public String getAuthorization() {
        return authorization;
    }

    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public Integer getIdMemberPayer() {
        return idMemberPayer;
    }

    public void setIdMemberPayer(Integer idMemberPayer) {
        this.idMemberPayer = idMemberPayer;
    }

    public Integer getIdProspectPayer() {
        return idProspectPayer;
    }

    public void setIdProspectPayer(Integer idProspectPayer) {
        this.idProspectPayer = idProspectPayer;
    }

    public Integer getIdBranchMember() {
        return idBranchMember;
    }

    public void setIdBranchMember(Integer idBranchMember) {
        this.idBranchMember = idBranchMember;
    }

    public Integer getIdSale() {
        return idSale;
    }

    public void setIdSale(Integer idSale) {
        this.idSale = idSale;
    }

    public BankAccountViewTO getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(BankAccountViewTO bankAccount) {
        this.bankAccount = bankAccount;
    }

    public PaymentTypeViewTO getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentTypeViewTO paymentType) {
        this.paymentType = paymentType;
    }

    public List<InvoiceDetailViewTO> getInvoiceDetails() {
        return invoiceDetails;
    }

    public void setInvoiceDetails(List<InvoiceDetailViewTO> invoiceDetails) {
        this.invoiceDetails = invoiceDetails;
    }

    public Integer getFees() {
        return fees;
    }

    public void setFees(Integer fees) {
        this.fees = fees;
    }

    public boolean isConciliated() {
        return conciliated;
    }

    public void setConciliated(boolean conciliated) {
        this.conciliated = conciliated;
    }


    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getChargeDate() {
        return chargeDate;
    }

    public void setChargeDate(String chargeDate) {
        this.chargeDate = chargeDate;
    }

    public Integer getIdReceivableFrom() {
        return idReceivableFrom;
    }

    public void setIdReceivableFrom(Integer idReceivableFrom) {
        this.idReceivableFrom = idReceivableFrom;
    }

    public String getCardAcquirer() {
        return cardAcquirer;
    }

    public void setCardAcquirer(String cardAcquirer) {
        this.cardAcquirer = cardAcquirer;
    }

    public String getCardFlag() {
        return cardFlag;
    }

    public void setCardFlag(String cardFlag) {
        this.cardFlag = cardFlag;
    }

    public List<CreditDetailViewTO> getCreditDetails() {
        return creditDetails;
    }

    public void setCreditDetails(List<CreditDetailViewTO> creditDetails) {
        this.creditDetails = creditDetails;
    }

    public String getCancellationDescription() {
        return cancellationDescription;
    }

    public void setCancellationDescription(String cancellationDescription) {
        this.cancellationDescription = cancellationDescription;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSaleDate() {
        return saleDate;
    }

    public void setSaleDate(String saleDate) {
        this.saleDate = saleDate;
    }

    public JSONObject getDataJson() {
        return dataJson;
    }

    public void setDataJson(JSONObject dataJson) {
        this.dataJson = dataJson;
    }
}
