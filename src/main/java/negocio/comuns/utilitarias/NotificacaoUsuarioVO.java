package negocio.comuns.utilitarias;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;

import java.util.Date;

/*
 * Created by <PERSON><PERSON> on 26/01/2017.
 */
public class NotificacaoUsuarioVO extends SuperVO {

    private Integer codigo;
    private UsuarioVO usuarioVO;
    private EmpresaVO empresaVO;
    private TipoNotificacaoUsuarioEnum tipo;
    private Date dataLancamento;
    private String mensagem;
    private String link;
    private boolean apresentarHoje;
    private Date dataNaoApresentar;
    private String dados; //salva um JSON

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public TipoNotificacaoUsuarioEnum getTipo() {
        if (tipo == null) {
            tipo = TipoNotificacaoUsuarioEnum.NENHUM;
        }
        return tipo;
    }

    public void setTipo(TipoNotificacaoUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getLink() {
        if (link == null) {
            link = "";
        }
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public boolean isApresentarHoje() {
        return apresentarHoje;
    }

    public void setApresentarHoje(boolean apresentarHoje) {
        this.apresentarHoje = apresentarHoje;
    }

    public String getLinkMontadaUCP() {
        try {
            String request = (String) JSFUtilities.getManagedBean("SuperControle.contextPath");

            if (tipo.equals(TipoNotificacaoUsuarioEnum.ENCONTROS_UCP)) {
                return request + "/redir?up&visualizacaoUCP=" + TipoNotificacaoUsuarioEnum.ENCONTROS_UCP.getLinkAgrupado();
            } else if (tipo.equals(TipoNotificacaoUsuarioEnum.UNIVERSIDADE_UCP) || tipo.equals(TipoNotificacaoUsuarioEnum.RESPOSTA_UCP)) {
                return request + "/redir?up&codPerguntaUCP=" + getLink();
            } else {
                return request + "/redir?up";
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDados() {
        if (dados == null) {
            dados = "";
        }
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public Date getDataNaoApresentar() {
        return dataNaoApresentar;
    }

    public void setDataNaoApresentar(Date dataNaoApresentar) {
        this.dataNaoApresentar = dataNaoApresentar;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }
}
