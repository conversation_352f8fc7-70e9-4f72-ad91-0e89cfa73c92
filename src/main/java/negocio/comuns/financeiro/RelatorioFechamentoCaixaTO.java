package negocio.comuns.financeiro;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class RelatorioFechamentoCaixaTO extends SuperTO {

    private List<ResumoFormaPagamentoRelatorio> formasPagamento = new ArrayList<ResumoFormaPagamentoRelatorio>();
    private List<ContaVO> contas = new ArrayList<ContaVO>();
    private List<TotaisFormaPagamento> totaisPorForma = new ArrayList<TotaisFormaPagamento>();
    private List<TotaisRecebidos> totaisRecebidos = new ArrayList<TotaisRecebidos>();
    private List<CaixaAgrupar> caixas = new ArrayList<CaixaAgrupar>();
    private ContaVO totalContas = new ContaVO();
    private TotaisFormaPagamento totalFP = new TotaisFormaPagamento();


    public class CaixaAgrupar extends SuperTO {
        private Integer codigoCaixa;
        private String nomeCaixa = "";
        private List<ItemRelatorioFechamentoCaixaTO> listaMovimentacoes = new ArrayList<ItemRelatorioFechamentoCaixaTO>();

        public String getDescricao() {
            return codigoCaixa.toString() + " - " + nomeCaixa;
        }

        public void setNomeCaixa(String nomeCaixa) {
            this.nomeCaixa = nomeCaixa;
        }

        public String getNomeCaixa() {
            return nomeCaixa;
        }

        public void setListaMovimentacoes(List<ItemRelatorioFechamentoCaixaTO> listaMovimentacoes) {
            this.listaMovimentacoes = listaMovimentacoes;
        }

        public List<ItemRelatorioFechamentoCaixaTO> getListaMovimentacoes() {
            return listaMovimentacoes;
        }

        public Double getSaldoFinal() {
            Double saldoFinal = 0.0;
            for (ItemRelatorioFechamentoCaixaTO irfc : listaMovimentacoes) {
                if (irfc.getTipo().equals(TipoES.ENTRADA)) {
                    saldoFinal += irfc.getValor();
                } else {
                    saldoFinal -= irfc.getValor();
                }
            }
            return saldoFinal;
        }

        public String getSaldoFinalApresentar() {
            Double saldoFinal = getSaldoFinal();
            return (saldoFinal < 0.0 ? " - " : "") + Formatador.formatarValorMonetarioSemMoeda(saldoFinal);
        }

        public JRDataSource getMovimentacoesJR() {
            JRDataSource jr1 = new JRBeanCollectionDataSource(listaMovimentacoes);
            return jr1;
        }

        public void setCodigoCaixa(Integer codigoCaixa) {
            this.codigoCaixa = codigoCaixa;
        }

        public Integer getCodigoCaixa() {
            return codigoCaixa;
        }

        public boolean getTemMovimentacoes() {
            return !getListaMovimentacoes().isEmpty();
        }
    }

    public void totalizarFormas() {
        totalFP = new TotaisFormaPagamento();
        for (TotaisFormaPagamento tot : totaisPorForma) {
            totalFP.setEntrada(totalFP.getEntrada() + tot.getEntrada());
            totalFP.setSaida(totalFP.getSaida() + tot.getSaida());
            totalFP.setSaldo(totalFP.getSaldo() + tot.getSaldo());
        }
    }

    public void totalizarContas() {
        totalContas = new ContaVO();
        for (ContaVO cc : contas) {
            totalContas.setSaldoInicial(totalContas.getSaldoInicial() + cc.getSaldoInicial());
            totalContas.setSaida(totalContas.getSaida() + cc.getSaida());
            totalContas.setSaldoAtual(totalContas.getSaldoAtual() + cc.getSaldoAtual());
            totalContas.setEntrada(totalContas.getEntrada() + cc.getEntrada());
        }
    }

    public Double getTotalRecebiveis() {
        double total = 0.0;
        for (ResumoFormaPagamentoRelatorio rf : formasPagamento) {
            total += rf.getValor();
        }
        return total;
    }

    public Double getTotalRecebidos() {
        double total = 0.0;
        for (TotaisRecebidos rf : totaisRecebidos) {
            total += rf.getSaldo();
        }
        return total;
    }

    public JRDataSource getFormasJR() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getFormasPagamento());
        return jr1;
    }


    public JRDataSource getCaixasJR() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getCaixas());
        return jr1;
    }

    public JRDataSource getContasJR() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getContas());
        return jr1;
    }

    public JRDataSource getContasJR2() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getContas());
        return jr1;
    }

    public JRDataSource getTotaisFormaJR() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getTotaisPorForma());
        return jr1;
    }

    public JRDataSource getTotaisRecebidosJR() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getTotaisRecebidos());
        return jr1;
    }

    public class TotaisRecebidos extends SuperTO {
        private String descricao;
        private Double saldo = 0.0;

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setSaldo(Double saldo) {
            this.saldo = saldo;
        }

        public Double getSaldo() {
            return saldo;
        }

        public String getSaldoApresentar() {
            return Formatador.formatarValorMonetarioSemMoeda(saldo);
        }

    }

    public class TotaisFormaPagamento extends SuperTO {
        private String descricao = "";
        private Double entrada = 0.0;
        private Double saida = 0.0;
        private Double saldo = 0.0;

        public TotaisFormaPagamento() {

        }

        public TotaisFormaPagamento(String descricao, double entrada, double saida) {
            this.setDescricao(descricao);
            this.setEntrada(entrada);
            this.setSaida(saida);
            this.setSaldo(entrada - saida);
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setEntrada(Double entrada) {
            this.entrada = entrada;
        }

        public Double getEntrada() {
            return entrada;
        }

        public void setSaida(Double saida) {
            this.saida = saida;
        }

        public Double getSaida() {
            return saida;
        }

        public void setSaldo(Double saldo) {
            this.saldo = saldo;
        }

        public Double getSaldo() {
            return saldo;
        }

        public String getSaldoApresentar() {
            return (saldo < 0.0 ? " - " : "") + (Formatador.formatarValorMonetarioSemMoeda(this.saldo));
        }

        public String getEntradaApresentar() {
            return Formatador.formatarValorMonetarioSemMoeda(entrada);
        }

        public String getSaidaApresentar() {
            return Formatador.formatarValorMonetarioSemMoeda(saida);
        }

    }

    public void setFormasPagamento(List<ResumoFormaPagamentoRelatorio> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public List<ResumoFormaPagamentoRelatorio> getFormasPagamento() {
        return formasPagamento;
    }

    public void setContas(List<ContaVO> contas) {
        this.contas = contas;
    }

    public List<ContaVO> getContas() {
        return contas;
    }

    public void setTotaisPorForma(List<TotaisFormaPagamento> totaisPorForma) {
        this.totaisPorForma = totaisPorForma;
    }

    public List<TotaisFormaPagamento> getTotaisPorForma() {
        return totaisPorForma;
    }

    public void setTotaisRecebidos(List<TotaisRecebidos> totaisRecebidos) {
        this.totaisRecebidos = totaisRecebidos;
    }

    public List<TotaisRecebidos> getTotaisRecebidos() {
        return totaisRecebidos;
    }

    public void setTotalContas(ContaVO totalContas) {
        this.totalContas = totalContas;
    }

    public ContaVO getTotalContas() {
        return totalContas;
    }

    public void setTotalFP(TotaisFormaPagamento totalFP) {
        this.totalFP = totalFP;
    }

    public TotaisFormaPagamento getTotalFP() {
        return totalFP;
    }

    public void setCaixas(List<CaixaAgrupar> caixas) {
        this.caixas = caixas;
    }

    public List<CaixaAgrupar> getCaixas() {
        return caixas;
    }


}
