package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

public class PinPadVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String descricao;
    private Integer pinpad = null;
    private String cnpjPinpad = null;
    private String pdvPinpad = null;
    private Boolean isAutoAtendimento = false;

    private OpcoesPinpadEnum opcoesPinpadEnum;
    private Integer nrMaxParcelas;

    @ChaveEstrangeira
    private FormaPagamentoVO formaPagamento = null;
    @ChaveEstrangeira
    private EmpresaVO empresa;
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobranca;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public OpcoesPinpadEnum getPinpadEnum() {
        return OpcoesPinpadEnum.fromCodigo(this.getPinpad());
    }

    public Integer getPinpad() {
        if(pinpad == null){
            pinpad = 0;
        }
        return pinpad;
    }

    public void setPinpad(Integer pinpad) {
        opcoesPinpadEnum = OpcoesPinpadEnum.fromCodigo(pinpad);
        this.pinpad = pinpad;
    }

    public String getCnpjPinpad() {
        return cnpjPinpad;
    }

    public void setCnpjPinpad(String cnpjPinpad) {
        this.cnpjPinpad = cnpjPinpad;
    }

    public String getPdvPinpad() {
        return pdvPinpad;
    }

    public void setPdvPinpad(String pdvPinpad) {
        this.pdvPinpad = pdvPinpad;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if(formaPagamento == null){
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public OpcoesPinpadEnum getOpcoesPinpadEnum() {
        return opcoesPinpadEnum;
    }

    public Boolean getAutoAtendimento() {
        return isAutoAtendimento;
    }

    public void setAutoAtendimento(Boolean autoAtendimento) {
        isAutoAtendimento = autoAtendimento;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        if (UteisValidacao.emptyString(descricao)) {
            return "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isCAPPTA() {
        return getPinpad() != null && getPinpad().equals(OpcoesPinpadEnum.CAPPTA.getCodigo());
    }

    public boolean isGEOIT(){
        return getPinpad() != null && getPinpad().equals(OpcoesPinpadEnum.GEOITD.getCodigo());
    }

    public boolean isStoneConnect(){
        return getPinpad() != null && getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT.getCodigo());
    }

    public boolean isGetCard(){
        return getPinpad() != null && getPinpad().equals(OpcoesPinpadEnum.GETCARD.getCodigo());
    }

    public ConvenioCobrancaVO getConvenioCobranca() {
        if (convenioCobranca == null) {
            convenioCobranca = new ConvenioCobrancaVO();
        }
        return convenioCobranca;
    }

    public void setConvenioCobranca(ConvenioCobrancaVO convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Integer getNrMaxParcelas() {
        if (nrMaxParcelas == null) {
            nrMaxParcelas = 0;
        }
        return nrMaxParcelas;
    }

    public void setNrMaxParcelas(Integer nrMaxParcelas) {
        this.nrMaxParcelas = nrMaxParcelas;
    }
}
