package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum ComportamentoConta {
	CAIXA(1,"Conta Caixa"),
	BANCO(2,"Conta Banco"),
	COFRE(3,"Conta Cofre"),
	PENDENCIAS(4,"Pendências"),
	DEVOLUCOES(5,"Devoluções"),
	CUSTODIA(6, "Conta Custódia"),
	OPENBANK(7, "OpenBank"),
	;
	
	private Integer codigo;
	private String descricao;
	
	private ComportamentoConta(int codigo, String descricao) {
		this.codigo = codigo;
		this.descricao = descricao;
	}
	
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public Integer getCodigo() {
		return codigo;
	}
	
	public static ComportamentoConta getComportamentoConta(final int codigo) {
		ComportamentoConta cc = null;
		for (ComportamentoConta comp : ComportamentoConta.values()) {
			if (comp.getCodigo() == codigo){
				cc = comp;
			}
		}
		return cc;
	}
	
	public static List<SelectItem> getListaSelectItem(){
		List<SelectItem> lista = new ArrayList<SelectItem>();
		for(ComportamentoConta comp : ComportamentoConta.values()){
			if(comp.codigo == ComportamentoConta.OPENBANK.getCodigo()){ // Tipo openbank apenas inserido pela integração openbank.
				continue;
			}
			lista.add(new SelectItem(comp.codigo, comp.descricao));
		}
		return lista; 
	}

	public static ComportamentoConta getFromDescricao(String descricao) {
		for (ComportamentoConta cfg : ComportamentoConta.values()) {
			if (descricao != null && cfg.getDescricao().equals(descricao)) {
				return cfg;
			}
		}
		return null;

	}
	
	
}
