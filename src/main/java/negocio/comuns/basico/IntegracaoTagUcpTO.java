package negocio.comuns.basico;


import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 24/02/2017.
 */
public class IntegracaoTagUcpTO extends SuperTO {

    private Integer codigo;
    private String nome;
    private Integer qtdConhecimentos;
    private List<PerguntaUcpTO> perguntas;

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getQtdConhecimentos() {
        if (qtdConhecimentos == null) {
            qtdConhecimentos = 0;
        }
        return qtdConhecimentos;
    }

    public void setQtdConhecimentos(Integer qtdConhecimentos) {
        this.qtdConhecimentos = qtdConhecimentos;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<PerguntaUcpTO> getPerguntas() {
        if (perguntas == null) {
            perguntas = new ArrayList<PerguntaUcpTO>();
}
        return perguntas;
    }

    public void setPerguntas(List<PerguntaUcpTO> perguntas) {
        this.perguntas = perguntas;
    }
}
