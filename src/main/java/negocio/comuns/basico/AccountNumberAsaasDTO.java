package negocio.comuns.basico;

import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 26/05/2023
 */

public class AccountNumberAsaasDTO{

    private String agency;
    private String account;
    private String accountDigit;

    public AccountNumberAsaasDTO(JSONObject json) {
        this.agency = json.optString("agency");
        this.account = json.optString("account");;
        this.accountDigit = json.optString("accountDigit");;
    }

    public String getAgency() {
        return agency;
    }

    public void setAgency(String agency) {
        this.agency = agency;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccountDigit() {
        return accountDigit;
    }

    public void setAccountDigit(String accountDigit) {
        this.accountDigit = accountDigit;
    }
}
