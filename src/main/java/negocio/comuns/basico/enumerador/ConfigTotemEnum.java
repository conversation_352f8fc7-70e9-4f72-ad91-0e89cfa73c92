package negocio.comuns.basico.enumerador;

import java.util.ArrayList;
import java.util.List;

public enum ConfigTotemEnum {

    USAR_PINPAD_DEBITO("boolean", null, null, false, "true","","1", false),
    FORMA_PAGAMENTO_DEBITO("combo", "formasPgto", USAR_PINPAD_DEBITO, false, "","","2", false),
    USAR_PINPAD_CREDITO("boolean", null, null, false, "true", "","3", false),
    FORMA_PAGAMENTO_CREDITO("combo", "formasPgto", USAR_PINPAD_CREDITO, false, "","","4", false),
    USAR_TRANSACAO_ONLINE("boolean", null, null, false, "true","","5", false),
    CONVENIO_COBRANCA("combo", "listaSelectConvenioCobranca", null, false, "","","6", false),
    CONVENIO_COBRANCA_DCO("combo", "listaSelectConvenioCobrancaDCO", null, true, "","","7", false),
    USAR_RENOVAR_PLANO("boolean", null, null, false, "true","","8", false),
    USAR_PLANO("boolean", null, null, false, "true","","9", false),
    USAR_MEU_FINANCEIRO("boolean", null, null, false, "true", "","10", false),
    USAR_TREINO_DIA("boolean", null, null, false, "true","","11", false),
    USAR_AVALIACAO_FISICA("boolean", null, null, false, "true","","12", false),
    USAR_AULAS_COLETIVAS("boolean", null, null, false, "true","","13", false),
    USAR_MEUS_CONTRATOS("boolean", null, null, false, "true","","14", false),
    USAR_CAPTURAR_FOTO("boolean", null, null, false, "true","","15", false),
    USAR_BIOMETRIA("boolean", null, null, false, "true","","16", false),
    PAGAR_PRIMEIRA_A_VISTA("boolean", null, null, false, "false","","17", false),
    ALUNO_ALTERA_DIA_VENCIMENTO("boolean", null, null, false, "false","","18", false),
    ALUNO_PREENCHER_BV("boolean", null, null, false, "true","","19", false),
    ALUNO_PREENCHER_PARQ("boolean", null, null, false, "false","","20", false),
    USAR_DCO("boolean", null, null, false, "false","","21", false),
    PERMITE_PAGAR_DEPOIS("boolean", null, null, false, "true", "PERMITE_PAGAR_DEPOIS_HINT", "22", false),
    PERMITE_SELECIONAR_CONSULTOR("boolean", null, null, false, "false", "PERMITE_SELECIONAR_CONSULTOR_HINT","23", false),
    NOME_ADESAO("text", null, null, false, "Taxa de adesão", "NOME_ADESAO_HINT", "24", false),
    PERMITIR_CUPOM("boolean", null, null , false, "false","","26", false),
    USAR_MEUS_CONTRATOS_IMPRIMIR_CONTRATO("boolean", null, USAR_MEUS_CONTRATOS, false, "true", "","27", false),
    USAR_MEUS_CONTRATOS_FERIAS("boolean", null, USAR_MEUS_CONTRATOS, false, "true","","28", false),
    USAR_MEUS_CONTRATOS_TRANCAMENTO("boolean", null, USAR_MEUS_CONTRATOS, false, "true","","29", false),
    COBRAR_NO_CADASTRO("boolean", null, null , false, "false","","30", false),
    TELEFONE_OBRIGATORIO("boolean", null, null , false, "true","","31", false),
    EMAIL_OBRIGATORIO("boolean", null, null , false, "true","","32", false),
    ENDERECO_OBRIGATORIO("boolean", null, null , false, "true","","33", false),
    PERMITIR_CONVIDAR("boolean", null, null , false, "false" ,"","34", false),
    PROIBIR_RENOVAR_PLANO_COM_PARCELAS_ABERTAS("boolean", null, null, false, "false", "PROIBIR_RENOVAR_PLANO_COM_PARCELAS_ABERTAS_HINT", "35", false),
    MOSTRAR_MSG_PARCELA_PAGAR("boolean", null, null, false, "true", "MOSTRAR_MSG_PAGAR_PARCELAS_HINT", "36", false),
    HABILITAR_CARD_CADASTRO_VISITANTE("boolean", null, null, false, "false","","37", false),
    HABILITAR_MULTI_EMPRESA("boolean", null, null, false, "false","","38", true),
    LINK_APP_IOS("text", null, null, false, "Link app ios", "LINK_APP_IOS_HINT", "39", false),
    LINK_APP_ANDROID("text", null, null, false, "Link app Android", "LINK_APP_ANDROID_HINT", "40", false),
    HABILITAR_STONE_CONNECT("boolean", null, null, false, "false","","41", true),
    PINPAD_STONE_CONNECT_DEBITO("combo", "pinPadStoneConnect", HABILITAR_STONE_CONNECT, false, "","","42", false),
    PINPAD_STONE_CONNECT_CREDITO("combo", "pinPadStoneConnect", HABILITAR_STONE_CONNECT, false, "","","43", false),
    EXIGIR_RESPONSAVEL_MENOR("boolean", null, null, false, "false", "", "44", false),
    FORMATO_VALOR_TOTAL_OPCAO_PARCELADO_PINPAD("boolean", null, USAR_PINPAD_CREDITO, false, "false","","45", false),
    APRESENTAR_PARCELAS_EA_BOLETO("boolean", null, null, false, "false","","46", false),
    HABILITAR_QR_CODE_PIX("boolean", null, null, false, "false","HABILITAR_QR_CODE_PIX_HINT","47", false),
    PERMITIR_ESTORNAR_CONTRATO_TROCA_PLANO("boolean", null, null, false, "false", "PERMITIR_ESTORNAR_CONTRATO_TROCA_PLANO_HINT", "48", false);

    private String tipo;
    private String prop;
    private String padrao;
    private String hint;
    private ConfigTotemEnum dependencia;
    private boolean mostrarConfEmpresa;
    private String id;
    private boolean recursoSomenteMultiEmpresa;

    ConfigTotemEnum(String tipo, String prop, ConfigTotemEnum dependencia, boolean mostrarConfEmpresa, String padrao) {
        this.padrao = padrao;
        this.tipo = tipo;
        this.prop = prop;
        this.hint = "";
        this.dependencia = dependencia;
        this.mostrarConfEmpresa = mostrarConfEmpresa;
    }
    ConfigTotemEnum(String tipo, String prop, ConfigTotemEnum dependencia, boolean mostrarConfEmpresa, String padrao, String hint, String id, boolean recursoSomenteMultiEmpresa) {
        this.padrao = padrao;
        this.tipo = tipo;
        this.prop = prop;
        this.hint = hint;
        this.dependencia = dependencia;
        this.mostrarConfEmpresa = mostrarConfEmpresa;
        this.id = id;
        this.recursoSomenteMultiEmpresa = recursoSomenteMultiEmpresa;
    }

    public static ConfigTotemEnum obter(int ord){
        for(ConfigTotemEnum f : ConfigTotemEnum.values()){
            if(f.ordinal() == ord){
                return f;
            }
        }
        return null;
    }

    public static List<ConfigTotemEnum> valores(boolean permiteMultiEmpresa){
        List<ConfigTotemEnum> l = new ArrayList<ConfigTotemEnum>();
        for(ConfigTotemEnum f : ConfigTotemEnum.values()){
            if(!f.isMostrarConfEmpresa()){
                if (f.isRecursoSomenteMultiEmpresa() && !permiteMultiEmpresa) {
                    continue;
                }
                l.add(f);
            }
        }
        return l;
    }

    public ConfigTotemEnum getDependencia() {
        return dependencia;
    }

    public String getTipo() {
        return tipo;
    }

    public String getProp() {
        return prop;
    }

    public boolean isMostrarConfEmpresa() {
        return mostrarConfEmpresa;
    }

    public void setMostrarConfEmpresa(boolean mostrarConfEmpresa) {
        this.mostrarConfEmpresa = mostrarConfEmpresa;
    }

    public String getPadrao() {
        return padrao;
    }

    public void setPadrao(String padrao) {
        this.padrao = padrao;
    }

    public String getHint() {
        return hint;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isRecursoSomenteMultiEmpresa() {
        return recursoSomenteMultiEmpresa;
    }

    public void setRecursoSomenteMultiEmpresa(boolean recursoSomenteMultiEmpresa) {
        this.recursoSomenteMultiEmpresa = recursoSomenteMultiEmpresa;
    }
}
