package negocio.comuns.basico.enumerador;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public enum TipoBIDCC {
    ContratosAtivosRecorrencia(0,"Contratos ativos","Contratos ativos por regime de Recorrência",false,false,false,false,false, null, ItemExportacaoEnum.BI_DCC_CONTRATOS_ATIVOS),
    ContratosCanceladosRecorrencia(1,"Contratos cancelados automaticamente","Contratos cancelados automaticamente pela Recorrência",false,false,false,false,false, null, ItemExportacaoEnum.BI_DCC_CONTRATOS_CANCELADOS),
    ContratosNaoRenovadosRecorrencia(2,"Contratos não renovados automaticamente","Contratos que não conseguiram renovação automaticamente",false,false,false,false,false, null, ItemExportacaoEnum.BI_DCC_CONTRATOS_NAO_RENOVADOS),
    ContratosSemAutorizacaoCobranca(3,"Contratos sem Autorização de Cobrança","Contratos ativos por regime de Recorrência sem Autorização de Cobrança",false,false,true,false,false, null, ItemExportacaoEnum.BI_DCC_CONTRATOS_SEM_AUTORIZACAOCOBRANCA),
    CartoesCreditoVencidos(4,"Cartões de Crédito Vencidos","Cartões de Crédito Vencidos",false,false,true,false,false, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, ItemExportacaoEnum.PENDENCIA_CARTOES_VENCIDOS),
    CartoesCreditoAVencer(5,"Cartões de Crédito A Vencer Próximo Mês","Cartões de Crédito A Vencer Próximo Mês",false,false,true,false,false, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, ItemExportacaoEnum.PENDENCIA_CARTOES_AVENCER),
    ClientesMesmoCartao(6,"Clientes com o mesmo Cartão de Crédito","Clientes com o mesmo Cartão de Crédito",false,false,true,false,false,TipoAutorizacaoCobrancaEnum.CARTAOCREDITO,  ItemExportacaoEnum.PENDENCIA_MESMO_CARTOES),
    CartoesComProblema(14, "Cartões de Credito Incompletos", "Cartões de Credito Incompletos", false, false, false, false, false, null, ItemExportacaoEnum.PENDENCIA__CARTOES_PROBLEMA),
    ParcelasCanceladas(7,"Alunos com Parcelas Canceladas","Alunos com Parcelas Canceladas",false,true,true,false,false,null, ItemExportacaoEnum.BI_DCC_PARCELASCANCELADAS),
    ParcelasVencidasEmAberto(8,"Parcelas Vencidas em aberto","Parcelas Vencidas em aberto",false,false,true,false,false,null, ItemExportacaoEnum.BI_DCC_PARCELASVENCIDAS_EMABERTO),
    ParcelasEmAberto(9,"Parcelas em aberto","Parcelas em aberto",true,true,true,false,true,null, ItemExportacaoEnum.BI_DCC_PARCELAS_EMABERTO),
    OperacoesSuspeitas(11,"Operações Suspeitas","Operações Suspeitas",false,true,false,true,false,TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, ItemExportacaoEnum.BI_DCC_OPERACOESSUSPEITAS),
    AlunosAdimplentes(13,"Alunos Adimplentes","Diretrizes do indicador: Exibe número de alunos com autorização de cobrança e parcelas conforme regras abaixo.<br/><br/>\n" +
            "<b>Regras:</b><br/>\n" +
            "- A lista exibe número de parcelas pelo vencimento, independente da data de pagamento.<br/>\n" +
            "- Aluno com autorização de cobrança ativa.<br/>\n" +
            "- Aluno sem parcelas vencidas em aberto.<br/>\n" +
            "- Aluno com qualquer parcela paga e vencimento no período filtrado, com valor superior a R$ 0.<br/>\n" +
            "- Se o aluno tiver duas parcelas de qualquer origem no mesmo mês, a mesma será exibida duas vezes na lista.<br/>\n" +
            "- Recebimentos por qualquer forma de pagamento, pois o indicador filtra por autorização de cobrança e não por forma de pagamento.",true,true,false,false,false, null, ItemExportacaoEnum.BI_DCC_ALUNOSADIMPLENTES);


    private int id;
    private String descricao;
    private String hint;
    private boolean paginacao;
    private boolean apresentarVencimento;
    private boolean apresentarParcela;
    private boolean apresentarSuspeita;
    private boolean apresentarValorParcela;
    private TipoAutorizacaoCobrancaEnum tipoAutorizacao;

    private ItemExportacaoEnum itemExportacao;

    private TipoBIDCC(int id,String descricao,String hint, boolean paginacao, boolean apresentarVencimento,boolean apresentarParcela, boolean apresentarSuspeita, boolean apresentarValorParcela, TipoAutorizacaoCobrancaEnum tipoAutorizacao, ItemExportacaoEnum item ) {
        this.id = id;
        this.descricao = descricao;
        this.hint = hint;
        this.paginacao = paginacao;
        this.apresentarVencimento = apresentarVencimento;
        this.apresentarSuspeita = apresentarSuspeita;
        this.apresentarValorParcela = apresentarValorParcela;
        this.apresentarParcela = apresentarParcela;
        this.tipoAutorizacao = tipoAutorizacao;
        this.itemExportacao = item;
    }
    public static TipoBIDCC obterPorNome(String nome){
        for(TipoBIDCC obj : values()){
            if(obj.name().equals(nome)){
                return obj;
            }
        }
        return null;
    }
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public boolean isPaginacao() {
        return paginacao;
    }

    public void setPaginacao(boolean paginacao) {
        this.paginacao = paginacao;
    }

    public boolean isApresentarVencimento() {
        return apresentarVencimento;
    }

    public void setApresentarVencimento(boolean apresentarVencimento) {
        this.apresentarVencimento = apresentarVencimento;
    }

    public boolean isApresentarParcela() {
        return apresentarParcela;
    }

    public void setApresentarParcela(boolean apresentarParcela) {
        this.apresentarParcela = apresentarParcela;
    }

    public boolean isApresentarSuspeita() {
        return apresentarSuspeita;
    }

    public void setApresentarSuspeita(boolean apresentarSuspeita) {
        this.apresentarSuspeita = apresentarSuspeita;
    }

    public boolean isApresentarValorParcela() {
        return apresentarValorParcela;
    }

    public void setApresentarValorParcela(boolean apresentarValorParcela) {
        this.apresentarValorParcela = apresentarValorParcela;
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacao() {
        return tipoAutorizacao;
    }

    public static TipoBIDCC obterPorID(int id){
        for(TipoBIDCC obj : values()){
            if(obj.getId() == id){
                return obj;
            }
        }
        return null;
    }

    public ItemExportacaoEnum getItemExportacao() {
        return itemExportacao;
    }
}
