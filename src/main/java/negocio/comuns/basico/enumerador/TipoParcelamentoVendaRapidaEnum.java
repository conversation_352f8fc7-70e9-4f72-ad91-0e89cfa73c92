package negocio.comuns.basico.enumerador;

/**
 * Created by ulisses on 05/12/2022.
 */
public enum TipoParcelamentoVendaRapidaEnum {

    PARCELAMENTO_RECORRENTE(2, "Crédito recorrente"),
    PARCELAMENTO_OPERADORA(1, "Crédito parcelado");


    TipoParcelamentoVendaRapidaEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    private final int codigo;
    private final String descricao;

    public static TipoParcelamentoVendaRapidaEnum get(int codigo) {
        for (TipoParcelamentoVendaRapidaEnum obj : TipoParcelamentoVendaRapidaEnum.values()) {
            if (obj.getCodigo() == codigo) {
                return obj;
            }
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

}
