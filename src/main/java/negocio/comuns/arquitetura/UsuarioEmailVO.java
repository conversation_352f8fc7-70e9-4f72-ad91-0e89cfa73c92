package negocio.comuns.arquitetura;

/**
 * Created by <PERSON> on 27/12/2016.
 */
public class UsuarioEmailVO extends SuperVO {

    private Integer codigo;
    private Integer usuario;
    private String email;
    private boolean verificado = false;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getEmail() {
        if(email == null){
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isVerificado() {
        return verificado;
    }

    public void setVerificado(boolean verificado) {
        this.verificado = verificado;
    }
}
