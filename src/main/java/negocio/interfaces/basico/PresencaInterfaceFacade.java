
package negocio.interfaces.basico;

import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.PresencaVO;

/**
 *
 * <AUTHOR>
 */
 public interface PresencaInterfaceFacade extends SuperInterface {

    public void incluir(PresencaVO presenca) throws Exception;
    public void incluirSemCommit(PresencaVO presenca) throws Exception;
    public void alterar(PresencaVO presenca) throws Exception;
    public void alterarSemCommit(PresencaVO presenca) throws Exception;
    public void excluir(PresencaVO presenca) throws Exception;
    public void excluirSemCommit(PresencaVO presenca) throws Exception;
    
    public int contarPorPessoaPeriodoPresenca(int codigoPessoa, int codigoHorarioTurma, Date dataInicial, Date dataFinal) throws Exception;

    public PresencaVO consultarPorChavePrimaria(int codigo, int nivelMontarDados) throws Exception;    
    public PresencaVO consultarPorPessoaHorarioPeriodoPresenca(int codigoCliente, int codigoHorario, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception;

    public PresencaVO montarDadosBasicos(ResultSet resultado) throws Exception;
    public PresencaVO montarDados(ResultSet resultado, int nivelMontarDados, Connection con) throws Exception;
    public List<PresencaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception;
    public int contarPresencasAluno(final int codMatAlunoHorarioTurma, Date periodoInicial, Date periodoFinal) throws Exception;
    public List<AgendadoJSON> consultarPresencasTVGestor(Date inicio,Date fim, Integer empresa) throws Exception;
    public List<PresencaVO> listaPresenca(Integer codigo) throws Exception;

   int contarPresencasPorPeriodoModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade) throws Exception;
   boolean presencaJaMarcada(Date dataPresenca, Integer dadosTurma) throws Exception;
}
