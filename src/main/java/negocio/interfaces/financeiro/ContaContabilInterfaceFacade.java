package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ContaContabilVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.List;

/**
 * Created by ulisses on 08/02/2017.
 */
public interface ContaContabilInterfaceFacade extends SuperInterface {

    public void alterar(ContaContabilVO obj) throws Exception;

    public void excluir(ContaContabilVO obj) throws Exception;

    public void incluir(ContaContabilVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List<ContaContabilVO> consultarPorDescricao(String descricao) throws Exception;

    public List consultarTodas(int nivelDados) throws Exception;

    public ContaContabilVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao) throws SQLException;

    public ContaContabilVO consultarDescricao(String descricao) throws Exception;
}
