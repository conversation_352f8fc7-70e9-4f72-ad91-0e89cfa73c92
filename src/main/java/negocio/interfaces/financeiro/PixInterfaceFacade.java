package negocio.interfaces.financeiro;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.interfaces.basico.SuperInterface;
import servicos.pix.PixRequisicaoDto;
import servicos.pix.PixStatusEnum;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface PixInterfaceFacade extends SuperInterface {

    PixVO incluir(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO, Integer pessoa,
                  Integer empresa, List<PixMovParcelaVO> pixMovParcelasVOS, Integer formapagamento,
                  UsuarioVO usuarioResponsavel, OrigemCobrancaEnum origemCobrancaEnum, Double desconto) throws Exception;

    void incluir(PixVO pixVO) throws SQLException;

    PixVO consultarPorTxId(String txId) throws Exception;

    void alterarStatus(String txId, String status) throws SQLException;

    void alterarStatus(PixVO pixVO) throws SQLException;

    PixVO consultarPorCodigo(Integer codigo) throws Exception;

    PixVO consultarPorCodigo(Integer codigo, boolean montarParcelas) throws Exception;

    int consultarCodigoReciboPorCodigoPix(Integer codigo) throws Exception;

    Integer quantidadePorPessoa(Integer pessoa) throws SQLException;

    List<PixVO> consultarPorPessoaTelaCliente(Integer pessoa, int limit, int offset) throws Exception;

    void alterarReciboPagamento(PixVO pixVO) throws SQLException;

    void cancelarAtivosPorParcelas(List<Integer> codigosParcelas) throws Exception;

    void cancelar(PixVO pixVO) throws Exception;

    void cancelarAtivosPorParcelasVOs(List<MovParcelaVO> parcelaVOS) throws Exception;

    List<PixVO> consultarAtivosPorParcelasVOs(List<MovParcelaVO> parcelaVOS) throws Exception;

    List<PixVO> consultarAtivosPorParcelas(List<Integer> codigosParcelas) throws Exception;

    List<PixVO> consultarAtivosPorParcelasETipoConvenio(List<Integer> codigosParcelas, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    void excluirPorContrato(Integer codigo, UsuarioVO usuarioVO) throws Exception;

    void excluirPorParcela(Integer movparcela, UsuarioVO usuarioVO) throws Exception;

    List<PixVO> consultarPorPeriodo(Date filterStart, Date filterEnd) throws Exception;

    void debitarCreditosPacto(PixVO pixVO) throws Exception;

    void validarCreditosPacto(EmpresaVO empresaVO) throws Exception;

    void alterarStatusAjusteManual(PixVO pixVO, PixStatusEnum pixStatusEnum) throws Exception;

    void excluirPorReciboPagamento(ReciboPagamentoVO reciboPagamentoVO, UsuarioVO usuarioVO) throws Exception;

    List<PixVO> consultarPorReciboPagamento(Integer reciboPagamento, boolean montarPixMovParcelas) throws Exception;

    String obterCPF(PessoaVO pessoaVO) throws Exception;

    PixVO gerarObterPix(String chave, PessoaVO pessoaVO,
                        ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                        List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO,
                        Integer formaPagamento, OrigemCobrancaEnum origemCobrancaEnum, boolean cobrarMultaEJuros) throws Exception;

    PixVO gerarObterPix(String chave, PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                        List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO, Integer formaPagamento,
                        OrigemCobrancaEnum origemCobrancaEnum, Double descontoValorFixo, Double descontoPercentual,
                        Integer expiracao, boolean cobrarMultaEJuros) throws Exception;

    FormaPagamentoVO obterFormaPagamentoPix(PixVO pixVO, ConvenioCobrancaVO convenioVO);

    String obterDetalhePixPJBank(Integer boleto) throws Exception;

    List<PixVO> consultarParaProcesso(String sql) throws Exception;

    PessoaCPFTO obterDadosPessoaPagador(Integer empresa, PessoaVO pessoaVO,
                                        boolean validarNome, boolean validarCPF) throws Exception;

    boolean isPixPago(Integer codigo) throws Exception;
}
