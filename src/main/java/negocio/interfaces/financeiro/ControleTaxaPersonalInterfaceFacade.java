
package negocio.interfaces.financeiro;

import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface ControleTaxaPersonalInterfaceFacade extends SuperInterface {

    void incluir(ControleTaxaPersonalVO obj) throws Exception;

    void incluirSemCommit(ControleTaxaPersonalVO obj) throws Exception;

    void excluir(int codigo) throws Exception;

    void excluirSemCommit(int codigo) throws Exception;

    ControleTaxaPersonalVO consultarPorChavePrimaria(int codigoPrm, int nivelMontarDados) throws Exception;

    List<ControleTaxaPersonalVO> consultarPorPeriodo(int empresa, int personal, Date inicio, Date fim, int nivelMontarDados) throws Exception;

    List<ControleTaxaPersonalVO> consultarComParcelasEmAtrasoParaCancelamento(int qtdeMinimaParcelasAtrasadas) throws Exception;

    List<ControleTaxaPersonalVO> consultarPorPersonal(int personal, int nivelMontarDados) throws Exception;

    List<ControleTaxaPersonalVO> consultarPorPersonal(int personal, int nivelMontarDados, boolean incluirQtdeParcelas) throws Exception;

    List<ControleTaxaPersonalVO> consultarPorPessoaColaborador(int pessoa, int nivelMontarDados, boolean incluirQtdeParcelas) throws Exception;

    void cancelar(ControleTaxaPersonalVO controleTaxaPersonal) throws Exception;
}
