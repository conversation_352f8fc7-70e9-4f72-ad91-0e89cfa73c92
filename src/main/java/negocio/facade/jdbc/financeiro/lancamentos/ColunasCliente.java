package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 15/08/2018
 */
enum ColunasCliente implements ColunaPrefixavel {

    CODIGO("codigo"),
    PESSOA("pessoa"),
    SITUACAO("situacao"),
    MATRICULA("matricula"),
    CODIGO_MATRICULA("codigoMatricula"),
    CATEGORIA("categoria"),
    COD_ACESSO("codAcesso"),
    COD_ACESSO_ALTERNATIVO("codAcessoAlternativo"),
    BANCO("banco"),
    AGENCIA("agencia"),
    AGENCIA_DIGITO("agenciaDigito"),
    CONTA("conta"),
    EMPRESA("empresa"),
    CONTA_DIGITO("contaDigito"),
    IDENTIFICADOR_PARA_COBRANCA("identificadorParaCobranca"),
    FREE_PASS("freePass"),
    RESPONSAVEL_FREE_PASS("responsavelFreePass"),
    UA_CODIGO("uaCodigo"),
    MATRICULA_EXTERNA("matriculaexterna"),
    DATA_VALIDADE_CARTEIRINHA("dataValidadeCarteirinha"),
    PORCENTAGEM_DESCONTOBOLETO_PAG_ANTECIPADO("porcentagemDescontoBoletoPagAntecipado"),
    PESSOA_RESPONSAVEL("pessoaresponsavel"),
    PARQ_POSITIVO("parqpositivo"),
    VERIFICAR_CLIENTE("verificarCliente"),
    VERIFICADO_EM("verificadoem"),
    OBJECAO("objecao"),
    ANEXO("anexo"),
    NOME_ANEXO("nomeAnexo"),
    DATA_CADASTRO_ANEXO("dataCadastroAnexo");

    private static final String PREFIXO = "cliente";
    private String label;

    ColunasCliente(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }

}
