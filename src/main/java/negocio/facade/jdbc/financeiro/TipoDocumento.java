package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Declaracao;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.TipoDocumentoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class TipoDocumento extends SuperEntidade implements TipoDocumentoInterfaceFacade {

    public TipoDocumento() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public TipoDocumento(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }

    @Override
    public void alterar(TipoDocumentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TipoDocumentoVO.validarDados(obj);
            alterar(getIdEntidade());

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE tipodocumento ");
            sql.append("SET descricao = ? ");
            sql.append("WHERE  codigo = ?");
            PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
            int i = 1;
            sqlAlterar.setString(i++, obj.getDescricao());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(TipoDocumentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM tipodocumento WHERE codigo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluir(TipoDocumentoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            TipoDocumentoVO.validarDados(obj);
            incluir(getIdEntidade());
            StringBuilder sql = new StringBuilder();
            sql.append("insert into tipodocumento (descricao) ");
            sql.append("values (?)");
            PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
            int i = 0;
            sqlInserir.setString(++i, obj.getDescricao());
            sqlInserir.execute();
            con.commit();
            obj.setCodigo(obterValorChavePrimariaCodigo("tipodocumento"));
            obj.setNovoObj(false);
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Integer obterValorChavePrimariaCodigo(String entidade)
            throws Exception {
        this.inicializar();
        final String sqlStr = "SELECT MAX(codigo) FROM " + entidade;
        final Declaracao declara = new Declaracao(sqlStr, this.con);
        final ResultSet tabelaResultado = declara.executeQuery();
        Integer chave = null;

        if (tabelaResultado.next()) {
            chave = Integer.valueOf(tabelaResultado.getInt(1));
        }
        return chave;
    }

    @Override
    public List<TipoDocumentoVO> consultar(TipoDocumentoVO filtro) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo, descricao ");
        sql.append("FROM tipodocumento ");
        sql.append("WHERE descricao ilike('%" + filtro.getDescricao().toUpperCase() + "%') ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        return (montarDadosConsulta(tabelaResultado,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    @Override
    public List<TipoDocumentoVO> consultarTodas(int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT codigo, descricao FROM tipodocumento";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public static List<TipoDocumentoVO> montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados) throws Exception {
        List<TipoDocumentoVO> vetResultado = new ArrayList<TipoDocumentoVO>();
        while (tabelaResultado.next()) {
            TipoDocumentoVO obj = new TipoDocumentoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static TipoDocumentoVO montarDados(ResultSet dadosSQL,
            int nivelMontarDados) throws Exception {
        TipoDocumentoVO obj = new TipoDocumentoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setNovoObj(false);
        return obj;
    }

	@Override
	public TipoDocumentoVO consultarPorCodigo(Integer codigo) throws Exception {
		StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo, descricao ");
        sql.append("FROM tipodocumento ");
        sql.append("WHERE codigo = "+codigo);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        if(tabelaResultado.next()){
        	return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);	
        }
        return new TipoDocumentoVO();
	}
    private ResultSet getRS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append(" Select codigo,descricao\n");
        sql.append(" FROM TipoDocumento");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("descricao")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        ResultSet rs = getRS();
        List<TipoDocumentoVO> lista = new ArrayList<TipoDocumentoVO>();
        while (rs.next()) {

            TipoDocumentoVO tipoDocumentoVO = new TipoDocumentoVO();
            String geral = rs.getString("codigo") + rs.getString("descricao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                tipoDocumentoVO.setCodigo(rs.getInt("codigo"));
                tipoDocumentoVO.setDescricao(rs.getString("descricao"));
                lista.add(tipoDocumentoVO);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }
}
