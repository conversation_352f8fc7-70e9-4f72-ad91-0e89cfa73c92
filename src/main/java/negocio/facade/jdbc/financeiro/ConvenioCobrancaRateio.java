package negocio.facade.jdbc.financeiro;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ConvenioCobrancaRateioItemVO;
import negocio.comuns.financeiro.ConvenioCobrancaRateioVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.financeiro.ConvenioCobrancaRateioInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 15/05/2020
 */
public class ConvenioCobrancaRateio extends SuperEntidade implements ConvenioCobrancaRateioInterfaceFacade {

    public ConvenioCobrancaRateio() throws Exception {
        super();
    }

    public ConvenioCobrancaRateio(Connection con) throws Exception {
        super(con);
    }

    public void gravar(ConvenioCobrancaVO obj, UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            if (!obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                return;
            }

            Integer qtdPadrao = 0;
            for (ConvenioCobrancaRateioVO rateioVO : obj.getListaConvenioCobrancaRateioVO()) {
                if (rateioVO.isPadrao()) {
                   qtdPadrao++;
                }
            }
            if (qtdPadrao > 1) {
                throw new Exception("Existe mais de um rateio definido como padrão.");
            }


            List<ConvenioCobrancaRateioVO> listaAnterior = consultarPorConvenioCobranca(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Set<Integer> atuais = new HashSet<>();
            for (ConvenioCobrancaRateioVO rateioVO : obj.getListaConvenioCobrancaRateioVO()) {
                rateioVO.setConvenioCobrancaVO(obj);
                if (UteisValidacao.emptyNumber(rateioVO.getCodigo())) {
                    incluir(rateioVO);
                } else {
                    alterar(rateioVO);
                }
                atuais.add(rateioVO.getCodigo());
            }
            excluirItensRemovidos(listaAnterior, atuais);

            gravarLog(obj, listaAnterior, usuarioVO);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void gravarLog(ConvenioCobrancaVO obj, List<ConvenioCobrancaRateioVO> listaAnterior, UsuarioVO usuarioVO) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("CONVENIOCOBRANCA");
            log.setNomeEntidadeDescricao("CONVENIOCOBRANCA");
            log.setDescricao("CONVENIOCOBRANCA-RATEIO");
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("CONVENIOCOBRANCA-RATEIO");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setValorCampoAnterior("");

            JSONObject json = new JSONObject();
            json.put("anterior", obterArrayLog(listaAnterior));
            json.put("atual", obterArrayLog(obj.getListaConvenioCobrancaRateioVO()));
            log.setValorCampoAlterado(json.toString());

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private JSONArray obterArrayLog(List<ConvenioCobrancaRateioVO> lista) {
        JSONArray array = new JSONArray();
        for (ConvenioCobrancaRateioVO rateioVO : lista) {
            JSONObject rateio = new JSONObject();
            rateio.put("codigo", rateioVO.getCodigo());
            rateio.put("descricao", rateioVO.getDescricao());
            rateio.put("plano", rateioVO.getPlanoVO().getCodigo());
            rateio.put("produto", rateioVO.getProdutoVO().getCodigo());
            rateio.put("tipoProduto", rateioVO.getTipoProduto());
            rateio.put("empresa", rateioVO.getEmpresaVO().getCodigo());
            rateio.put("padrao", rateioVO.isPadrao());

            JSONArray itens = new JSONArray();
            for (ConvenioCobrancaRateioItemVO itemVO : rateioVO.getItens()) {
                JSONObject item = new JSONObject();
                item.put("codigo", itemVO.getCodigo());
                item.put("idRecebedor", itemVO.getIdRecebedor());
                item.put("nomeRecebedor", itemVO.getNomeRecebedor());
                item.put("porcentagem", itemVO.getPorcentagem());
                itens.put(item);
            }
            rateio.put("itens", itens);

            array.put(rateio);
        }
        return array;
    }

    private void excluirItensRemovidos(List<ConvenioCobrancaRateioVO> listaAnterior, Set<Integer> atuais) throws Exception {
        for (ConvenioCobrancaRateioVO cobrancaRateioVO : listaAnterior) {
            if (!atuais.contains(cobrancaRateioVO.getCodigo())) {
                ConvenioCobrancaRateioItem itemDAO = new ConvenioCobrancaRateioItem(con);
                itemDAO.excluirPorRateio(cobrancaRateioVO);
                itemDAO = null;
                excluir(cobrancaRateioVO);
            }
        }
    }

    @Override
    public void incluir(ConvenioCobrancaRateioVO obj) throws Exception {
        String insert = "INSERT INTO conveniocobrancarateio(dataregistro, dataalteracao, descricao, conveniocobranca, empresa, produto, plano, tipoProduto, padrao) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement ps = con.prepareStatement(insert)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setString(++i, obj.getDescricao());
            resolveFKNull(ps, ++i, obj.getConvenioCobrancaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getProdutoVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getPlanoVO().getCodigo());
            ps.setString(++i, obj.getTipoProduto());
            ps.setBoolean(++i, obj.isPadrao());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        gravarItens(obj);
    }

    @Override
    public void alterar(ConvenioCobrancaRateioVO obj) throws Exception {
        String update = "UPDATE conveniocobrancarateio SET dataalteracao=?, descricao=?, conveniocobranca=?, empresa=?, produto=?, plano=?, tipoProduto=?, padrao=? WHERE codigo = ?;";
        try (PreparedStatement ps = con.prepareStatement(update)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setString(++i, obj.getDescricao());
            resolveFKNull(ps, ++i, obj.getConvenioCobrancaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getProdutoVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getPlanoVO().getCodigo());
            ps.setString(++i, obj.getTipoProduto());
            ps.setBoolean(++i, obj.isPadrao());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
        gravarItens(obj);
    }

    private void gravarItens(ConvenioCobrancaRateioVO obj) throws Exception {
        ConvenioCobrancaRateioItem dao = new ConvenioCobrancaRateioItem(con);
        dao.gravar(obj);
        dao = null;
    }


    @Override
    public void excluir(ConvenioCobrancaRateioVO obj) throws Exception {
        String sql = "DELETE FROM conveniocobrancarateio WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, obj.getCodigo());
            ps.execute();
        }
    }

    public List<ConvenioCobrancaRateioVO> consultarPorConvenioCobranca(Integer convenioCobranca, int nivelMontarDados) throws Exception {
        if (UteisValidacao.emptyNumber(convenioCobranca)) {
            return new ArrayList<>();
        }

        String sql = "select * from ConvenioCobrancaRateio where conveniocobranca = " + convenioCobranca + " order by codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                return montarDadosConulta(rs, nivelMontarDados, this.con);
            }
        }
    }

    private List<ConvenioCobrancaRateioVO> montarDadosConulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<ConvenioCobrancaRateioVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivelMontarDados, con));
        }
        return lista;
    }

    private ConvenioCobrancaRateioVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        ConvenioCobrancaRateioVO obj = new ConvenioCobrancaRateioVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataregistro"));
        obj.setDataAlteracao(rs.getTimestamp("dataalteracao"));
        obj.setDescricao(rs.getString("descricao"));
        obj.getConvenioCobrancaVO().setCodigo(rs.getInt("convenioCobranca"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.getProdutoVO().setCodigo(rs.getInt("produto"));
        obj.getPlanoVO().setCodigo(rs.getInt("plano"));
        obj.setTipoProduto(rs.getString("tipoProduto"));
        obj.setPadrao(rs.getBoolean("padrao"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosItens(obj, con);
            montarDadosPlano(obj, con);
            montarDadosProduto(obj, con);
        }
        return obj;
    }

    private void montarDadosItens(ConvenioCobrancaRateioVO obj, Connection con) throws Exception {
        ConvenioCobrancaRateioItem itemDAO = new ConvenioCobrancaRateioItem(con);
        obj.setItens(itemDAO.consultarPorConvenioCobrancaRateio(obj.getCodigo()));
        itemDAO = null;
    }

    private void montarDadosPlano(ConvenioCobrancaRateioVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
            Plano dao = new Plano(con);
            obj.setPlanoVO(dao.consultarPorChavePrimaria(obj.getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            dao = null;
        }
    }

    private void montarDadosProduto(ConvenioCobrancaRateioVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
            Produto dao = new Produto(con);
            obj.setProdutoVO(dao.consultarPorChavePrimaria(obj.getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            dao = null;
        }
    }
}
