package negocio.facade.jdbc.contrato;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.OperacaoColetivaVO;
import negocio.comuns.contrato.StatusOperacaoColetivaEnum;
import negocio.comuns.contrato.TipoOperacaoColetivaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.OperacaoColetivaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OperacaoColetiva extends SuperEntidade implements OperacaoColetivaInterfaceFacade {

    public OperacaoColetiva() throws Exception {
        super();
    }

    public OperacaoColetiva(Connection con) throws Exception {
        super(con);
    }

    public static OperacaoColetivaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        OperacaoColetivaVO obj = new OperacaoColetivaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getPlanoVO().setCodigo(dadosSQL.getInt("plano"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setStatus(StatusOperacaoColetivaEnum.getStatusOperacaoColetiva(dadosSQL.getInt("status")));
        obj.setTipo(TipoOperacaoColetivaEnum.getTipoOperacaoColetiva(dadosSQL.getInt("tipo")));
        obj.setDataInicio(dadosSQL.getDate("datainicio"));
        obj.setDataFinal(dadosSQL.getDate("datafim"));
        obj.setDataCadastro(dadosSQL.getTimestamp("datacadastro"));
        obj.setDataprocessamento(dadosSQL.getTimestamp("dataprocessamento"));
        obj.setObservacao(dadosSQL.getString("observacao"));
        obj.setResultado(dadosSQL.getString("resultado"));
        obj.setUsuario(dadosSQL.getString("usuario"));
        obj.getModalidadeVO().setCodigo(dadosSQL.getInt("modalidade"));
        obj.getTurmaVO().setCodigo(dadosSQL.getInt("turma"));
        obj.setIdadeMinima(dadosSQL.getInt("idademinima"));
        obj.setIdadeMaxima(dadosSQL.getInt("idademaxima"));
        obj.setMensagemCatraca(dadosSQL.getString("mensagemCatraca"));
        obj.setBloquearAcessoAniversario(dadosSQL.getBoolean("bloquearAcessoAniversario"));
        obj.setCancelarParcelasEmAbertoAlunosDesmarcados(dadosSQL.getBoolean("cancelarparcelasemaberto"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosEmpresa(obj, nivelMontarDados, con);
        montarDadosPlano(obj, nivelMontarDados, con);
        montarDadosModalidade(obj,con);
        montarDadosTurma(   obj,con);

        return obj;
    }

    private static void montarDadosEmpresa(OperacaoColetivaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.notEmptyNumber(obj.getEmpresa().getCodigo())) {
            Empresa empresaDAO = new Empresa(con);
            obj.setEmpresa(empresaDAO.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            empresaDAO = null;
        }
    }

    private static void montarDadosPlano(OperacaoColetivaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.notEmptyNumber(obj.getPlanoVO().getCodigo())) {
            Plano planoDAO = new Plano(con);
            obj.setPlanoVO(planoDAO.consultarPorChavePrimaria(obj.getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            planoDAO = null;
        }
    }
    private static void montarDadosModalidade(OperacaoColetivaVO obj, Connection con) throws Exception {
        if (UteisValidacao.notEmptyNumber(obj.getModalidadeVO().getCodigo())) {
            Modalidade modalidadeDAO = new Modalidade(con);
            obj.setModalidadeVO(modalidadeDAO.consultarPorChavePrimaria(obj.getModalidadeVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            modalidadeDAO = null;
        }
    }

    private static void montarDadosTurma(OperacaoColetivaVO obj,  Connection con) throws Exception {
        if (UteisValidacao.notEmptyNumber(obj.getTurmaVO().getCodigo())) {
            Turma turmaDAO = new Turma(con);
            obj.setTurmaVO(turmaDAO.consultarPorChavePrimaria(obj.getTurmaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            turmaDAO = null;
        }
    }

    public void incluir(OperacaoColetivaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void incluirSemCommit(OperacaoColetivaVO obj) throws Exception {
        obj.validarDados();
        String sql = "INSERT INTO operacaocoletiva(\n" +
                "             tipo, status, empresa, plano, observacao, \n" +
                "            resultado, usuario,  datainicio, datafim, datacadastro, dataprocessamento, \n" +
                "            modalidade, turma, idademinima, idademaxima, mensagemCatraca, bloquearAcessoAniversario," +
                "            cancelarparcelasemaberto)\n" +
                "    VALUES (?, ?, ?, ?, ?, ?, \n" +
                "            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?);";
        int i = 1;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(i++, obj.getTipo().getCodigo());
            sqlInserir.setInt(i++, obj.getStatus().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getEmpresa().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getPlanoVO().getCodigo());
            sqlInserir.setString(i++, obj.getObservacao());
            sqlInserir.setString(i++, obj.getResultado());
            sqlInserir.setString(i++, obj.getUsuario());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataFinal()));
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataprocessamento()));
            resolveIntegerNull(sqlInserir, i++, obj.getModalidadeVO().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getTurmaVO().getCodigo());
            resolveIntegerNull(sqlInserir, i++, obj.getIdadeMinima());
            resolveIntegerNull(sqlInserir, i++, obj.getIdadeMaxima());
            sqlInserir.setString(i++, obj.getMensagemCatraca());
            sqlInserir.setBoolean(i++, obj.getBloquearAcessoAniversario());
            sqlInserir.setBoolean(i++, obj.isCancelarParcelasEmAbertoAlunosDesmarcados());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

    }

    public void alterar(OperacaoColetivaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            altetarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void altetarSemCommit(OperacaoColetivaVO obj) throws Exception {
        String sql = "UPDATE operacaocoletiva\n" +
                "        SET  tipo=?, status=?, empresa=?, plano=?, \n" +
                "        observacao=?, resultado=?, usuario=?, datainicio=?, datafim=?, \n" +
                "        dataprocessamento=?,modalidade=?,turma=? ,idademinima=?, idademaxima=?, mensagemCatraca=?, bloquearAcessoAniversario=?," +
                "        cancelarparcelasemaberto=? \n" +
                "        WHERE codigo = ?;";
        int i = 1;
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(i++, obj.getTipo().getCodigo());
            sqlAlterar.setInt(i++, obj.getStatus().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getEmpresa().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getPlanoVO().getCodigo());
            sqlAlterar.setString(i++, obj.getObservacao());
            sqlAlterar.setString(i++, obj.getResultado());
            sqlAlterar.setString(i++, obj.getUsuario());
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataFinal()));
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataprocessamento()));
            resolveIntegerNull(sqlAlterar, i++, obj.getModalidadeVO().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getTurmaVO().getCodigo());
            resolveIntegerNull(sqlAlterar, i++, obj.getIdadeMinima());
            resolveIntegerNull(sqlAlterar, i++, obj.getIdadeMaxima());
            sqlAlterar.setString(i++, obj.getMensagemCatraca());
            sqlAlterar.setBoolean(i++, obj.getBloquearAcessoAniversario());
            sqlAlterar.setBoolean(i++, obj.isCancelarParcelasEmAbertoAlunosDesmarcados());

            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
        }

    }

    public List<OperacaoColetivaVO> consultar(Integer empresa, Date datainicio, Boolean processado, int nivelMontarDados) throws Exception {

        String sqlStr = "SELECT * FROM operacaocoletiva ";
        String where = "";
        if (UteisValidacao.notEmptyNumber(empresa)) {
            where = " empresa = " + empresa;
        }
        if (datainicio != null) {
            if (!where.equals("")) {
                where += " and ";
            }
            where += " datainicio <= '" + Uteis.getDataFormatoBD(datainicio) + "'";
        }

        if (processado != null) {
            if (!where.equals("")) {
                where += " and ";
            }
            if (processado) {
                where += " status in (" + StatusOperacaoColetivaEnum.PROCESSADA.getCodigo() + ") ";
            } else {
                where += " status in (" + StatusOperacaoColetivaEnum.AGUARDANDO.getCodigo()+"," + StatusOperacaoColetivaEnum.ERRO.getCodigo()+", " + StatusOperacaoColetivaEnum.PROCESSANDO.getCodigo() + ", " +StatusOperacaoColetivaEnum.AGUARDANDO_EXCLUSAO.getCodigo()+") ";
            }
        }

        if (!where.equals("")) {
            sqlStr += " where " + where;
        }
        sqlStr += "order by dataCadastro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }

    }

    private List<OperacaoColetivaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<OperacaoColetivaVO> vetResultado = new ArrayList<OperacaoColetivaVO>();
        while (tabelaResultado.next()) {
            OperacaoColetivaVO obj = new OperacaoColetivaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;

    }

    public void excluir(OperacaoColetivaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM operacaocoletiva WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public String existeOperacaoConflitante(OperacaoColetivaVO operacaoColetivaVO) throws Exception {

        String sqlStr = "SELECT * FROM operacaocoletiva where empresa = " + operacaoColetivaVO.getEmpresa().getCodigo();
        sqlStr += " and status <> " + StatusOperacaoColetivaEnum.EXCLUIDA.getCodigo();
        sqlStr += " and (datainicio between '" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataInicio()) + "' and '" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataFinal()) + "' ";
        sqlStr += " or datafim between '" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataInicio()) + "' and '" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataFinal()) + "' ";
        sqlStr += " or '" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataInicio()) + "' between datainicio and datafim ";
        sqlStr += " or '" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataFinal()) + "' between datainicio and datafim )";
        if (UteisValidacao.notEmptyNumber(operacaoColetivaVO.getPlanoVO().getCodigo())) {
            sqlStr += " and (plano is null or plano = " + operacaoColetivaVO.getPlanoVO().getCodigo() + ") ";
        }
        if(operacaoColetivaVO.getIdadeMinima() != null && UteisValidacao.notEmptyNumber(operacaoColetivaVO.getIdadeMaxima())){
            sqlStr += " AND ((coalesce(idademinima,0)  = 0 and coalesce(idademaxima,0)  = 0) or ( idademinima between "+operacaoColetivaVO.getIdadeMinima()+" and "+operacaoColetivaVO.getIdadeMaxima()+")";
            sqlStr += " or ( idademaxima between "+operacaoColetivaVO.getIdadeMinima()+" and "+operacaoColetivaVO.getIdadeMaxima()+"))";
        }

        if (UteisValidacao.notEmptyNumber(operacaoColetivaVO.getTurmaVO().getCodigo())) {
            sqlStr += " and turma = " + operacaoColetivaVO.getTurmaVO().getCodigo();
        }else {
            sqlStr += " and tipo <> " + TipoOperacaoColetivaEnum.DESMARCAR_AULAS_ALUNOS_PERIODO.getCodigo();
        }
        sqlStr += " order by dataCadastro desc";
        List<OperacaoColetivaVO> listaOperacoes;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                listaOperacoes = montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, this.con);
            }
        }
        if (listaOperacoes.isEmpty()) {
            return "";
        }
        int i = 1;
        StringBuilder detalhesConflito = new StringBuilder("Operação(ões) conflitante(s): ");
        for (OperacaoColetivaVO operacao : listaOperacoes) {
            detalhesConflito.append(i++).append("- Data inicio: ").append(operacao.getDataInicio_Apresentar()).append(", plano:").append(UteisValidacao.notEmptyNumber(operacao.getPlanoVO().getCodigo()) ? operacao.getPlanoVO().getDescricao() : "todos").append(", data cadastro: ").append(operacao.getDataCadastro_Apresentar()).append(" ");
        }
        return detalhesConflito.toString();
    }


    private PreparedStatement getPS() throws SQLException {
        String sql = "SELECT oc.codigo, emp.nome as empresa, datacadastro, tipo, status FROM operacaocoletiva oc\n" +
                "INNER JOIN empresa emp ON oc.empresa = emp.codigo";
        return con.prepareStatement(sql);
    }

    public String consultarJSON() throws Exception {
        JSONObject aaData;
        JSONArray valores;
        try (ResultSet rs = getPS().executeQuery()) {
            aaData = new JSONObject();
            valores = new JSONArray();
            while (rs.next()) {
                JSONArray itemArray = new JSONArray();

                itemArray.put(rs.getInt("codigo"));
                itemArray.put(rs.getString("empresa"));
                itemArray.put(Uteis.getDataComHHMM(rs.getTimestamp("datacadastro")));
                itemArray.put(TipoOperacaoColetivaEnum.getDescricao(rs.getInt("tipo")));
                itemArray.put(rs.getString("status"));
                valores.put(itemArray);
            }
        }

        aaData.put("aaData", valores);

        return aaData.toString();
    }


    public List<OperacaoColetivaVO> consultar(String sql,  int nivelMontarDados) throws Exception {
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }

    }

    public OperacaoColetivaVO consultarPorChavePrimaria(Integer codigo, Integer nivelMontarDados) throws Exception {

        String sqlStr = "SELECT * FROM operacaocoletiva where  codigo =  "+codigo;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
                return new OperacaoColetivaVO();
            }
        }
    }

}
