package servicos.vendasonline.dto;

import negocio.comuns.basico.ClienteVO;

/**
 * Created by ulisses on 26/01/2023.
 */
public class VendasOnlineIcvDTO {

    private Integer codigoVendasOnlineCampanhaIcv;
    private Integer contrato;
    private Integer vendaAvulsa;
    private VendaDTO vendaDTO;
    private ClienteVO clienteVO;

    public Integer getCodigoVendasOnlineCampanhaIcv() {
        return codigoVendasOnlineCampanhaIcv;
    }

    public void setCodigoVendasOnlineCampanhaIcv(Integer codigoVendasOnlineCampanhaIcv) {
        this.codigoVendasOnlineCampanhaIcv = codigoVendasOnlineCampanhaIcv;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public VendaDTO getVendaDTO() {
        return vendaDTO;
    }

    public void setVendaDTO(VendaDTO vendaDTO) {
        this.vendaDTO = vendaDTO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }
}
