package servicos.impl.login;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenSolicitacaoDTO {

    private String usuarioGeral;
    private Integer usuariozw;
    private String usernamezw;
    private String ip;

    private String responsavel; //usuario que soliciou originou
    private String chave; //chave de onde originou a solicitação
    private Integer empresa; //empresa de onde originou a solicitação

    private String email; //novo email
    private String telefone; //novo telefone

    private Boolean codigoViaEmail;
    private Boolean enviarLink;
    private Boolean novoLogin;
    private Boolean trocaEmailVincularDados;

    public TokenSolicitacaoDTO() {

    }

    public TokenSolicitacaoDTO(UsuarioVO usuarioVO, String chave, Integer empresa,
                               UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin) {
        this.usuarioGeral = UteisValidacao.emptyString(usuarioVO.getUsuarioGeral()) ? null : usuarioVO.getUsuarioGeral();
        this.usuariozw = usuarioVO.getCodigo();
        this.usernamezw = usuarioVO.getUsername();
        this.chave = chave;
        this.empresa = empresa;
        this.ip = ipCliente;

        //defini para qual link será direcionado
        this.novoLogin = novoLogin;

        JSONObject json = new JSONObject();
        json.put("origem", "ZillyonWeb");
        json.put("chave", chave);
        json.put("empresa", empresa);
        if (usuarioResponsavelVO != null) {
            json.put("username", usuarioResponsavelVO.getUsername());
            json.put("usuario_zw", usuarioResponsavelVO.getCodigo());
            json.put("usuario_oamd", usuarioResponsavelVO.getUserOamd());
        }
        this.responsavel = json.toString();
    }

    public TokenSolicitacaoDTO(UsuarioVO usuarioVO, String chave, Integer empresa,
                               UsuarioVO usuarioResponsavelVO, String ipCliente, boolean novoLogin, Boolean trocaEmailVincularDados) {
        this(usuarioVO, chave, empresa, usuarioResponsavelVO, ipCliente, novoLogin);
        this.trocaEmailVincularDados = trocaEmailVincularDados;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getUsuarioGeral() {
        return usuarioGeral;
    }

    public void setUsuarioGeral(String usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public Boolean getCodigoViaEmail() {
        return codigoViaEmail;
    }

    public void setCodigoViaEmail(Boolean codigoViaEmail) {
        this.codigoViaEmail = codigoViaEmail;
    }

    public Boolean getEnviarLink() {
        return enviarLink;
    }

    public void setEnviarLink(Boolean enviarLink) {
        this.enviarLink = enviarLink;
    }

    public Boolean getNovoLogin() {
        return novoLogin;
    }

    public void setNovoLogin(Boolean novoLogin) {
        this.novoLogin = novoLogin;
    }

    public Integer getUsuariozw() {
        return usuariozw;
    }

    public void setUsuariozw(Integer usuariozw) {
        this.usuariozw = usuariozw;
    }

    public String getUsernamezw() {
        return usernamezw;
    }

    public void setUsernamezw(String usernamezw) {
        this.usernamezw = usernamezw;
    }

    public Boolean getTrocaEmailVincularDados() {
        return trocaEmailVincularDados;
    }

    public void setTrocaEmailVincularDados(Boolean trocaEmailVincularDados) {
        this.trocaEmailVincularDados = trocaEmailVincularDados;
    }
}
