/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bb;

import negocio.comuns.financeiro.RemessaVO;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dco.febraban.LayoutRemessaFebrabanDCO;

import java.util.List;

/**
 * <AUTHOR>
 */
public class LayoutRemessaBBDCO extends LayoutRemessaFebrabanDCO {

    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();

        //HEADER
        sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuffer sbDetail = new StringBuffer();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer()).append("\r\n");
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer());
        sb.append("\r\n");

        return sb;
    }

}
