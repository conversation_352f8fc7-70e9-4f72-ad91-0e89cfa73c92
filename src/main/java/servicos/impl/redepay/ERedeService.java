package servicos.impl.redepay;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoERedeVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.ERedeServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/*
 * Created by Luiz Felipe on 20/09/2017.
 */
public class ERedeService extends AbstractCobrancaOnlineServiceComum implements ERedeServiceInterface {

    private String urlApiRede = "";
    private String numeroFiliacao;
    private String token;

    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioERede;

    public ERedeService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioERede = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioERede != null) {
            this.numeroFiliacao = this.convenioERede.getCodigoAutenticacao01();
            this.token = this.convenioERede.getCodigoAutenticacao02();

            if (this.convenioERede.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlApiRede = PropsService.getPropertyValue(PropsService.urlApiRedeProducao);
            } else {
                this.urlApiRede = PropsService.getPropertyValue(PropsService.urlApiRedeSandbox);
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoERedeVO(), TipoTransacaoEnum.E_REDE, this.convenioERede);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            JSONObject parametrosPagamento = criarParametrosPagamento(transacao, dadosCartao);
            transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
            transacaoDAO.alterar(transacao);

            validarDadosTransacao(transacao, dadosCartao);
            validarDadosTransacaoPelosCriteriosDaAdquirente(dadosCartao);

            String retorno = executarRequestRede(parametrosPagamento.toString(), "/transactions", ExecuteRequestHttpService.METODO_POST);
            processarRetorno(transacao, retorno);

            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                    consultarPeloCodigoReferencia(transacao);
                }
                consultarSituacaoTransacao(transacao);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private void validarDadosTransacaoPelosCriteriosDaAdquirente(CartaoCreditoTO dadosCartao) throws Exception {
        //Transação de Zero Dollar deve ter CVV
        if (dadosCartao.isTransacaoPresencial() && UteisValidacao.emptyString(dadosCartao.getCodigoSeguranca())) {
            throw new Exception("CVV obrigatório para Transação de Verificação.");
        }
    }

    private JSONObject criarParametrosPagamento(TransacaoVO transacaoVO, CartaoCreditoTO dadosCartao) {

        String identificador = "TRA" + transacaoVO.getCodigo();
        Uteis.logarDebug("IDENTIFICADOR E-REDE: " + identificador);

        JSONObject pagamento = new JSONObject();
        pagamento.put("capture", true);
        pagamento.put("reference", identificador);

        //débito cartão elo virtual caixa
        if (dadosCartao.getNumero().startsWith("5067228")) {
            pagamento.put("kind", "debit");
        } else {
            pagamento.put("kind", "credit");
        }

        if (dadosCartao.isTransacaoVerificarCartao()) {
            pagamento.put("amount", 0);
        } else {
            pagamento.put("amount", StringUtilities.formatarCampoMonetario(dadosCartao.getValor(), 10));
        }

        if (!dadosCartao.isTransacaoVerificarCartao()) {
            pagamento.put("installments", dadosCartao.getParcelas());
        }

        if (!UteisValidacao.emptyString(dadosCartao.getNomeTitular().trim())) {
            pagamento.put("cardHolderName", Uteis.retirarAcentuacao(dadosCartao.getNomeTitular()));
        }

        String numeroCartao = "";
        if (!UteisValidacao.emptyString(dadosCartao.getNumero())) {
            numeroCartao = dadosCartao.getNumero();
        }

        pagamento.put("cardNumber", numeroCartao);
        pagamento.put("expirationMonth", dadosCartao.getMesValidade());
        pagamento.put("expirationYear", dadosCartao.getAnoValidade());

        //transação presencial enviar o cvv
        if (dadosCartao.isTransacaoPresencial() && !UteisValidacao.emptyString(dadosCartao.getCodigoSeguranca())) {
            pagamento.put("securityCode", dadosCartao.getCodigoSeguranca());
        }

        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(numeroCartao, dadosCartao, transacaoVO);

        //Comentado para analise no futuro se retorna ou não, pois na documentação esse campo deve ser enviado com o brandTid, que é o código da primeira transação da Recorrência e parcelas fixas.
        //Como enviamos valores diferentes de Produto, Multa e Juros, Taxas e etc. optei pelo Card-on-File.
        //Se o Card-on-File não performar bem, estudamos a possibilidade de retornar a Recorrência ou um hibrido do Recorrência com Card-on-File.
        //Dúvidas, ler o ticket ou me perguntar
        //Documentação do desenvolvedor: https://developer.userede.com.br/e-rede => Transações Autorização
        //Documentação do desenvolvedor: https://developer.userede.com.br/e-rede#documentacao-recorrencia-categorizacao-transacoes-card-on-file => Dados para Card-on-File ou Recorrência deve adicioar Autorização acima.
//        //transação recorrente
//        if (!dadosCartao.isTransacaoPresencial()) {
//            pagamento.put("subscription", true);
//        }

        if (!dadosCartao.isTransacaoVerificarCartao()) {
            pagamento.put("origin", 1);
            pagamento.put("distributorAffiliation", numeroFiliacao);

            if (dadosCartao.getOrigemCobranca().equals(OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO)) {
                pagamento.put("storageCard", "0"); //Cobranças que Não vai salvar o cartão para futuras compras, devem entrar aqui
            } else if (dadosCartao.getOrigemCobranca().equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) ||
                       dadosCartao.getOrigemCobranca().equals(OrigemCobrancaEnum.VENDAS_ONLINE_VENDA) ||
                       dadosCartao.getOrigemCobranca().equals(OrigemCobrancaEnum.TOTEM)) {
                pagamento.put("storageCard", "1"); //Cobranças que aprovadas Vai salvar o cartão para futuras compras, deve entrar aqui
            } else {
                pagamento.put("storageCard", "2"); //Cobranças que vão usar os Dados já salvos na autorização de cobrança, devem entrar aqui
            }

            if (dadosCartao.getBand().equals(OperadorasExternasAprovaFacilEnum.MASTERCARD)) {
                JSONObject transactionCredentials = new JSONObject();
                //Como enviamos valores diferentes de Produto, Multa e Juros, Taxas e etc. optei pelo Card-on-File - credentialId = 06.
                //Se o Card-on-File não performar bem, estudamos a possibilidade de retornar a Recorrência ou um hibrido do Recorrência com Card-on-File.
                //Para consultar outros possíveis valores, ver documentação: https://developer.userede.com.br/e-rede#documentacao-recorrencia-categorizacao-transacoes-card-on-file
                transactionCredentials.put("credentialId", "06"); //Ordem Permanente - Usar os dados da conta do titular do cartão para uma transação que deve ocorrer em intervalos regulares por um valor variável. - Pagamentos mensais de serviços
                pagamento.put("transactionCredentials", transactionCredentials);
            }
        }
        return pagamento;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) throws Exception {
        JSONObject payment = new JSONObject(parametrosPagamento.toString());
        payment.put("cardNumber", APF.getCartaoMascarado(payment.getString("cardNumber")));
        try {
            if (payment.has("securityCode")) {
                payment.put("securityCode", "***");
            }
        } catch (Exception ignored) {
        }
        return payment.toString();
    }

    @Override
    public String consultarTransacao(TransacaoVO transacaoVO) throws Exception {
        return executarRequestRede(null, "/transactions/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        transacaoOriginal.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoNova, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        transacaoNova.setDataProcessamento(Calendario.hoje());
        new Transacao(getCon()).incluir(transacaoNova);
        JSONObject parametrosEnviar = decifrarDadosSigilososReEnvio(transacaoNova);
        String retorno = executarRequestRede(parametrosEnviar.toString(), "/transactions", ExecuteRequestHttpService.METODO_POST);
        processarRetorno(transacaoNova, retorno);
        if (transacaoNova.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            if (UteisValidacao.emptyString(transacaoNova.getCodigoExterno())) {
                consultarPeloCodigoReferencia(transacaoNova);
            }
            consultarSituacaoTransacao(transacaoNova);
        }
        new Transacao(getCon()).alterar(transacaoNova);
        return transacaoNova;
    }

    private JSONObject decifrarDadosSigilososReEnvio(TransacaoVO transacaoVO) throws Exception {
        if (UteisValidacao.emptyString(transacaoVO.getTokenAragorn())) {
            throw new Exception("Não foi possível obter o token do cartão");
        } else {
            NazgDTO nazgDTO = obterNazgTO(transacaoVO.getTokenAragorn());
            JSONObject parametrosEnvio = new JSONObject(transacaoVO.getParamsEnvio());
            parametrosEnvio.put("cardNumber", nazgDTO.getCard());
//            if (parametrosEnvio.has("securityCode")) {
//                parametrosEnvio.put("securityCode", nazgDTO.getCvv());
//            }
            return parametrosEnvio;
        }
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        //Transação de Verificação não aceita cancelamento.
        if (!transacao.isTransacaoVerificarCartao()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("amount", StringUtilities.formatarCampoMonetario(transacao.getValor(), 10));
            String retorno = executarRequestRede(jsonObject.toString(), "/transactions/" + transacao.getCodigoExterno() + "/refunds", ExecuteRequestHttpService.METODO_POST);
            processarRetornoCancelamento(transacao, retorno);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && (estornarRecibo && transacao.getReciboPagamento() != 0)) {
                estornarRecibo(transacao, estornarRecibo);
                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            }
            new Transacao(getCon()).alterar(transacao);
        }
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, String retorno) throws Exception {
        try {
            JSONObject retornoCancelamento = new JSONObject(retorno);
            String returnCode = retornoCancelamento.getString("returnCode");

            if (returnCode.equals(ERedeRetornoEnum.Retorno355.getCodigo()) ||
                    returnCode.equals(ERedeRetornoEnum.Retorno359.getCodigo()) ||
                    returnCode.equals(ERedeRetornoEnum.Retorno360.getCodigo())) {
                transacao.setResultadoCancelamento(retorno);
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacao.setDataHoraCancelamento(Calendario.hoje());
            } else {
                transacao.setResultadoCancelamento(retorno);
            }
        } catch (Exception e) {
            consultarSituacaoTransacao(transacao);
        }
    }

    public void consultarSituacaoTransacao(TransacaoVO transacao) throws Exception {
        String retorno = executarRequestRede(null, "/transactions/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
        incluirHistoricoRetornoTransacao(transacao, retorno, "consultarSituacaoTransacao");
        processarRetornoConsultaTransacao(transacao, retorno);
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoConsultaTransacao(TransacaoVO transacao, String retorno) {
        try {
            String returnCode = "";
            boolean cancelada = false;
            boolean naoAutorizada = false;

            JSONObject jsonRetorno = new JSONObject(retorno);
            if (jsonRetorno.has("returnCode")) {
                returnCode = jsonRetorno.optString("returnCode");
                cancelada = jsonRetorno.optString("status").equalsIgnoreCase("Canceled");
                naoAutorizada = jsonRetorno.optString("status").equalsIgnoreCase("Denied");
            } else if (jsonRetorno.has("authorization")) {
                JSONObject jsonAuthorization = jsonRetorno.getJSONObject("authorization");
                returnCode = jsonAuthorization.optString("returnCode");
                cancelada = jsonAuthorization.optString("status").equalsIgnoreCase("Canceled");
                naoAutorizada = jsonRetorno.optString("status").equalsIgnoreCase("Denied");
            }

            if (cancelada) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (returnCode.equals(ERedeRetornoEnum.Retorno00.getCodigo())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                transacao.setPermiteRepescagem(false);
            } else if (returnCode.equals(ERedeRetornoEnum.Retorno360.getCodigo())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (naoAutorizada) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void consultarPeloCodigoReferencia(TransacaoVO transacaoVO) {
        try {
            String identificador = ("TRA" + transacaoVO.getCodigo());
            if (!UteisValidacao.emptyString(transacaoVO.getParamsEnvio())) {
                try {
                    JSONObject envio = new JSONObject(transacaoVO.getParamsEnvio());
                    identificador = envio.getString("reference");
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyString(identificador)) {
                throw new Exception("Código referencia não identificado");
            }

            String retorno = executarRequestRede(null, "/transactions?reference=" + identificador, ExecuteRequestHttpService.METODO_GET);
            incluirHistoricoRetornoTransacao(transacaoVO, retorno, "consultarPeloCodigoReferencia");
            JSONObject jsonRetorno = new JSONObject(retorno);
            JSONObject jsonAuthorization = jsonRetorno.getJSONObject("authorization");
            transacaoVO.setCodigoExterno(jsonAuthorization.getString("tid"));
            new Transacao(getCon()).alterar(transacaoVO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        transacao.setParamsResposta(retorno);
        try {
            JSONObject retornoJSON = new JSONObject(retorno);

            String tid = retornoJSON.getString("tid");
            String returnCode = retornoJSON.getString("returnCode");

            transacao.setCodigoExterno(tid);

            if (returnCode.equals(ERedeRetornoEnum.Retorno00.getCodigo())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                transacao.setPermiteRepescagem(false);
            } else if (returnCode.equals(ERedeRetornoEnum.Retorno174.getCodigo()) || retorno.contains("Zero dollar transaction success")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                transacao.setPermiteRepescagem(false);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                transacao.setPermiteRepescagem(true);
            }
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            transacao.setPermiteRepescagem(true);
        }
    }

    private String executarRequestRede(String parametros, String metodo, String metodoHTTP) throws Exception {
        validarDadosConvenio();

        String autenticacao = numeroFiliacao + ":" + token;

        String URL = this.urlApiRede;
        String path = URL + metodo;
        Map<String, String> maps = new HashMap<String, String>();
        maps.put("Content-Type", "application/json");
        maps.put("Authorization", "Basic " + new String(new Base64().encode(autenticacao.getBytes())));
        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, maps, metodoHTTP, "UTF-8");
    }

    private void validarDadosConvenio() throws Exception {
        if (this.convenioERede == null || UteisValidacao.emptyNumber(this.convenioERede.getCodigo())) {
            throw new Exception("Convênio de cobrança não encontrado ou inativo.");
        }
        if (UteisValidacao.emptyString(this.numeroFiliacao)) {
            throw new Exception("Número Filiação no convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyString(this.token)) {
            throw new Exception("Token no convênio de cobrança não informado.");
        }
    }

    public ConvenioCobrancaVO getConvenioERede() {
        return convenioERede;
    }

    public void setConvenioERede(ConvenioCobrancaVO convenioERede) {
        this.convenioERede = convenioERede;
    }

    public String getNumeroFiliacao() {
        return numeroFiliacao;
    }

    public void setNumeroFiliacao(String numeroFiliacao) {
        this.numeroFiliacao = numeroFiliacao;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception{
        if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            //consultar pela referencia para obter o TID
            consultarPeloCodigoReferencia(transacaoVO);
        }
        consultarSituacaoTransacao(transacaoVO);
    }
}

