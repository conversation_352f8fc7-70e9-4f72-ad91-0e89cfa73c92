package servicos.impl.stripe;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.StripeServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 18/10/2021
 */
public class StripeService extends AbstractCobrancaOnlineServiceComum implements StripeServiceInterface {

    private String urlAPI;
    private String secretKey;

    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public StripeService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() throws Exception {
        if (this.convenioCobrancaVO != null) {
            this.secretKey = this.convenioCobrancaVO.getCodigoAutenticacao02();

            this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiStripe);

            //Stripe é a mesma Url para sandbox e produção, o que altera são os códigos de integração
            //Produção tem a "_live_"
            //Homologacao tem a "_test_"
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO) &&
                    (!this.convenioCobrancaVO.getCodigoAutenticacao02().toLowerCase().contains("_live_"))) {
                throw new Exception("Está em ambiente de " + AmbienteEnum.PRODUCAO.getDescricao() + " e não contem a palavra \"_live_\" nas chaves.");
            } else if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO) &&
                    (!this.convenioCobrancaVO.getCodigoAutenticacao02().toLowerCase().contains("_test_"))) {
                throw new Exception("Está em ambiente de " + AmbienteEnum.HOMOLOGACAO.getDescricao() + " e não contem a palavra \"_test_\" nas chaves.");
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoStripeVO(), TipoTransacaoEnum.STRIPE, this.convenioCobrancaVO);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            //PaymentMethod é um perfil de pagamento (em outras palavras é como se fosse o cartão tokenizado)
            Map<String, String> paramsPaymentMethod = montarParametrosPaymentMethod(dadosCartao, transacao);
            RespostaHttpDTO card = executarRequestStripe("/payment_methods", null, paramsPaymentMethod, MetodoHttpEnum.POST);

            if (card.getHttpStatus() == 200) {
                // PaymentIntent é "criar uma cobrança" usando o PaymentMethod previamente obtido
                Map<String, String> paramsPaymentIntent = montarParametrosPaymentIntent(transacao, card.getResponse());
                RespostaHttpDTO retorno = executarRequestStripe("/payment_intents", null, paramsPaymentIntent, MetodoHttpEnum.POST);
                processarRetorno(transacao, retorno.getResponse());

                transacao.setParamsEnvio(paramsPaymentIntent.toString());
                transacaoDAO.alterar(transacao);

                //consultar para verificar se já foi Concluída com sucesso...
                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    realizarConsultaSituacao(3, transacao);
                }

                preencherOutrasInformacoes(transacao);
                transacaoDAO.alterar(transacao);
                return transacao;
            } else {
                StringBuilder msg = new StringBuilder();
                JSONObject jsonCard = new JSONObject(card.getResponse());
                if (jsonCard.has("error")) {
                    String motivo = jsonCard.getJSONObject("error").getString("message");
                    msg.append(motivo);
                }
                throw new Exception("Não foi possível enviar o cartão. Motivo:" + msg);
            }

        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            if (qtd > 0) {
                RespostaHttpDTO retorno = consultarTransacao(transacaoVO.getCodigoExterno());
                processarRetorno(transacaoVO, retorno.getResponse());
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    Thread.sleep(1000);
                    realizarConsultaSituacao(qtd - 1, transacaoVO);
                }
            }
        }
    }

    @Override
    public RespostaHttpDTO consultarTransacao(String codigoExterno) throws Exception {
        return executarRequestStripe("/payment_intents/" + codigoExterno, null, null, MetodoHttpEnum.GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Pagar.Me");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        // atualiza situação da transação, caso ela esteja aguardando
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            retransmitirTransacao(transacaoVO, null, null);
        }
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            RespostaHttpDTO retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno.getResponse());
        }
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        Map<String, String> paramsRefund = montarParametrosRefund(transacaoVO);
        RespostaHttpDTO retorno = executarRequestStripe("/refunds", null, paramsRefund, MetodoHttpEnum.POST);
        processarRetornoCancelamento(transacaoVO, retorno.getResponse());
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
        transacaoVO.setResultadoCancelamento(retornoCancelamento);
        try {
            JSONObject cancelamentoJSON = new JSONObject(retornoCancelamento);

            if (cancelamentoJSON.has("error")) {
                String code = cancelamentoJSON.getJSONObject("error").optString("code");
                if (!UteisValidacao.emptyString(code) && code.equals("charge_already_refunded")) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                    transacaoVO.setDataHoraCancelamento(Calendario.hoje());
                }
            } else {
                String status = cancelamentoJSON.getString("status");
                if (status.equalsIgnoreCase("succeeded")) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                    transacaoVO.setDataHoraCancelamento(Calendario.hoje());
                } else {
                    consultarSituacaoCobrancaTransacao(transacaoVO);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        transacao.setParamsResposta(retorno);
        JSONObject retornoJSON = new JSONObject(retorno);
        if (retornoJSON.has("error")) {
            processarRetornoComErro(transacao, retorno);
        } else {
            processarRetornoComum(transacao, retorno);
        }
    }

    private void processarRetornoComum(TransacaoVO transacao, String retorno) {
        JSONObject retornoJSON = new JSONObject(retorno);
        if (!UteisValidacao.emptyString(retornoJSON.optString("id"))) {
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                String id = retornoJSON.optString("id");
                if (!UteisValidacao.emptyString(id)) {
                    transacao.setCodigoExterno(id);
                }
            }

            try { //IDENTIFICAR A OPERADORA DO CARTAO
                String card_brand = retornoJSON.getJSONObject("charges").getJSONArray("data").getJSONObject(0).getJSONObject("payment_method_details").getJSONObject("card").optString("brand");
                if (!UteisValidacao.emptyString(card_brand)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = card_brand.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }

            //https://stripe.com/docs/payments/intents#intent-statuses
            //Valores possíveis: succeeded, processing, canceled, requires_payment_method.
            String status = retornoJSON.optString("status");
            if (status.equalsIgnoreCase("succeeded")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("processing")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("canceled")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("requires_payment_method") || status.equalsIgnoreCase("requires_action")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }
        }
    }

    private void processarRetornoComErro(TransacaoVO transacao, String retorno) {
        JSONObject retornoJSON = new JSONObject(retorno);
        if (retornoJSON.getJSONObject("error").optJSONObject("payment_intent") != null && !UteisValidacao.emptyString(retornoJSON.getJSONObject("error").optJSONObject("payment_intent").optString("id"))) {
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                String id = retornoJSON.getJSONObject("error").getJSONObject("payment_intent").optString("id");
                if (!UteisValidacao.emptyString(id)) {
                    transacao.setCodigoExterno(id);
                }
            }

            try { //IDENTIFICAR A OPERADORA DO CARTAO
                String card_brand = retornoJSON.getJSONObject("error").getJSONObject("payment_method").getJSONObject("card").optString("brand");
                if (!UteisValidacao.emptyString(card_brand)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = card_brand.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }

            //https://stripe.com/docs/payments/intents#intent-statuses
            //Valores possíveis: succeeded, processing, canceled, requires_payment_method.
            String status = retornoJSON.getJSONObject("error").getJSONObject("payment_intent").getString("status");
            if (status.equalsIgnoreCase("succeeded")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("processing")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("canceled")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("requires_payment_method")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }
        }
    }

    private Map<String, String> montarParametrosPaymentMethod(CartaoCreditoTO cartaoCreditoTO, TransacaoVO transacaoVO) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        map.put("type", "card");
        map.put("card[number]", cartaoCreditoTO.getNumero());
        map.put("card[exp_month]", Integer.toString(cartaoCreditoTO.getMesValidade()));
        map.put("card[exp_year]", cartaoCreditoTO.getAnoValidadeYYYY());
        //quando vem pelo processo automático o cvv vem nulo, então não precisa nem criar o parâmetro no map
       if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
           map.put("card[cvc]", cartaoCreditoTO.getCodigoSeguranca());
       }
        gravarOutrasInformacoes(cartaoCreditoTO.getNumero(), cartaoCreditoTO, transacaoVO);

        return map;
    }

    private Map<String, String> montarParametrosPaymentIntent(TransacaoVO transacaoVO, String card) {
        String identificador = "TRA" + transacaoVO.getCodigo();
        String amount = Integer.toString((int) (transacaoVO.getValor() * 100));
        JSONObject cardJSON = new JSONObject(card);
        String paymentMethodId = "";

        if (!UteisValidacao.emptyString(cardJSON.optString("id"))) {
            paymentMethodId = cardJSON.optString("id");
        }

        Map<String, String> map = new HashMap<String, String>();
        map.put("amount", amount);
        map.put("currency", transacaoVO.getConvenioCobrancaVO().getCurrencyConvenioEnum().getDescricao());
        map.put("capture_method", "automatic");
        map.put("confirm", "true");
        map.put("description", identificador);
        map.put("payment_method", paymentMethodId);

        return map;
    }

    private Map<String, String> montarParametrosRefund(TransacaoVO transacaoVO) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("payment_intent", transacaoVO.getCodigoExterno());
        return map;
    }

    private RespostaHttpDTO executarRequestStripe(String endPoint, String body, Map<String, String> params, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = this.urlAPI + endPoint;

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("Authorization", "Bearer " + this.secretKey);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, params, body, metodoHttpEnum);
        return respostaHttpDTO;
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }
}
