package servicos.remessa.layouts;

import servicos.remessa.to.ConvenioCobrancaTO;

/**
 * Created by Johnys on 14/02/2017.
 */
public class LayoutFactory {

    /**
     * Retorna o {@link LayoutBase} do convenio específico.
     * @param convenio
     * @return
     */
    public static LayoutBase getLayout(ConvenioCobrancaTO convenio){
        LayoutBase layout = null;
        switch (convenio.getTipoConvenio()){
            case DCC_BIN:
                layout = new LayoutBIN();
                break;
            default:
                break;
        }
        return layout;
    }
}
