package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Cesar Henrique",
        data = "21/11/2024",
        descricao = "INCLUIR NOVOS CAMPOS NA TABELA NOTA FISCAL PARA ALTERAÇÃO DO STATUS MANUALMENTE.",
        motivacao = "M2-2616")
public class M22616AlterarStatusNota implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE NOTAFISCAL ADD COLUMN ALTERADOSTATUSMANUAL BOOLEAN;", c);
        }
    }
}
