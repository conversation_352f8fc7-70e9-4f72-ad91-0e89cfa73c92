package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "11/02/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1192")
public class AtualizacaoTicketGC1192 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table cliente add column idSelfloops text default null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table turma add column aulaIntegracaoSelfloops boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table turmahorariointegracaoselfloops ( \n" +
                    "codigo serial primary key, \n" +
                    "datacriacao timestamp default null, \n" +
                    "courseScheduleId text default null, \n" +
                    "dataalteracao timestamp default null, \n" +
                    "horarioIdentificador text default null, \n" +
                    "jsonEnvio text default null, \n" +
                    "jsonRetorno text default null, \n" +
                    "turma integer references turma (codigo));", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table alunoturmahorariosensorintegracaoselfloops ( \n" +
                    "codigo serial primary key, \n" +
                    "userIdSelfloops text default null, \n" +
                    "courseScheduleId text default null, \n" +
                    "sensorIdSelfloops text default null, \n" +
                    "diaAula timestamp default null, \n" +
                    "jsonEnvio text default null, \n" +
                    "jsonRetorno text default null, \n" +
                    "horarioTurma integer references horarioTurma (codigo), \n" +
                    "cliente integer references cliente (codigo));", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table turmahorarioatividadesintegracaoselfloops ( \n" +
                    "codigo serial primary key, \n" +
                    "userIdSelfloops text default null, \n" +
                    "courseScheduleId text default null, \n" +
                    "courseId text default null, \n" +
                    "diaAula timestamp default null, \n" +
                    "jsonRetorno text default null, \n" +
                    "horarioTurma integer references horarioTurma (codigo));", c);
        }
    }
}
