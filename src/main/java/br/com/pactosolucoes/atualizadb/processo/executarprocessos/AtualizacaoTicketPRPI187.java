package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luís Antônio de Melo Gomes",
        data = "25/09/2024",
        descricao = "PRPI-187 Configuracao Pacto conversas",
        motivacao = "PRPI-187")
public class AtualizacaoTicketPRPI187 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdate("ALTER TABLE public.configuracaocrmia\n" +
                    "    ADD COLUMN tokenzapi varchar(250) NULL,\n" +
                    "    ADD COLUMN idInstancia varchar(250) NULL,\n" +
                    "    ADD COLUMN pactoConversasLogin varchar(250) NULL,\n" +
                    "    ADD COLUMN pactoConversasSenha varchar(250) NULL;",
                    c);

        }
    }
}
