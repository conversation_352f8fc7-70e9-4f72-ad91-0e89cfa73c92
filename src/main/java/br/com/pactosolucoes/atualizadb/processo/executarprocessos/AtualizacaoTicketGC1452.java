package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "01/04/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1452")
public class AtualizacaoTicketGC1452 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN horariocapacidadeporcategoria BOOLEAN default false;", c);
            StringBuilder sql = new StringBuilder();
            sql.append("CREATE TABLE horariocapacidadecategoria (\n");
            sql.append("codigo serial NOT NULL,\n");
            sql.append("horarioturma integer NOT NULL,\n");
            sql.append("tipoCategoria varchar(10) NOT NULL,\n");
            sql.append("capacidade integer NOT NULL,\n");
            sql.append("CONSTRAINT horariocapacidadecategoria_pkey PRIMARY KEY (codigo),\n");
            sql.append("CONSTRAINT fk_horariocapacidadecategoria_horarioturma FOREIGN KEY (horarioturma)\n");
            sql.append("REFERENCES horarioturma (codigo) MATCH SIMPLE\n");
            sql.append("ON UPDATE RESTRICT ON DELETE RESTRICT);\n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }
}
