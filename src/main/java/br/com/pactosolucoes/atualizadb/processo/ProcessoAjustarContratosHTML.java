package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoAjustarContratosHTML {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"24horas"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustarContratosHTML.corrigirContratosHTML(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirContratosHTML(Connection con) throws Exception {
        try {

            String sqlGetContratosHTML = "SELECT \n" +
                    "contrato AS codContrato,\n" +
                    "contratohtml AS html,\n" +
                    "codigo AS codigoContratoTextoPadrao\n" +
                    "FROM contratotextopadrao c\n" +
                    "WHERE contratohtml IS NOT NULL AND contratohtml <> ''\n" +
                    "ORDER BY contrato";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlGetContratosHTML)) {
                    Uteis.logar("INÍCIO | ProcessoAjustarContratosHTML");
                    while (rs.next()) {
                        Integer codContrato = rs.getInt("codContrato");
                        Integer codigoContratoTextoPadrao = rs.getInt("codigoContratoTextoPadrao");
                        String html = rs.getString("html");

                        String[] arraySplitHTML = html.split("<head>");
                        String headHTML = arraySplitHTML[0];
                        String bodyHTML = arraySplitHTML[1];
                        String metaCharset = "<head>\n<meta charset=\"UTF-8\">";

                        String finalHTML = headHTML + metaCharset + bodyHTML;

                        Uteis.logar("Ajustando CONTRATO - " + codContrato);

                        String sqlAlt = "UPDATE contratotextopadrao SET contratohtml = ? WHERE codigo = ?";
                        PreparedStatement sqlAlteracaoContratoTextoPadrao = con.prepareStatement(sqlAlt);
                        sqlAlteracaoContratoTextoPadrao.setString(1, finalHTML);
                        sqlAlteracaoContratoTextoPadrao.setInt(2, codigoContratoTextoPadrao);
                        sqlAlteracaoContratoTextoPadrao.execute();

                    }
                    Uteis.logar("FIM | ProcessoAjustarContratosHTML");
                }
            }

        } catch (Exception ignore) {
            ignore.printStackTrace();
        }
    }
}
