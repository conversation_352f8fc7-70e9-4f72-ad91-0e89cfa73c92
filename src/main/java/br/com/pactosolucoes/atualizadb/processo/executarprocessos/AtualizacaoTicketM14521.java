package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarPerguntasEmBrancoBV;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "20/02/2025",
        descricao = "Ajustar perguntas em branco do BV",
        motivacao = "M1-4521")
public class AtualizacaoTicketM14521 implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarPerguntasEmBrancoBV.ajustarVencimentoContratosPorDataInicioContrato(c);
        }
    }

}
