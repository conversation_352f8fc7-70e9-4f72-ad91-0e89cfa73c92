package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "16/10/2024",
        descricao = "Remover sintéticos que não estão vinculados a nenhum cliente",
        motivacao = "M1-3038")
public class AtualizacaoTicketM13038 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE\n" +
                    "FROM situacaoclientesinteticodw\n" +
                    "WHERE CODIGO IN(\n" +
                    "\tSELECT s.codigo\n" +
                    "\tFROM situacaoclientesinteticodw s\n" +
                    "\tLEFT JOIN cliente c ON s.codigocliente = c.codigo\n" +
                    "\tWHERE c.codigo IS NULL)",c);
        }
    }
}
