package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.List;

public class AjustarVencimentoParcelas {
    public static void main(String[] args) throws Exception {
        Connection c = DriverManager.getConnection("*********************************************", "postgres", "pactodb");

        ajustarVencimentoParcelasRecorrencia(c);
    }

    public static void ajustarVencimentoParcelasRecorrencia(Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();

       sql.append(" select em.nome,cli.codigomatricula,cr.contrato,mp.codigo as parcela, c.vigenciade,mp.datavencimento,  cr.diavencimentocartao, em.permitecontratopospagorenovacaoauto, exists(SELECT par.codigo FROM movparcela par INNER JOIN movprodutoparcela mpp  ON mpp.movparcela = par.codigo INNER JOIN movproduto mprod ON mpp.movproduto = mprod.codigo INNER JOIN produto prod ON mprod.produto = prod.codigo WHERE par.contrato  = c.contratobaseadorenovacao ");sql.append(" AND par.regimerecorrencia IS TRUE  AND prod.tipoproduto = 'PM' AND mp.situacao not in ('CA', 'RG')  AND date_part('month', par.datavencimento) = date_part('month', mp.datavencimento - interval '1 month')   AND date_part('year', par.datavencimento) = date_part('year', mp.datavencimento - interval '1 month')) as parcelaContratoAnterior,");
       sql.append(" cp.recebimentoprepago");
       sql.append(" from movparcela mp inner join contrato c on c.codigo = mp.contrato");
       sql.append(" inner join empresa em on em.codigo = c.empresa");
       sql.append(" inner join cliente cli on cli.pessoa = c.pessoa");
       sql.append(" inner join contratorecorrencia  cr on cr.contrato = c.codigo");
       sql.append(" inner join contratocondicaopagamento ccp on ccp.contrato = c.codigo");
       sql.append(" inner join condicaopagamento cp on cp.codigo = ccp.condicaopagamento");
       sql.append(" inner join usuario u on u.codigo = responsavelcontrato and u.username = 'RECOR'");
       sql.append(" where c.regimerecorrencia  and c.datalancamento > '2019-09-05' and c.situacao = 'AT' and mp.descricao = 'PARCELA 1'  and ((date_part('month',c.vigenciade) < date_part('month',mp.datavencimento))");
       sql.append("         or (cp.recebimentoprepago and c.datalancamento < c.vigenciade - interval '14 days' and date_part('month',c.vigenciade) = date_part('month',mp.datavencimento) ))  order by vigenciade");

        ResultSet consultaContratos = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        MovParcela movParcelaDAO = new MovParcela(con);
        while(consultaContratos.next()){
            if(consultaContratos.getBoolean("permitecontratopospagorenovacaoauto") && consultaContratos.getBoolean("parcelaContratoAnterior")){
                continue;
            }
            try{
                con.setAutoCommit(false);
                List<MovParcelaVO> parcelas = movParcelaDAO.consultarPorContrato(consultaContratos.getInt("contrato"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                for(MovParcelaVO parcela: parcelas) {
                    if (parcela.getDescricao().startsWith("PARCELA")) {
                        parcela.setDataVencimento(Uteis.somarCampoData(parcela.getDataVencimento(), Calendar.MONTH, -1));
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE movparcela set datavencimento = '" +Uteis.getDataJDBC(parcela.getDataVencimento()) + "', datacobranca = '"+ Uteis.getDataJDBC(parcela.getDataVencimento()) + "' where codigo = " + parcela.getCodigo(), con);
                    }
                }
                Uteis.logar("Contrato "+ consultaContratos.getInt("contrato")+" teve o vencimento de suas parcelas ajustadas");
                con.commit();
            } catch (Exception e){
                con.rollback();
                Uteis.logar("Erro ao ajustar vencimento das parcelas do contrato "+ consultaContratos.getInt("contrato")+": "+ e.getMessage());
            } finally {
                con.setAutoCommit(true);
            }

        }
        consultaContratos = null;
        movParcelaDAO = null;
    }
}
