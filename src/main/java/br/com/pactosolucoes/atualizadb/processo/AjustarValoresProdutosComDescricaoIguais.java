/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AjustarValoresProdutosComDescricaoIguais {
    
    private static  Double guara = 0.0;
    protected  Connection con;
    private Double sul = 0.0;
    private Double norte = 0.0;
    private Double qnl = 0.0;
    private Double ceilandia = 0.0;
    private Double totalGeral = 0.0;
    protected String sql = "INSERT INTO movproduto(\n" +
            "            situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, \n" +
            "            anoreferencia, mesreferencia, responsavellancamento, datalancamento, \n" +
            "            totalfinal, valordesconto, precounitario, quantidade, descricao, \n" +
            "            empresa, pessoa, contrato, produto, naogerarmensagem)\n" +
            "    VALUES (?, ?, ?, ?, ?, \n" +
            "            ?, ?, ?, ?, \n" +
            "            ?, ?, ?, ?, ?, \n" +
            "            ?, ?, ?, ?, ?);";
        
    protected String sqlMovprodutoParcela = "INSERT INTO movprodutoparcela(\n" +
            "            movproduto,movparcela, recibopagamento,valorpago)\n" +
            "    VALUES (?, ?, ?, ?);";
    
    

    public Double getTotalGeral() {
        return totalGeral;
    }

    public void setTotalGeral(Double totalGeral) {
        this.totalGeral = totalGeral;
    }



    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("********************************************", "postgres", "pactodb");
           AjustarValoresProdutosComDescricaoIguais atualiza = new AjustarValoresProdutosComDescricaoIguais();
           atualiza.atualizaValorProdutos(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void atualizaValorProdutos(Connection con1) throws Exception {
        this.con = con1;
        File matriculaSemOperacao = new File("D:/produtosAjustados.txt");
        FileWriter writer = new FileWriter(matriculaSemOperacao, true);
        PrintWriter saida = new PrintWriter(writer);
        ResultSet consultaUltParcela;
        ResultSet consultaUltProduto; 
        ResultSet consultaParcela;
        ResultSet consultaProdutos;
        
          
        saida.println("atualizar produtos- início em : " + new Date());
      

        int geral = 0;
        String sql = "INSERT INTO movproduto(\n" +
            "            situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, \n" +
            "            anoreferencia, mesreferencia, responsavellancamento, datalancamento, \n" +
            "            totalfinal, valordesconto, precounitario, quantidade, descricao, \n" +
            "            empresa, pessoa, contrato, produto, naogerarmensagem)\n" +
            "    VALUES (?, ?, ?, ?, ?, \n" +
            "            ?, ?, ?, ?, \n" +
            "            ?, ?, ?, ?, ?, \n" +
            "            ?, ?, ?, ?, ?);";
        
        String sqlMovprodutoParcela = "INSERT INTO movprodutoparcela(\n" +
            "            movproduto,movparcela, recibopagamento,valorpago)\n" +
            "    VALUES (?, ?, ?, ?);";

        saida.println("################### CONTRATOS SEM OPERAÇÕES ####################");
        ResultSet consultaContrato = SuperFacadeJDBC.criarConsulta("select * from movproduto  "
                + "where  produto = 17 and contrato not in  (select contrato from contratooperacao  where tipooperacao in('TR', 'AH','MM','CA'))", con);
        int count = 0;
        Double valorSegundaParcela = 0.0;
        while (consultaContrato.next()) {
            consultaParcela = SuperFacadeJDBC.criarConsulta("select sum(valorparcela) as valorParcela from movparcela where contrato = " + consultaContrato.getInt("contrato") + " and situacao <> 'RG' ", con);
            consultaParcela.next();
            Double valorParcela = consultaParcela.getDouble("valorParcela");
            consultaProdutos = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valorProduto from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and produto <> 13", con);
            consultaProdutos.next();
            Double valorProdutos = consultaProdutos.getDouble("valorProduto");
            if (Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(valorProdutos)) {
                Double diferenca = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProdutos);
                if (diferenca.doubleValue() > 1.0) {
                    try {
                        con.setAutoCommit(false);
                        count++;
                        geral++;
                        saida.println(count + "# Contrato = " + consultaContrato.getInt("contrato") + ", parcelas = " + valorParcela.toString() + ", produtos = "
                                + valorProdutos + ", valor sugerido para o produto = " + diferenca);
                        consultaUltParcela = SuperFacadeJDBC.criarConsulta("select * from movparcela where contrato = " + consultaContrato.getInt("contrato") + " order by codigo desc limit 1", con);
                        consultaUltParcela.next();
                        Double desconto = 100 - diferenca;

                        PreparedStatement sqlInsert = con.prepareStatement(sql);

                        sqlInsert.setString(1, consultaUltParcela.getString("situacao"));
                        sqlInsert.setBoolean(2,  true);
                        sqlInsert.setDate(3, consultaContrato.getDate("datafinalvigencia"));
                        sqlInsert.setDate(4, consultaContrato.getDate("datainiciovigencia"));
                        sqlInsert.setBoolean(5, false);
                        sqlInsert.setInt(6, consultaContrato.getInt("anoreferencia"));
                        sqlInsert.setString(7, consultaContrato.getString("mesreferencia"));
                        sqlInsert.setInt(8, consultaContrato.getInt("responsavellancamento"));
                        sqlInsert.setTimestamp(9, consultaContrato.getTimestamp("datalancamento"));
                        sqlInsert.setDouble(10, diferenca);
                        sqlInsert.setDouble(11, desconto);
                        sqlInsert.setDouble(12, 100.0);
                        sqlInsert.setInt(13, 1);
                        sqlInsert.setString(14, consultaContrato.getString("descricao"));
                        sqlInsert.setInt(15, consultaContrato.getInt("empresa"));
                        sqlInsert.setInt(16, consultaContrato.getInt("pessoa"));
                        sqlInsert.setInt(17, consultaContrato.getInt("contrato"));
                        sqlInsert.setInt(18, 41);
                        sqlInsert.setBoolean(19, false);



                        sqlInsert.execute();
                        consultaUltProduto = SuperFacadeJDBC.criarConsulta("select codigo from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and descricao <> 'PARCELA RENEGOCIADA' order by codigo desc limit 1", con);
                        consultaUltProduto.next();
                        valorSegundaParcela = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(consultaUltParcela.getDouble("valorparcela") - diferenca);
                       
                        if(valorSegundaParcela >= 0.0){
                            incluirmovprodutoparcela(diferenca, consultaUltProduto.getInt("codigo"), consultaUltParcela.getInt("codigo"),consultaUltParcela.getString("situacao"),consultaContrato.getInt("contrato")); 
                        } else{
                            incluirmovprodutoparcela(consultaUltParcela.getDouble("valorparcela"), consultaUltProduto.getInt("codigo"), consultaUltParcela.getInt("codigo"),consultaUltParcela.getString("situacao"),consultaContrato.getInt("contrato")); 
                            incluirmovprodutoparcela((valorSegundaParcela * -1), consultaUltProduto.getInt("codigo"), (consultaUltParcela.getInt("codigo") - 1),consultaUltParcela.getString("situacao"),consultaContrato.getInt("contrato")); 
                        }
                      
                        somarDiferencas(diferenca, consultaContrato.getInt("empresa"));
                        con.commit();
                    } catch(Exception e){
                        con.rollback();
                        con.setAutoCommit(true);
                        saida.println("# Contrato = " + consultaContrato.getInt("contrato") + " teve o seguinte problema "  + e.getMessage());
                    } finally {
                        con.setAutoCommit(true);
                    }
                }

            }
        }
        count = 0;
//        saida.println("\n\n################### CONTRATOS  COM OPERAÇÕES ####################");
//        consultaContrato = SuperFacadeJDBC.criarConsulta("select * from movproduto  "
//                + "where  produto = 17 and contrato in  (select contrato from contratooperacao  where tipooperacao in('TR', 'AH','MM','CA'))", con);
//        while (consultaContrato.next()) {
//            consultaParcela = SuperFacadeJDBC.criarConsulta("select sum(valorparcela) as valorParcela from movparcela where contrato = " + consultaContrato.getInt("contrato"), con);
//            consultaParcela.next();
//            Double valorParcela = consultaParcela.getDouble("valorParcela");
//            consultaProdutos = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valorProduto from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and produto <> 13", con);
//            consultaProdutos.next();
//            Double valorProdutos = consultaProdutos.getDouble("valorProduto");
//            if (Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(valorProdutos)) {
//                Double diferenca = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProdutos);
//                if (diferenca.doubleValue() > 1.0) {
//                    count++;
//                    geral++;
//                    saida.println(count + "# Contrato = " + consultaContrato.getInt("contrato") + ", parcelas = " + valorParcela.toString() + ", produtos = "
//                            + valorProdutos + ", valor sugerido para o produto = " + diferenca);
//                    consultaUltParcela = SuperFacadeJDBC.criarConsulta("select * from movparcela where contrato = " + consultaContrato.getInt("contrato") + " order by codigo desc limit 1", con);
//                    consultaUltParcela.next();
//                    Double desconto = 100 - diferenca;
//                    
//                    PreparedStatement sqlInsert = con.prepareStatement(sql);
//
//                    sqlInsert.setString(1, consultaUltParcela.getString("situacao"));
//                    sqlInsert.setBoolean(2,  true);
//                    sqlInsert.setDate(3, consultaContrato.getDate("datafinalvigencia"));
//                    sqlInsert.setDate(4, consultaContrato.getDate("datainiciovigencia"));
//                    sqlInsert.setBoolean(5, false);
//                    sqlInsert.setInt(6, consultaContrato.getInt("anoreferencia"));
//                    sqlInsert.setString(7, consultaContrato.getString("mesreferencia"));
//                    sqlInsert.setInt(8, consultaContrato.getInt("responsavellancamento"));
//                    sqlInsert.setTimestamp(9, consultaContrato.getTimestamp("datalancamento"));
//                    sqlInsert.setDouble(10, diferenca);
//                    sqlInsert.setDouble(11, desconto);
//                    sqlInsert.setDouble(12, 100.0);
//                    sqlInsert.setInt(13, 1);
//                    sqlInsert.setString(14, consultaContrato.getString("descricao"));
//                    sqlInsert.setInt(15, consultaContrato.getInt("empresa"));
//                    sqlInsert.setInt(16, consultaContrato.getInt("pessoa"));
//                    sqlInsert.setInt(17, consultaContrato.getInt("contrato"));
//                    sqlInsert.setInt(18, 41);
//                    sqlInsert.setBoolean(19, false);
//                         
//                    
//                    
//                    sqlInsert.execute();
//                    consultaUltProduto = SuperFacadeJDBC.criarConsulta("select codigo from movproduto where contrato = " + consultaContrato.getInt("contrato") + " order by codigo desc limit 1", con);
//                    consultaUltProduto.next();
//                   
//                   PreparedStatement sqlInsertProdParc = con.prepareStatement(sqlMovprodutoParcela);
//                    sqlInsertProdParc.setInt(1, consultaUltProduto.getInt("codigo"));
//                   sqlInsertProdParc.setInt(2, consultaUltParcela.getInt("codigo"));
//                   if(consultaUltParcela.getString("situacao").equals("PG")){
//                        consultaRecibo = SuperFacadeJDBC.criarConsulta("select recibopagamento from movprodutoparcela where movparcela = " + consultaUltParcela.getInt("codigo") + " limit 1", con);
//                        consultaRecibo.next();
//                        sqlInsertProdParc.setInt(3, consultaRecibo.getInt("recibopagamento"));
//                   } else {
//                        sqlInsertProdParc.setNull(3, 0);
//                   }
//                    sqlInsertProdParc.setDouble(4, diferenca);
//                    sqlInsertProdParc.execute();
//                    somarDiferencas(diferenca, consultaContrato.getInt("empresa"));
//                }
//            }
//        }
        

        saida.println("\n\n################### Total de contrato = " + geral + " ####################");
        saida.println("\n\n################### Valores ####################");
         saida.println("Total Geral: R$ "+getTotalGeral());
        saida.println("atualizar Matriculas- fim em : " + new Date());
        saida.close();
        writer.close();
    }

    private  void somarDiferencas(Double diferenca, int empresa) {
        setTotalGeral(getTotalGeral()+diferenca);
        switch(empresa){
            case 1:
                setGuara(getGuara() + diferenca);
                break;
            case 3:
                setNorte(getNorte() + diferenca);
                break;
            case 4:
                setQnl(getQnl() + diferenca);
                break;
            case 5:
                setSul(getSul() + diferenca);
                break;
            case 6:
                setCeilandia(getCeilandia() + diferenca);
                break;
        }
    }

    public Double getCeilandia() {
        return ceilandia;
    }

    public void setCeilandia(Double ceilandia) {
        this.ceilandia = ceilandia;
    }

    public Double getGuara() {
        return guara;
    }

    public void setGuara(Double guara) {
        this.guara = guara;
    }

    public Double getNorte() {
        return norte;
    }

    public void setNorte(Double norte) {
        this.norte = norte;
    }

    public Double getQnl() {
        return qnl;
    }

    public void setQnl(Double qnl) {
        this.qnl = qnl;
    }

    public Double getSul() {
        return sul;
    }

    public void setSul(Double sul) {
        this.sul = sul;
    }

    private void incluirmovprodutoparcela(Double diferenca, int movproduto, int movparcela, String situacao,int contrato) throws Exception {
        PreparedStatement sqlInsertProdParc = con.prepareStatement(sqlMovprodutoParcela);
        sqlInsertProdParc.setInt(1, movproduto);
        sqlInsertProdParc.setInt(2, movparcela);
        if (situacao.equals("PG")) {
            ResultSet consultaRecibo = SuperFacadeJDBC.criarConsulta("select recibopagamento from movprodutoparcela where movparcela = " + movparcela + " limit 1", con);
            if(!consultaRecibo.next()){
                consultaRecibo = SuperFacadeJDBC.criarConsulta("select max(recibopagamento) as recibopagamento  from  movprodutoparcela where movparcela in( select codigo from movparcela where contrato =  " + contrato + ")", con);
                consultaRecibo.next();
            }
            sqlInsertProdParc.setInt(3, consultaRecibo.getInt("recibopagamento"));
        } else {
            sqlInsertProdParc.setNull(3, 0);
        }
        sqlInsertProdParc.setDouble(4, diferenca);
        sqlInsertProdParc.execute();
    }
}
