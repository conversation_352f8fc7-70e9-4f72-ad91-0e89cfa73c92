package br.com.pactosolucoes.atualizadb.processo.ajustebd;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.Contrato;

import javax.xml.crypto.Data;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 07/03/2017
 */
public class AdicionarInicioContrato extends SuperEntidade {

    public AdicionarInicioContrato() throws Exception {
        super();
    }

    public List<ContratoVO> consultarContratosAfetados(int codEmpresa, Date dataInicio, PlanoVO plano) throws Exception {
        String sbSql = "SELECT con.*\n" +
                "FROM contrato con\n" +
                "WHERE 1 = 1\n" +
                "      AND con.empresa = ?\n" +
                "      AND con.vigenciade = ?\n" +
                "      AND con.plano = ?;";

        PreparedStatement ps = con.prepareStatement(sbSql);
        ps.setInt(1, codEmpresa);
        ps.setDate(2, Uteis.getDataJDBC(dataInicio));
        ps.setInt(3, plano.getCodigo());
        ResultSet rs = ps.executeQuery();

        return Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_ROBO, con);
    }


    public void adicionarDias(ContratoVO contratoVO, int qtdDias, UsuarioVO usuarioLogado, boolean alterarParcelas) throws Exception {
        try {
            con.setAutoCommit(false);
            List<MovParcelaVO> parcelasContrato = getFacade().getMovParcela().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<MovParcelaVO> parcelasAdesao = new ArrayList<MovParcelaVO>();
            List<MovParcelaVO> parcelasPlano = new ArrayList<MovParcelaVO>();
            for (MovParcelaVO parcelaVO : parcelasContrato) {
                if (!parcelaVO.getDescricao().contains("ANUIDADE")) {
                    if (parcelaVO.getDescricao().contains("ADESÃO")) {
                        parcelasAdesao.add(parcelaVO);
                    } else {
                        parcelasPlano.add(parcelaVO);
                    }
                }
            }
            Ordenacao.ordenarLista(parcelasAdesao, "codigo");
            Ordenacao.ordenarLista(parcelasPlano, "codigo");
            atualizarParcelasProdutos(qtdDias, parcelasPlano, contratoVO, alterarParcelas);
            atualizarParcelasProdutos(qtdDias, parcelasAdesao, contratoVO, alterarParcelas);

            List<PeriodoAcessoClienteVO> periodoAcessoClienteVOS = getFacade().getPeriodoAcessoCliente().consultarPorContrato(contratoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (int i = 0; i < periodoAcessoClienteVOS.size(); i++) {
                boolean modificou = false;
                PeriodoAcessoClienteVO periodoAcessoClienteVO = periodoAcessoClienteVOS.get(i);
                if (i == 0) {
                    periodoAcessoClienteVO.setDataInicioAcesso(Uteis.somarDias(periodoAcessoClienteVO.getDataInicioAcesso(), qtdDias));
                    modificou = true;
                }
                if (i == (periodoAcessoClienteVOS.size() - 1)) {
                    periodoAcessoClienteVO.setDataFinalAcesso(Uteis.somarDias(periodoAcessoClienteVO.getDataFinalAcesso(), qtdDias));
                    modificou = true;
                }
                if (modificou) {
                    String updPeriodoAcesso = "UPDATE periodoacessocliente SET datainicioacesso = ?, datafinalacesso = ? WHERE codigo = ?";

                    PreparedStatement psUpdPeriodoAcesso = con.prepareStatement(updPeriodoAcesso);
                    psUpdPeriodoAcesso.setDate(1, Uteis.getDataJDBC(periodoAcessoClienteVO.getDataInicioAcesso()));
                    psUpdPeriodoAcesso.setDate(2, Uteis.getDataJDBC(periodoAcessoClienteVO.getDataFinalAcesso()));
                    psUpdPeriodoAcesso.setInt(3, periodoAcessoClienteVO.getCodigo());
                    psUpdPeriodoAcesso.execute();
                }
            }


            List<HistoricoContratoVO> historicoContratoVOS = getFacade().getHistoricoContrato().consultarPorContrato(contratoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (int i = 0; i < historicoContratoVOS.size(); i++) {
                boolean modificou = false;
                HistoricoContratoVO historicoContratoVO = historicoContratoVOS.get(i);
                if (i == 0) {
                    historicoContratoVO.setDataInicioSituacao(Uteis.somarDias(historicoContratoVO.getDataInicioSituacao(), qtdDias));
                    modificou = true;
                }
                if (i == (periodoAcessoClienteVOS.size() - 1)) {
                    historicoContratoVO.setDataFinalSituacao(Uteis.somarDias(historicoContratoVO.getDataFinalSituacao(), qtdDias));
                    modificou = true;
                }
                if (modificou) {
                    String updHistoricoContrato = "UPDATE historicocontrato SET datainiciosituacao = ?, datafinalsituacao = ? WHERE codigo = ?";

                    PreparedStatement psUpdHistoricoContrato = con.prepareStatement(updHistoricoContrato);
                    psUpdHistoricoContrato.setDate(1, Uteis.getDataJDBC(historicoContratoVO.getDataInicioSituacao()));
                    psUpdHistoricoContrato.setDate(2, Uteis.getDataJDBC(historicoContratoVO.getDataFinalSituacao()));
                    psUpdHistoricoContrato.setInt(3, historicoContratoVO.getCodigo());
                    psUpdHistoricoContrato.execute();
                }
            }


            String updContrato = "UPDATE contrato SET vigenciade = ?, vigenciaAte = ?, vigenciaateajustada = ? WHERE codigo = ?";
            PreparedStatement psUpdContrato = con.prepareStatement(updContrato);
            psUpdContrato.setDate(1, Uteis.getDataJDBC(Uteis.somarDias(contratoVO.getVigenciaDe(), qtdDias)));
            psUpdContrato.setDate(2, Uteis.getDataJDBC(Uteis.somarDias(contratoVO.getVigenciaAte(), qtdDias)));
            psUpdContrato.setDate(3, Uteis.getDataJDBC(Uteis.somarDias(contratoVO.getVigenciaAteAjustada(), qtdDias)));
            psUpdContrato.setInt(4, contratoVO.getCodigo());
            psUpdContrato.execute();

            incluirLog(contratoVO, qtdDias, usuarioLogado);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void atualizarParcelasProdutos(int qtdDias, List<MovParcelaVO> parcelasContrato, ContratoVO contratoVO, boolean alterarParcelas) throws Exception {
        String updMovparcela = "UPDATE movparcela SET datavencimento = ?, datacobranca = ? WHERE codigo = ?";
        String updMovproduto = "UPDATE movproduto SET descricao = ?, mesreferencia = ?, anoreferencia = ? WHERE codigo = ?";
        int i = 0;
        Date dataParcelaAnterior = null;
        List<Integer> movprodutosAjustados = new ArrayList<>();
        Date novaDatainicio = Uteis.somarDias(contratoVO.getVigenciaDe(), qtdDias);
        Date dataAvaliarCompetencia = novaDatainicio;
        int mes = 1;

        for (MovParcelaVO movParcelaVO : parcelasContrato) {
            movParcelaVO.setMovProdutoParcelaVOs(getFacade().getMovProdutoParcela().consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            if(alterarParcelas) {
                if (i == 0) {
                    movParcelaVO.setDataVencimento(Uteis.somarDias(movParcelaVO.getDataVencimento(), qtdDias));
                    movParcelaVO.setDataCobranca(Uteis.somarDias(movParcelaVO.getDataCobranca(), qtdDias));
                } else {
                    movParcelaVO.setDataVencimento(Uteis.somarMeses(dataParcelaAnterior, 1));
                    movParcelaVO.setDataCobranca(Uteis.somarMeses(dataParcelaAnterior, 1));
                }
                dataParcelaAnterior = movParcelaVO.getDataVencimento();

                PreparedStatement psUpdMovParcela = con.prepareStatement(updMovparcela);
                psUpdMovParcela.setDate(1, Uteis.getDataJDBC(movParcelaVO.getDataVencimento()));
                psUpdMovParcela.setDate(2, Uteis.getDataJDBC(movParcelaVO.getDataCobranca()));
                psUpdMovParcela.setInt(3, movParcelaVO.getCodigo());
                psUpdMovParcela.execute();
                i++;
            }

            for (MovProdutoParcelaVO mppVO : movParcelaVO.getMovProdutoParcelaVOs()) {
                if(!movprodutosAjustados.contains(mppVO.getMovProdutoVO().getCodigo())) {
                    MovProdutoVO movProdutoVO = mppVO.getMovProdutoVO();

                    if (movProdutoVO.getProduto().getTipoProduto().equals("PM")) {
                        movProdutoVO.setMesReferencia(Uteis.getDataMesAnoConcatenado(dataAvaliarCompetencia));
                        movProdutoVO.setAnoReferencia(Uteis.getAnoData(dataAvaliarCompetencia));
                        movProdutoVO.setDescricao(contratoVO.getPlano().getDescricao() + " - " + movProdutoVO.getMesReferencia());
                        dataAvaliarCompetencia = Uteis.obterDataFuturaParcela(novaDatainicio, mes);
                        mes++;

                    } else {
                        movProdutoVO.setMesReferencia(Uteis.getDataMesAnoConcatenado(novaDatainicio));
                        movProdutoVO.setAnoReferencia(Uteis.getAnoData(novaDatainicio));
                    }

                    PreparedStatement psUpdMovProduto = con.prepareStatement(updMovproduto);
                    psUpdMovProduto.setString(1, movProdutoVO.getDescricao());
                    psUpdMovProduto.setString(2, movProdutoVO.getMesReferencia());
                    psUpdMovProduto.setInt(3, movProdutoVO.getAnoReferencia());
                    psUpdMovProduto.setInt(4, movProdutoVO.getCodigo());
                    psUpdMovProduto.execute();
                    movprodutosAjustados.add(movProdutoVO.getCodigo());
                }
            }

        }
    }

    private void incluirLog(ContratoVO contratoVO, int qtdDias, UsuarioVO usuarioVO) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(contratoVO.getCodigo().toString());
        obj.setPessoa(contratoVO.getPessoa().getCodigo());
        obj.setNomeEntidade("CONTRATO");
        obj.setNomeEntidadeDescricao("Configurações");
        obj.setOperacao("Adicionar Dias ao Início Contrato (Processo)");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("Início do Contrato");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior(contratoVO.getVigenciaDe_Apresentar());
        obj.setValorCampoAlterado(Uteis.getData(Uteis.somarDias(contratoVO.getVigenciaDe(), qtdDias)));

        getFacade().getLog().incluirSemCommit(obj);
    }
}
