package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenderson Reis",
        data = "29/01/2025",
        descricao = "Alterar tamanho campo rgorgao tabela pessoa",
        motivacao = "Erro ao importar dados de tamanho maior.")
public class M14243AlterTablePessoaColumnRgOrgao implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE Pessoa \n" +
                    "ALTER COLUMN rgOrgao TYPE VARCHAR(20) USING rgOrgao::VARCHAR(20);", c);
        }
    }
}
