package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "08/10/2024",
        descricao = "Ajustar parcelas que foram geradas com vencimento anterior a data de inicio do contrato",
        motivacao = "M1-2949")
public class AtualizacaoTicketM12949 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT \n" +
                    "\t'UPDATE movparcela SET datavencimento = datavencimento + ''' || con.vigenciade - mpv.datavencimento || ''', datacobranca = datacobranca + ''' || con.vigenciade - mpv.datavencimento || ''' WHERE contrato = ' || con.codigo || ';' AS sqlUpdate,\n" +
                    "\tcon.datalancamento AS lancamentoContrato,\n" +
                    "\tcon.dataalteracaomanual,\n" +
                    "\tcon.vigenciade AS inicioContrato, \n" +
                    "\tmpv.datavencimento AS vencimentoParcela,\n" +
                    "\tcon.codigo AS codigoContrato,\n" +
                    "\tcon.vigenciade - mpv.datavencimento AS diferencaVencimento,\n" +
                    "\tmpv.descricao AS descricaoParcela, \n" +
                    "\tmpv.codigo AS codigoParcela,\n" +
                    "\tcli.codigomatricula AS matricula,\n" +
                    "\tpes.nome\n" +
                    "FROM contrato con\n" +
                    "INNER JOIN movparcela mpv ON mpv.contrato = con.codigo\n" +
                    "INNER JOIN pessoa pes ON pes.codigo = mpv.pessoa \n" +
                    "INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n" +
                    "WHERE ((con.dataalteracaomanual IS NULL AND con.datalancamento > mpv.datavencimento) OR (con.dataalteracaomanual IS NOT NULL AND con.dataalteracaomanual > mpv.datavencimento))\n" +
                    "AND con.datalancamento > '2024-07-01'\n" +
                    "AND con.datalancamento < '2024-12-31'\n" +
                    "AND NOT con.bolsa\n" +
                    "AND mpv.descricao ILIKE 'PARCELA 1'\n" +
                    "AND con.vigenciade - mpv.datavencimento > '300 days'", c);
            while (rs.next()) {
                String sqlUpdate = rs.getString("sqlUpdate");
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlUpdate, c);
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }

}
