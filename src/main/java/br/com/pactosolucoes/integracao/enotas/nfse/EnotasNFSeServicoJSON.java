package br.com.pactosolucoes.integracao.enotas.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.List;
import java.util.Optional;

public class EnotasNFSeServicoJSON extends SuperJSON {

    private String descricao;
    private Double aliquotaIss;
    private boolean issRetidoFonte;
    private String cnae;
    private String codigoServicoMunicipio;
    private Double valorPis;
    private Double valorCofins;
    private Double valorCsll;
    private Double valorInss;
    private Double valorIr;
    private String itemListaServicoLC116;
    private String descricaoServicoMunicipio;


    public EnotasNFSeServicoJSON(NotaTO notaTO) {
        this.descricao = notaTO.getNotaDescricao().replace("|", "");
        this.aliquotaIss = notaTO.getNotaAliquotaISS();
        this.issRetidoFonte = notaTO.isNotaIssRetido();
        if (UteisValidacao.emptyString(notaTO.getNotaCNAE())) {
            this.cnae = null;
        } else {
            this.cnae = notaTO.getNotaCNAE();
        }
        this.codigoServicoMunicipio = notaTO.getNotaCodigoTributacaoMunicipio();
        this.valorPis = notaTO.getNotaValorPIS();
        this.valorCofins = notaTO.getNotaValorCOFINS();
        this.valorCsll = notaTO.getNotaValorCSLL();
        this.valorInss = 0.0;
        this.valorIr = 0.0;
        this.itemListaServicoLC116 = UteisValidacao.emptyString(notaTO.getNotaItemListaServico()) ? null : notaTO.getNotaItemListaServico();
        this.descricaoServicoMunicipio = definirDescricaoServicoMunicipioProduto(notaTO.getNotaProdutos());
    }

    private String definirDescricaoServicoMunicipioProduto(List<NotaProdutoTO> notaProdutos){
        return notaProdutos.stream().filter(notaProdutoTO ->  !UteisValidacao.emptyString(notaProdutoTO.getProdutoVO().getDescricaoServicoMunicipio())).map(notaProdutoTO -> notaProdutoTO.getProdutoVO().getDescricaoServicoMunicipio())   // Mapeia para a descrição
                .findFirst().orElse("");
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getAliquotaIss() {
        return aliquotaIss;
    }

    public void setAliquotaIss(Double aliquotaIss) {
        this.aliquotaIss = aliquotaIss;
    }

    public boolean isIssRetidoFonte() {
        return issRetidoFonte;
    }

    public void setIssRetidoFonte(boolean issRetidoFonte) {
        this.issRetidoFonte = issRetidoFonte;
    }

    public String getCnae() {
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public String getCodigoServicoMunicipio() {
        return codigoServicoMunicipio;
    }

    public void setCodigoServicoMunicipio(String codigoServicoMunicipio) {
        this.codigoServicoMunicipio = codigoServicoMunicipio;
    }

    public Double getValorPis() {
        return valorPis;
    }

    public void setValorPis(Double valorPis) {
        this.valorPis = valorPis;
    }

    public Double getValorCofins() {
        return valorCofins;
    }

    public void setValorCofins(Double valorCofins) {
        this.valorCofins = valorCofins;
    }

    public Double getValorCsll() {
        return valorCsll;
    }

    public void setValorCsll(Double valorCsll) {
        this.valorCsll = valorCsll;
    }

    public Double getValorInss() {
        return valorInss;
    }

    public void setValorInss(Double valorInss) {
        this.valorInss = valorInss;
    }

    public Double getValorIr() {
        return valorIr;
    }

    public void setValorIr(Double valorIr) {
        this.valorIr = valorIr;
    }

    public String getItemListaServicoLC116() { return itemListaServicoLC116; }

    public void setItemListaServicoLC116(String itemListaServicoLC116) { this.itemListaServicoLC116 = itemListaServicoLC116; }

    public String getDescricaoServicoMunicipio() {
        return descricaoServicoMunicipio;
    }

    public void setDescricaoServicoMunicipio(String descricaoServicoMunicipio) {
        this.descricaoServicoMunicipio = descricaoServicoMunicipio;
    }
}
