/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.ce.comuns.to;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;
import negocio.facade.jdbc.arquitetura.Usuario;

/**
 *
 * <AUTHOR>
 */
public class DevolucaoCreditoEventoTO extends SuperTO {

    private NegociacaoEventoTO negociacaoEvento;
    private Date dataDevolucao;
    private Usuario responsavel;
    private Double valor;

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataDevolucao() {
        return dataDevolucao;
    }

    public void setDataDevolucao(Date dataDevolucao) {
        this.dataDevolucao = dataDevolucao;
    }

    public NegociacaoEventoTO getNegociacaoEvento() {
        return negociacaoEvento;
    }

    public void setNegociacaoEvento(NegociacaoEventoTO negociacaoEvento) {
        this.negociacaoEvento = negociacaoEvento;
    }

    public Usuario getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Usuario responsavel) {
        this.responsavel = responsavel;
    }

    
}
