/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;

import annotations.arquitetura.ChavePrimaria;

/**
 * <AUTHOR>
 */
public class PerfilEventoProdutoTO implements Serializable, Cloneable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 922645327413339876L;

	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private Double valor;
	private Integer codigoProduto;
	private String descricaoProduto;
	private Integer quantidade;

	
	public PerfilEventoProdutoTO clone() throws CloneNotSupportedException{
		return (PerfilEventoProdutoTO) super.clone();
	}
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo valor.
	 */
	public Double getValor() {
		return this.valor;
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValor(final Double valor) {
		this.valor = valor;
	}

	/**
	 * @return O campo codigoProduto.
	 */
	public Integer getCodigoProduto() {
		return this.codigoProduto;
	}

	/**
	 * @param codigoProduto
	 *            O novo valor de codigoProduto.
	 */
	public void setCodigoProduto(final Integer codigoProduto) {
		this.codigoProduto = codigoProduto;
	}

	/**
	 * @return O campo descricaoProduto.
	 */
	public String getDescricaoProduto() {
		return this.descricaoProduto;
	}

	/**
	 * @param descricaoProduto
	 *            O novo valor de descricaoProduto.
	 */
	public void setDescricaoProduto(final String descricaoProduto) {
		this.descricaoProduto = descricaoProduto;
	}

	/**
	 * @return O campo quantidade.
	 */
	public Integer getQuantidade() {
		return this.quantidade;
	}

	/**
	 * @param quantidade
	 *            O novo valor de quantidade.
	 */
	public void setQuantidade(final Integer quantidade) {
		this.quantidade = quantidade;
	}

}
