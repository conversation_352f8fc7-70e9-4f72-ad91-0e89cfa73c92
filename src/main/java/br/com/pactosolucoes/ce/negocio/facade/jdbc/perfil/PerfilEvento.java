package br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.perfil.PerfilEventoInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PerfilEventoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PerfilEventoVO</code>. Encapsula toda a
 * interação com o banco de dados.
 * 
 * @see SuperEntidade
 * <AUTHOR>
 */
public class PerfilEvento extends CEDao implements PerfilEventoInterfaceFacade {

	/**
	 * Cosntrutor padrão da classe
	 * 
	 * @throws Exception
	 */
	public PerfilEvento() throws Exception {
		super();
		this.setIdEntidade("perfilevento");
	}

	// TODO: paginar consulta!!!
	@SuppressWarnings("unchecked")
	public List<PerfilEventoTO> consultar(final PerfilEventoTO filtro, final Boolean verificarAcesso) throws Exception {
//		this.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT DISTINCT PE.codigo, PE.descricao, PE.datainicio,PE.datatermino, PE.textopadrao, PE.permiteoutrosbensconsumo,"); 
        sql.append(" PE.permiteoutrosutensilios, PE.permiteoutrosbrinquedos, PE.exigecontratoassinado,");
        sql.append(" PE.pagamentoprevio, PE.produto, PE.permiteoutrosservicos \n");
        sql.append("FROM perfilevento PE \n");
		sql.append("LEFT JOIN perfileventoambiente PEA ON PE.codigo = PEA.perfilevento \n");
		sql.append("LEFT JOIN ambiente A ON PEA.ambiente = A.codigo \n");

		if (filtro != null) {
			sql.append("WHERE 1 = 1 \n");

			if ((filtro.getCodigo() != null) && !filtro.getCodigo().equals(0)) {
				sql.append("AND PE.codigo = ? \n");
			}
			if ((filtro.getDescricao() != null) && !filtro.getDescricao().equals("")) {
				sql.append("AND UPPER(PE.descricao) LIKE ? \n");
			}
			if ((filtro.getDescricaoAmbiente() != null) && !filtro.getDescricaoAmbiente().equals("")) {
				sql.append("AND UPPER(A.descricao) LIKE ? \n");
			}
			if (filtro.getDataInicio() != null) {
				sql.append("AND PE.datainicio >= ? \n");
			}
			if (filtro.getDataTermino() != null) {
				sql.append("AND PE.datatermino <= ? \n");
			}
		}
		sql.append("ORDER BY PE.descricao");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		int numParam = 0;

		if (filtro != null) {
			if ((filtro.getCodigo() != null) && !filtro.getCodigo().equals(0)) {
				dc.setInt(++numParam, filtro.getCodigo());
			}
			if ((filtro.getDescricao() != null) && !filtro.getDescricao().equals("")) {
				dc.setString(++numParam, filtro.getDescricao().toUpperCase() + "%");
			}
			if ((filtro.getDescricaoAmbiente() != null) && !filtro.getDescricaoAmbiente().equals("")) {
				dc.setString(++numParam, filtro.getDescricaoAmbiente().toUpperCase() + "%");
			}
			if (filtro.getDataInicio() != null) {
				dc.setDate(++numParam, new java.sql.Date(Uteis.getDataComHoraZerada(filtro.getDataInicio()).getTime()));
			}
			if (filtro.getDataTermino() != null) {
				dc.setDate(++numParam, new java.sql.Date(Uteis.getDateTime(filtro.getDataTermino(), 23, 59, 59).getTime()));
			}
		}

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}

	@SuppressWarnings("unchecked")
	public PerfilEventoTO obter(final Integer codigo) throws Exception {
		//this.consultar(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT PE.codigo, PE.descricao, PE.datainicio,PE.datatermino, PE.textopadrao, PE.permiteoutrosbensconsumo,"); 
        sql.append(" PE.permiteoutrosutensilios, PE.permiteoutrosbrinquedos, PE.exigecontratoassinado,");
        sql.append(" PE.pagamentoprevio, PE.produto, PE.permiteoutrosservicos \n");
        sql.append("FROM perfilevento PE \n");
		sql.append("WHERE PE.codigo = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigo);

		ResultSet tabelaResultado = dc.executeQuery();
		List<PerfilEventoTO> result = this.montarDadosConsulta(tabelaResultado);

		return result.isEmpty() ? null : result.get(0);
	}

	public ResultSet all() throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT PE.codigo, PE.descricao, PE.datainicio,PE.datatermino, PE.textopadrao, PE.permiteoutrosbensconsumo,");
		sql.append(" PE.permiteoutrosutensilios, PE.permiteoutrosbrinquedos, PE.exigecontratoassinado,");
		sql.append(" PE.pagamentoprevio, PE.produto, PE.permiteoutrosservicos \n");
		sql.append("FROM perfilevento PE ");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		return dc.executeQuery();
	}
	/**
	 * Responsável por obter o nome do perfil do evento
	 * <AUTHOR>
	 * 11/03/2011
	 * @param codigo
	 * @return
	 * @throws Exception
	 */
	public String obterNomePerfil(Integer codigo) throws Exception{
		String nomePerfil = "";
		String sql = "SELECT descricao FROM perfilevento WHERE codigo = ?";
		Declaracao dc = new Declaracao(sql, con);
		dc.setInt(1, codigo);
		ResultSet rs = dc.executeQuery();
		if(rs.next()){
			nomePerfil = rs.getString("descricao");	
		}
		return nomePerfil;
		
	}

	public void incluir(final PerfilEventoTO perfil) throws Exception {
//		super.incluirObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO perfilevento (descricao, datainicio, datatermino, produto, "
				+ "textopadrao, permiteoutrosbensconsumo, permiteoutrosutensilios, permiteoutrosbrinquedos, permiteoutrosservicos,"
				+ "exigecontratoassinado, pagamentoprevio) \n");
		
		sql.append("VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )");
		
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int numParam = 0;
		dc.setString(++numParam, perfil.getDescricao());
		dc.setDate(++numParam, new java.sql.Date(perfil.getDataInicio().getTime()));
		dc.setDate(++numParam, new java.sql.Date(perfil.getDataTermino().getTime()));
		dc.setInt(++numParam, perfil.getProduto().getCodigo());
		dc.setString(++numParam, perfil.getTextoPadrao());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosBensConsumo());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosUtensilios());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosBrinquedos());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosServicos());
		dc.setBoolean(++numParam, perfil.getExigeContratoAssinado());
		dc.setDouble(++numParam, perfil.getPagamentoPrevio());
		
		dc.execute();
		perfil.setCodigo(this.obterValorChavePrimariaCodigo());
	}

	public void alterar(final PerfilEventoTO perfil) throws Exception {
//		super.alterarObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE perfilevento SET descricao = ?, datainicio = ?, datatermino = ?, produto = ?, "
				+ "textopadrao = ?, permiteoutrosbensconsumo = ?, permiteoutrosutensilios = ?, permiteoutrosbrinquedos = ?, "
				+ "permiteoutrosservicos = ?, exigecontratoassinado = ?, pagamentoprevio = ? \n");
		
		sql.append("WHERE codigo = ?");	

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int numParam = 0;
		dc.setString(++numParam, perfil.getDescricao());
		dc.setDate(++numParam, new java.sql.Date(perfil.getDataInicio().getTime()));
		dc.setDate(++numParam, new java.sql.Date(perfil.getDataTermino().getTime()));
		dc.setInt(++numParam, perfil.getProduto().getCodigo());
		dc.setString(++numParam, perfil.getTextoPadrao());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosBensConsumo());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosUtensilios());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosBrinquedos());
		dc.setBoolean(++numParam, perfil.getPermiteOutrosServicos());
		dc.setBoolean(++numParam, perfil.getExigeContratoAssinado());
		dc.setDouble(++numParam, perfil.getPagamentoPrevio());
		
		
		dc.setInt(++numParam, perfil.getCodigo());
		dc.execute();
	}

	public Boolean verificarExistenciaDeNegociacoes(final PerfilEventoTO perfil) throws Exception {
		StringBuilder sql = new StringBuilder();
		Boolean existe = Boolean.FALSE;
		sql.append("SELECT NPE.codigo, NPE.negociacaoevento, NPE.perfilevento \n");
		sql.append("FROM negociacaoperfilevento NPE \n");
		sql.append("WHERE NPE.perfilevento = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, perfil.getCodigo());

		ResultSet tabelaResultado = dc.executeQuery();
		if(tabelaResultado.next())
			existe = Boolean.TRUE;
		return existe;
	}

	public void excluir(final PerfilEventoTO perfil) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		Declaracao dc = new Declaracao("DELETE FROM perfilevento WHERE codigo = ?", this.con);
		dc.setInt(1, perfil.getCodigo());
		dc.execute();
	}
	

	/**
	 * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>) em um objeto da classe
	 * <code>PerfilEventoVO</code>.
	 * 
	 * @param dadosSQL
	 * @param nivelMontarDados
	 *            Nível de montagem dos dados resultados da consulta.
	 * @return O objeto da classe <code>PerfilEventoVO</code> com os dados devidamente montados.
	 * @throws Exception
	 */
	@Override
	public PerfilEventoTO montarDados(final ResultSet dadosSQL) throws SQLException {
		PerfilEventoTO obj = new PerfilEventoTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setDescricao(dadosSQL.getString("descricao"));
		obj.setDataInicio(dadosSQL.getDate("datainicio"));
		obj.setDataTermino(dadosSQL.getDate("datatermino"));
		obj.getProduto().setCodigo(dadosSQL.getInt("produto"));
		obj.setTextoPadrao(dadosSQL.getString("textopadrao"));
		obj.setPermiteOutrosBensConsumo(dadosSQL.getBoolean("permiteoutrosbensconsumo"));
		obj.setPermiteOutrosUtensilios(dadosSQL.getBoolean("permiteoutrosutensilios"));
		obj.setPermiteOutrosBrinquedos(dadosSQL.getBoolean("permiteoutrosbrinquedos"));
		obj.setPermiteOutrosServicos(dadosSQL.getBoolean("permiteoutrosservicos"));
		obj.setExigeContratoAssinado(dadosSQL.getBoolean("exigecontratoassinado"));
		obj.setPagamentoPrevio(dadosSQL.getDouble("pagamentoprevio"));
		
		return obj;
	}

	/**
	 * Método que retornará o codigo de um perfil de evento de um determinado evento
	 * 
	 * @param evento
	 *            - codigo do evento
	 * @return codigo do perfilevento
	 * @throws SQLException
	 * <AUTHOR>
	 */
	public Integer consultarPerfilEventoPorEvento(final Integer evento) throws SQLException {
		// montar a consulta
		StringBuilder sql = new StringBuilder();
		sql
				.append("select perfilevento from negociacaoperfilevento where negociacaoevento = (select max(codigo) from negociacaoevento where eventointeresse=?)");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, evento);
		// executar a consulta
		ResultSet rs = dc.executeQuery();
		rs.next();
		// retornar o resultado
		return rs.getInt("perfilevento");
	}
	
	/**
	 * <AUTHOR>
	 * @param codigoAmbiente
	 * @param data
	 * @return lista de nomes de perfis que contenham ambiente passado como parametro e estejam situados na data parametro
	 * @throws Exception
	 */
	public List<String> listarPerfisPorAmbiente(Integer codigoAmbiente, Date data) throws Exception{
		List<String> nomesPerfis = new ArrayList<String>();
		StringBuilder sql = new StringBuilder();
		//selecionar a descricao do perfil
		sql.append("SELECT PE.descricao FROM perfilevento PE ");
		//de todos os perfis de eventos relacionados com um ambiente 
		sql.append("INNER JOIN perfileventoambiente PEA ON  PE.codigo = PEA.perfilevento ");
		sql.append("INNER JOIN ambiente A ON A.codigo = PEA.ambiente ");
		//onde o ambiente tenha codigo ? e o perfil esteja contido na data ?
		sql.append("WHERE A.codigo = ? AND PE.datainicio <= ? AND PE.datatermino >= ?");
		int i = 0;
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(++i, codigoAmbiente);
		dc.setDate(++i, new java.sql.Date(data.getTime()));
		dc.setDate(++i, new java.sql.Date(data.getTime()));
		ResultSet eventos = dc.executeQuery();
		while(eventos.next()){
			nomesPerfis.add(eventos.getString("descricao"));
		}
		return nomesPerfis;
		}

}
