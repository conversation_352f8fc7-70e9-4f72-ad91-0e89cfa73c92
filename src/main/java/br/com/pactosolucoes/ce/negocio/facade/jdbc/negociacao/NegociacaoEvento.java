/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.ce.comuns.to.ModeloContratoTO;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoTO;
import br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.negociacao.NegEvPerfilEventoAmbienteInterfaceFacade;
import br.com.pactosolucoes.ce.negocio.interfaces.negociacao.NegociacaoEventoInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;
import java.sql.Connection;
import java.sql.PreparedStatement;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>InteressadoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 * 
 * @see CEDao
 * @see NegEvPerfilEventoAmbienteInterfaceFacade
 * <AUTHOR>
 */
public class NegociacaoEvento extends CEDao implements NegociacaoEventoInterfaceFacade {

	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public NegociacaoEvento() throws Exception {
		super();
	}

	public NegociacaoEvento(Connection con) throws Exception {
		super(con);
	}
	/**
	 * Responsável por alterar a flag reserva da negociacao  
	 * <AUTHOR> 
	 * 16/02/2011
	 * @param reserva
	 * @param codigoNegociacao
	 * @throws Exception
	 */
	public void alterarReserva(Boolean reserva, Integer codigoNegociacao) throws Exception{
//		this.alterarObj(this.getIdEntidade());
		String sql = "UPDATE negociacaoevento SET eventoreserva = ? WHERE codigo = ?";
		Declaracao dc = new Declaracao(sql, con);
		int i = 0;
		dc.setBoolean(++i, reserva);
		dc.setInt(++i, codigoNegociacao);
		dc.execute();
	}
	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacio.NegociacaoEventoInterfaceFacade#consultarPorEventoInteresse(br.com.pactosolucoes.ce.negocio.evento.NegociacaoEventoTO)
	 */
	@SuppressWarnings("unchecked")
	public NegociacaoEventoTO consultarPorEventoInteresse(final Integer codigoEventoInteresse) throws Exception {
//		this.consultarObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT NE.*, NPE.perfilevento, U.nome as usuario \n");
		sql.append("FROM negociacaoevento NE \n");
		sql.append("INNER JOIN negociacaoperfilevento NPE ON NE.codigo = NPE.negociacaoevento \n");
		sql.append("INNER JOIN usuario U ON U.codigo = NE.usuariocadastro \n");
		sql.append("WHERE NE.eventointeresse = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoEventoInteresse);

		ResultSet tabelaResultado = dc.executeQuery();
		List<NegociacaoEventoTO> result = (this.montarDadosConsulta(tabelaResultado));
		return result.isEmpty() ? null : result.get(0);
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacio.NegociacaoEventoInterfaceFacade#incluir(br.com.pactosolucoes.ce.negocio.evento.NegociacaoEventoTO)
	 */
	public void incluir(final NegociacaoEventoTO negociacao, final Integer codigoUsuarioCadastro) throws Exception {
//		super.incluirObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoevento (datacadastro, dataevento, textopredefinido, textolivre, "
				+ "desconto, tipodesconto, valortotal, situacao, usuariocadastro, eventointeresse, horarioinicial, "
				+ "horariofinal, horariofinalexibicao, eventoreserva, empresa) \n");
		sql.append("VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setDate(1, new java.sql.Date(negociacao.getDataCadastro().getTime()));
		dc.setDate(2, new java.sql.Date(negociacao.getDataEvento().getTime()));
		dc.setString(3, negociacao.getTextoPredefinido());
		dc.setString(4, negociacao.getTextoLivre());
		dc.setDouble(5, negociacao.getDesconto());
		if (negociacao.getTipoDesconto() != null) {
			dc.setInt(6, negociacao.getTipoDesconto());
		} else {
			dc.setInt(6, 0);
		}
		dc.setDouble(7, negociacao.getValorTotal());
		dc.setInt(8, negociacao.getSituacao().getCodigo());
		dc.setInt(9, codigoUsuarioCadastro);
		dc.setInt(10, negociacao.getCodigoEventoInteresse());
		dc.setTimestamp(11, new java.sql.Timestamp(negociacao.getHorarioInicial().getTime()));
		dc.setTimestamp(12, new java.sql.Timestamp(negociacao.getHorarioFinal().getTime()));
		dc.setTimestamp(13, new java.sql.Timestamp(negociacao.getHorarioFinalExibicao().getTime()));
		dc.setBoolean(14, negociacao.getEventoReserva());
		dc.setInt(15, negociacao.getEmpresa().getCodigo());
		dc.execute();
		negociacao.setCodigo(this.obterValorChavePrimariaCodigo());

		sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoperfilevento (negociacaoevento, perfilevento) \n");
		sql.append("VALUES (?, ?)");

		dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, negociacao.getCodigo());
		dc.setInt(2, negociacao.getPerfilEventoTO().getCodigo());

		dc.execute();
	}

	public Double obterValorNegociacao(Integer codigoNegociacao) throws Exception {
		Double valor = null;
		String sql = "SELECT valortotal FROM negociacaoevento WHERE codigo = ? ";
		Declaracao dc = new Declaracao(sql, con);
		dc.setInt(1, codigoNegociacao);
		ResultSet rs = dc.executeQuery();
		if (rs.next())
			valor = rs.getDouble("valortotal");

		return valor;

	}
	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacio.NegociacaoEventoInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.NegociacaoEventoTO)
	 */
	public void alterar(final NegociacaoEventoTO negociacao, final Integer codigoUsuarioCadastro) throws Exception {
//		this.alterarObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE negociacaoevento SET datacadastro = ?, dataevento = ?, textopredefinido = ?, textolivre = ?, "
				+ "desconto = ?, tipodesconto = ?, valortotal = ?, situacao = ?, usuariocadastro = ?, eventointeresse = ?, "
				+ "horarioinicial = ?, horariofinal = ?, horariofinalexibicao = ?, eventoreserva = ?, empresa = ? ");
		sql.append("WHERE codigo = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setDate(1, new java.sql.Date(negociacao.getDataCadastro().getTime()));
		dc.setDate(2, new java.sql.Date(negociacao.getDataEvento().getTime()));
		dc.setString(3, negociacao.getTextoPredefinido());
		dc.setString(4, negociacao.getTextoLivre());
		dc.setDouble(5, negociacao.getDesconto());
		if (negociacao.getTipoDesconto() != null) {
			dc.setInt(6, negociacao.getTipoDesconto());
		} else {
			dc.setInt(6, 0);
		}
		dc.setDouble(7, negociacao.getValorTotal());
		dc.setInt(8, negociacao.getSituacao().getCodigo());
		dc.setInt(9, codigoUsuarioCadastro);
		dc.setInt(10, negociacao.getCodigoEventoInteresse());
		dc.setTimestamp(11, new java.sql.Timestamp(negociacao.getHorarioInicial().getTime()));
		dc.setTimestamp(12, new java.sql.Timestamp(negociacao.getHorarioFinal().getTime()));
		dc.setTimestamp(13, new java.sql.Timestamp(negociacao.getHorarioFinalExibicao().getTime()));
		dc.setBoolean(14, negociacao.getEventoReserva());
		dc.setInt(15, negociacao.getEmpresa().getCodigo());
		dc.setInt(16, negociacao.getCodigo());
		

		dc.execute();
		
		sql = new StringBuilder();
		sql.append("UPDATE negociacaoperfilevento SET perfilevento = ?  \n");
		sql.append("WHERE negociacaoevento = ?");

		dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, negociacao.getPerfilEventoTO().getCodigo());
		dc.setInt(2, negociacao.getCodigo());

		dc.execute();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacio.NegociacaoEventoInterfaceFacade#excluir(br.com.pactosolucoes.ce.negocio.evento.NegociacaoEventoTO)
	 */
	public void excluir(final NegociacaoEventoTO negociacao) throws Exception {
//		super.excluirObj(this.getIdEntidade());

		Declaracao dc = new Declaracao("DELETE FROM negociacaoperfilevento WHERE negociacaoevento = ?", this.con);
		dc.setInt(1, negociacao.getCodigo());
		dc.execute();

		dc = new Declaracao("DELETE FROM negociacaoevento WHERE codigo = ?", this.con);
		dc.setInt(1, negociacao.getCodigo());
		dc.execute();
	}


	public void salvarRegistroImpressaoContrato(Integer codigoNegociacao, Integer codigoModeloContrato) throws Exception{
		ResultSet rs = consultarRegistroContrato(codigoNegociacao, codigoModeloContrato);
		
		if(rs.next()){
			this.alterarRegistroImpressaoContrato(codigoNegociacao, codigoModeloContrato);
		} else {
			this.incluirRegistroImpressaoContrato(codigoNegociacao, codigoModeloContrato);
		}
	}
	public void salvarRegistroEnvioContrato(Integer codigoNegociacao, Integer codigoModeloContrato) throws Exception{
		ResultSet rs = consultarRegistroContrato(codigoNegociacao, codigoModeloContrato);
		
		if(rs.next()){
			this.alterarRegistroEnvioContrato(codigoNegociacao, codigoModeloContrato);
		} else {
			this.incluirRegistroEnvioContrato(codigoNegociacao, codigoModeloContrato);
		}
	}
	public void salvarRegistroEnvioOrcamento(Integer codigoNegociacao, Integer codigoModeloContrato) throws Exception{
		ResultSet rs = consultarRegistroOrcamento(codigoNegociacao, codigoModeloContrato);
		
		if(rs.next()){
			this.alterarRegistroEnvioOrcamento(codigoNegociacao, codigoModeloContrato);
		} else {
			this.incluirRegistroEnvioOrcamento(codigoNegociacao, codigoModeloContrato);
		}
	}

	private ResultSet consultarRegistroContrato(Integer codigoNegociacao, Integer codigoModeloContrato) throws Exception {
		//this.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo FROM negociacaoeventoimpressaocontrato WHERE negociacaoevento = ? AND contrato = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoNegociacao);
		dc.setInt(2, codigoModeloContrato);
		
		ResultSet rs = dc.executeQuery();
		return rs;
	}
	public void salvarRegistroImpressaoOrcamento(Integer codigoNegociacao, Integer codigoModeloOrcamento) throws Exception{
		ResultSet rs = consultarRegistroOrcamento(codigoNegociacao, codigoModeloOrcamento);
		
		if(rs.next()){
			this.alterarRegistroImpressaoOrcamento(codigoNegociacao, codigoModeloOrcamento);
		} else {
			this.incluirRegistroImpressaoOrcamento(codigoNegociacao, codigoModeloOrcamento);
		}
	}

	private ResultSet consultarRegistroOrcamento(Integer codigoNegociacao, Integer codigoModeloOrcamento) throws Exception {
		//this.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo FROM negociacaoeventoimpressaoorcamento WHERE negociacaoevento = ? AND orcamento = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoNegociacao);
		dc.setInt(2, codigoModeloOrcamento);
		
		ResultSet rs = dc.executeQuery();
		return rs;
	}
	private void incluirRegistroImpressaoContrato(final Integer codigoNegociacao, final Integer codigoModeloContrato) throws Exception {
//		super.incluir(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoeventoimpressaocontrato (negociacaoevento, contrato, data) \n");
		sql.append("VALUES (?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoNegociacao);
		dc.setInt(2, codigoModeloContrato);
		dc.setTimestamp(3, new java.sql.Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));

		dc.execute();
	}
	/**
	 * Responsável por persistir dados do envio de um contrato via e-mail
	 * <AUTHOR>
	 * 10/03/2011
	 * @param codigoNegociacao
	 * @param codigoModeloContrato
	 * @throws Exception
	 */
	private void incluirRegistroEnvioContrato(final Integer codigoNegociacao, final Integer codigoModeloContrato) throws Exception {
//		super.incluir(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoeventoimpressaocontrato (negociacaoevento, contrato, data, dataultimoenvio) ");
		sql.append("VALUES (?, ?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		int i = 0;
		dc.setInt(++i, codigoNegociacao);
		dc.setInt(++i, codigoModeloContrato);
		dc.setTimestamp(++i, new java.sql.Timestamp(Calendario.hoje().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(Calendario.hoje().getTime()));

		dc.execute();
	}
	/**
	 * Responsável por persistir dados do envio de um orcamento via e-mail
	 * <AUTHOR>
	 * 10/03/2011
	 * @param codigoNegociacao
	 * @param codigoModeloContrato
	 * @throws Exception
	 */
	private void incluirRegistroEnvioOrcamento(final Integer codigoNegociacao, final Integer codigoModeloContrato) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoeventoimpressaoorcamento (negociacaoevento, orcamento, data, dataultimoenvio) ");
		sql.append("VALUES (?, ?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setInt(++i, codigoNegociacao);
		dc.setInt(++i, codigoModeloContrato);
		dc.setTimestamp(++i, new java.sql.Timestamp(Calendario.hoje().getTime()));
		dc.setTimestamp(++i, new java.sql.Timestamp(Calendario.hoje().getTime()));

		dc.execute();
	}
	
	public void excluirRegistroImpressaoContrato(final Integer codigoNegociacao) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM negociacaoeventoimpressaocontrato WHERE negociacaoevento = ?\n");
		
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoNegociacao);
		
		
		dc.execute();
	}
	
	/**
	 * Responsável por alterar dados persistidos do envio de um contrato via e-mail
	 * <AUTHOR>
	 * 10/03/2011
	 * @param codigoNegociacao
	 * @param codigoModeloContrato
	 * @throws Exception
	 */
	private void alterarRegistroImpressaoContrato (final Integer codigoNegociacao, final Integer codigoModeloContrato) throws Exception {
//		this.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE negociacaoeventoimpressaocontrato SET data = ? ");
		sql.append("WHERE contrato = ? AND negociacaoevento = ? ");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setTimestamp(1, new java.sql.Timestamp(Calendario.hoje().getTime()));
		dc.setInt(2, codigoModeloContrato);
		dc.setInt(3, codigoNegociacao);
		dc.execute();
	}

	private void alterarRegistroEnvioContrato (final Integer codigoNegociacao, final Integer codigoModeloContrato) throws Exception {
//		this.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE negociacaoeventoimpressaocontrato SET dataultimoenvio = ? ");
		sql.append("WHERE contrato = ? AND negociacaoevento = ? ");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setTimestamp(1, new java.sql.Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));
		dc.setInt(2, codigoModeloContrato);
		dc.setInt(3, codigoNegociacao);
		dc.execute();
	}
	private void alterarRegistroEnvioOrcamento (final Integer codigoNegociacao, final Integer codigoModeloContrato) throws Exception {
//		this.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE negociacaoeventoimpressaoorcamento SET dataultimoenvio = ? ");
		sql.append("WHERE orcamento = ? AND negociacaoevento = ? ");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setTimestamp(1, new java.sql.Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));
		dc.setInt(2, codigoModeloContrato);
		dc.setInt(3, codigoNegociacao);
		dc.execute();
	}
	
	public ModeloContratoTO consultarEnvioContrato(final Integer codigo) throws Exception {
//		this.consultarObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT dataultimoenvio, codigo FROM negociacaoeventoimpressaocontrato WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigo);

		ResultSet tabelaResultado = dc.executeQuery();
		tabelaResultado.next();
		return (ModeloContratoTO) this.montarDadosConsultaEnvio(tabelaResultado);
	}

	public void incluirRegistroImpressaoOrcamento(final Integer codigoNegociacao, final Integer codigoModeloOrcamento) throws Exception {
//		super.incluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoeventoimpressaoorcamento (negociacaoevento, orcamento, data) ");
		sql.append("VALUES (?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoNegociacao);
		dc.setInt(2, codigoModeloOrcamento);
		dc.setTimestamp(3, new java.sql.Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));

		dc.execute();
	}
	public void excluirRegistroImpressaoOrcamento(final Integer codigoNegociacao) throws Exception {
//		super.excluirObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM negociacaoeventoimpressaoorcamento WHERE negociacaoevento = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigoNegociacao);
		dc.execute();
	}

	public void alterarRegistroImpressaoOrcamento (final Integer codigoNegociacao, final Integer codigoModeloOrcamento) throws Exception {
//		super.alterarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE negociacaoeventoimpressaoorcamento SET data = ? ");
		sql.append("WHERE orcamento = ? AND negociacaoevento = ? ");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setTimestamp(1, new java.sql.Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));
		dc.setInt(2, codigoModeloOrcamento);
		dc.setInt(3, codigoNegociacao);
		dc.execute();
	}
	
	
	public ModeloContratoTO montarDadosEnvio(final ResultSet dadosSQL) throws Exception {
		ModeloContratoTO obj = new ModeloContratoTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
//		obj.setDataModeloContrato(dadosSQL.getDate("dataultimoenvio"));
		return obj;
	}
	/**
	 * (non-Javadoc)
	 * 
	 * @see br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao#montarDados(java.sql.ResultSet)
	 */
	@Override
	public NegociacaoEventoTO montarDados(final ResultSet dadosSQL) throws Exception {
		NegociacaoEventoTO obj = new NegociacaoEventoTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setDataCadastro(dadosSQL.getDate("datacadastro"));
		obj.setDataEvento(dadosSQL.getDate("dataevento"));
		obj.setTextoPredefinido(dadosSQL.getString("textopredefinido"));
		obj.setTextoLivre(dadosSQL.getString("textolivre"));
		obj.setDesconto(dadosSQL.getDouble("desconto"));
		obj.setValorTotal(dadosSQL.getDouble("valortotal"));
		obj.setTipoDesconto(dadosSQL.getInt("tipodesconto"));
		obj.setSituacao(Situacao.getSituacao(dadosSQL.getInt("situacao")));
		obj.setCodigoEventoInteresse(dadosSQL.getInt("eventointeresse"));
		obj.getPerfilEventoTO().setCodigo(dadosSQL.getInt("perfilevento"));
		obj.setHorarioInicial(dadosSQL.getTimestamp("horarioinicial"));
		obj.setHorarioFinal(dadosSQL.getTimestamp("horariofinal"));
		obj.setHorarioFinalExibicao(dadosSQL.getTimestamp("horariofinalexibicao"));
		obj.getAtendente().setNome(dadosSQL.getString("usuario"));
		obj.setEventoReserva(dadosSQL.getBoolean("eventoreserva"));
		obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
		return obj;
		
	}

	/**
	 * Método responsável por obter o código do eevnto interesse de uma determinada negociacao
	 * 
	 * @param codigoNegociacao
	 *            - o código da negociação que será consultada
	 * @return o código do eventointeresse
	 * @throws SQLException
	 */
	public Integer obterCodigoEventoInteresse(final Integer codigoNegociacao) throws SQLException {
		// montar a string para consulta
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT eventointeresse from negociacaoevento where codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoNegociacao);
		// executar a consulta
		ResultSet tabelaResultado = dc.executeQuery();
		tabelaResultado.next();
		// retornar o resultado
		return tabelaResultado.getInt("eventointeresse");
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacio.NegociacaoEventoInterfaceFacade#consultarValorFinalEvento(br.com.pactosolucoes.ce.negocio.evento.NegociacaoEventoTO)
	 */
	public Double consultarValorFinalEvento(final EventoInteresseVO evento) throws Exception {
//		super.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT NE.valortotal, NE.desconto \n");
		sql.append("FROM negociacaoevento NE \n");
		sql.append("WHERE NE.eventointeresse = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, evento.getCodigo());
		ResultSet tabelaResultado = dc.executeQuery();

		Double valorFinal = null;
		if (tabelaResultado.next()) {
			valorFinal = tabelaResultado.getDouble("valortotal") - tabelaResultado.getDouble("desconto");
		}

		return valorFinal;
	}

	/**
	 * Pega somente a data do evento
	 * @param codigoEvento
	 * @return data do evento
	 * @throws Exception
	 * <AUTHOR>
	 */
	public Date getDataEvento(Integer codigoEvento) throws Exception{
	
		Date data = null;
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT dataevento FROM negociacaoevento WHERE eventointeresse = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(1, codigoEvento);
		ResultSet rs = dc.executeQuery();
		if(rs.next()){
			data = rs.getDate("dataevento");
		}
		return data;
	}

	
	/**
	 * @param tabelaResultado
	 * @return
	 * @throws Exception
	 */
	public List montarDadosConsultaEnvio(final ResultSet tabelaResultado) throws Exception {
		final List vetResultado = new ArrayList();
		while (tabelaResultado.next()) {
			final Object obj = this.montarDadosEnvio(tabelaResultado);
			vetResultado.add(obj);
		}
		return vetResultado;
	}

    public void excluirPagamentoSemCommit(MovPagamentoVO obj) throws Exception {
        try {
            String sql = "DELETE FROM negociacaoeventocontratopagamento WHERE movpagamento = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirReciboSemCommit(ReciboPagamentoVO obj) throws Exception {
        try {
            String sql = "DELETE FROM negociacaoeventocontratorecibo WHERE recibo = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }
}
