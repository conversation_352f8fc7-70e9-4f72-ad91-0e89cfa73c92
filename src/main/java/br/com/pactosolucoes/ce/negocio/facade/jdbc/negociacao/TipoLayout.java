/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.AmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.TipoLayoutTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.negociacao.TipoLayoutInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>InteressadoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 * 
 * @see CEDao
 * @see TipoLayoutInterfaceFacade
 * <AUTHOR>
 */
public class TipoLayout extends CEDao implements TipoLayoutInterfaceFacade {
	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public TipoLayout() throws Exception {
		super();
		setIdEntidade("NegociacaoEvento");
	}

	@SuppressWarnings("unchecked")
	public List<TipoLayoutTO> consultarPorAmbiente(final AmbienteTO ambiente) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT TL.*, A.descricao AS descricaoAmbiente \n");
		sql.append("FROM tipolayout TL \n");
		sql.append("INNER JOIN ambiente A ON TL.ambiente = A.codigo \n");
		sql.append("WHERE TL.ambiente = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, ambiente.getCodigo());

		ResultSet tabelaResultado = dc.executeQuery();
		return this.montarDadosConsulta(tabelaResultado);
	}

	/**
	 * Monta um objeto <code>TipoLayoutTO</code> a partir do <code>ResultSet</code> de um tipo de layout.
	 * 
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>TipoLayoutTO</code>.
	 * @throws Exception
	 */
	@Override
	public TipoLayoutTO montarDados(final ResultSet dadosSQL) throws SQLException {
		TipoLayoutTO obj = new TipoLayoutTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setDescricao(dadosSQL.getString("descricao"));
		obj.setCodigoAmbiente(dadosSQL.getInt("ambiente"));
		obj.setDescricaoAmbiente(dadosSQL.getString("descricaoAmbiente"));
		return obj;
	}

}
