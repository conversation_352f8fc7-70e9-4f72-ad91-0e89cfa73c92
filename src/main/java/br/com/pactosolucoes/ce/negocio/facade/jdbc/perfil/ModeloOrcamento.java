/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil;

import java.io.File;
import java.io.FileOutputStream;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.ModeloOrcamentoTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.FileUtilities;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>InteressadoVO</code>. Responsável por
 * implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 * 
 * @see CEDao
 * @see ModeloOrcamentoInterfaceFacade
 * <AUTHOR>
 * 
 */
public class ModeloOrcamento extends CEDao implements ModeloOrcamentoInterfaceFacade {

	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public ModeloOrcamento() throws Exception {
		super();
		this.setIdEntidade("PerfilEvento");
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#consultarPorPerfilEvento(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	
	public List<ModeloOrcamentoTO> consultarPorPerfilENegociacao(final Integer codigoPerfilEvento, final Integer codigoNegociacao) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT MC.codigo AS codigo, ");
		sql.append("MC.descricao AS descricao, ");
		sql.append("MC.nomearquivo AS nomearquivo, ");
		sql.append("MC.arquivo AS arquivo, ");
		sql.append("IMP.data AS dataImpressao, ");
		sql.append("IMP.dataultimoenvio AS dataEnvio ");
		sql.append("FROM perfileventomodeloorcamento MC ");
		//isso irá trazer as impressões caso elas existam,
		//se não existirem o registro ainda será consultado
		sql.append("LEFT JOIN (SELECT data,dataultimoenvio, orcamento FROM negociacaoeventoimpressaoorcamento WHERE negociacaoevento = ?) as IMP ");
		sql.append("ON MC.codigo = IMP.orcamento ");
		sql.append("WHERE MC.perfilevento = ? \n");
		sql.append("ORDER BY MC.nomearquivo");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setInt(++i, codigoNegociacao);
		dc.setInt(++i, codigoPerfilEvento);
		

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosImpressaoEnvio(tabelaResultado));
	}
	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#consultarPorPerfilEvento(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	@SuppressWarnings("unchecked")
	public List<ModeloOrcamentoTO> consultarPorPerfilEvento(final Integer codigoPerfilEvento) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT MC.codigo AS codigo, ");
		sql.append("MC.descricao AS descricao, ");
		sql.append("MC.nomearquivo AS nomearquivo, ");
		sql.append("MC.arquivo AS arquivo ");
		sql.append("FROM perfileventomodeloorcamento MC ");
		sql.append("WHERE MC.perfilevento = ? \n");
		sql.append("ORDER BY MC.nomearquivo");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		
		dc.setInt(++i, codigoPerfilEvento);
		

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#incluir(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	@Override
	public Integer incluir(final ModeloOrcamentoTO modeloOrcamento, final Integer codigoPerfilEvento) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO perfileventomodeloorcamento (perfilevento, arquivo, descricao, nomearquivo) \n");
		sql.append("VALUES (?, ?, ?, ?)");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoPerfilEvento);
		dc.setBytes(2, FileUtilities.obterBytesArquivo(modeloOrcamento.getArquivo()));
		dc.setString(3, modeloOrcamento.getDescricao());
		dc.setString(4, modeloOrcamento.getNomeArquivo());

		dc.execute();
		return this.obterValorChavePrimariaCodigo("perfileventomodeloorcamento");
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#alterar(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	public void alterar(final ModeloOrcamentoTO modeloOrcamento, final Integer codigoPerfilEvento) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE perfileventomodeloorcamento SET perfilevento = ?, arquivo = ?, descricao = ?, nomearquivo = ? \n");
		sql.append("WHERE codigo = ?");

		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoPerfilEvento);
		dc.setBytes(2, FileUtilities.obterBytesArquivo(modeloOrcamento.getArquivo()));
		dc.setString(3, modeloOrcamento.getDescricao());
		dc.setString(4, modeloOrcamento.getNomeArquivo());
		dc.setInt(5, modeloOrcamento.getCodigo());

		dc.execute();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#excluir(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	public void excluir(final List<Integer> codigos) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM perfileventomodeloorcamento WHERE codigo IN (");
		for (int i = 0; i < (codigos.size() - 1); i++) {
			sql.append(codigos.get(i) + ", ");
		}
		sql.append(codigos.get(codigos.size() - 1) + ")");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.execute();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#excluirPorPerfilEvento(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	public void excluirPorPerfilEvento(final Integer codigoPerfilEvento) throws Exception {
		Declaracao dc = new Declaracao("DELETE FROM perfileventomodeloorcamento WHERE perfilevento = ?", this.con);
		dc.setInt(1, codigoPerfilEvento);
		dc.execute();
	}

	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.perfil.ModeloOrcamentoInterfaceFacade#obter(br.com.pactosolucoes.ce.negocio.evento.ModeloOrcamentoTO)
	 */
	public ModeloOrcamentoTO obter(final Integer codigo) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, descricao, arquivo, nomearquivo FROM perfileventomodeloorcamento WHERE codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigo);

		ResultSet tabelaResultado = dc.executeQuery();
		tabelaResultado.next();
		return this.montarDados(tabelaResultado);
	}

	/**
	 * Monta um objeto <code>ModeloOrcamentoTO</code> a partir do <code>ResultSet</code> de um modelo de orcamento.
	 * 
	 * @param dadosSQL
	 *            <code>ResultSet</code> resultado de alguma query.
	 * @return Objeto <code>ModeloOrcamentoTO</code>.
	 * @throws Exception
	 */
	@Override
	public ModeloOrcamentoTO montarDados(final ResultSet dadosSQL) throws Exception {
		ModeloOrcamentoTO obj = new ModeloOrcamentoTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.setDescricao(dadosSQL.getString("descricao"));
		obj.setNomeArquivo(dadosSQL.getString("nomearquivo"));
		ResultSet resultSet = criarConsulta("SELECT COUNT(*) AS cont FROM negociacaoeventoimpressaoorcamento WHERE orcamento = "+obj.getCodigo(), con);
		resultSet.next();
		obj.setPossuiImpressao(resultSet.getInt("cont") > 0);

		byte[] bytesArquivo = dadosSQL.getBytes("arquivo");

		// Criar arquivo para manter o modelo de orcamento:
		// Caminho do arquivo
		String caminho;
		// Obter a pasta temporária do sistema
		String tmp = System.getProperty("java.io.tmpdir");
		// Caso o endereço do diretório não termine com um separador, adicionar
		if (File.separator.equals(tmp.substring(tmp.length() - 1))) {
			caminho = tmp + obj.getNomeArquivo();
		} else {
			caminho = tmp + File.separator + obj.getNomeArquivo();
		}
		// Criar o arquivo
		File arquivo = new File(caminho);
		// Escrever no arquivo os bytes guardados no banco
		FileOutputStream out = new FileOutputStream(arquivo);
		out.write(bytesArquivo);
		out.flush();
		out.close();

		obj.setArquivo(arquivo);

		
		return obj;
	}
	/**
	 * Responsável por montar dados de uma consulta com data de envio e impressao do orçamento
	 * <AUTHOR>
	 * 10/03/2011
	 * @param dadosSQL
	 * @return
	 * @throws Exception
	 */
	public List<ModeloOrcamentoTO> montarDadosImpressaoEnvio(final ResultSet dadosSQL) throws Exception{
		List<ModeloOrcamentoTO> orcamentos = new ArrayList<ModeloOrcamentoTO>();
		
		while(dadosSQL.next()){
			ModeloOrcamentoTO obj = montarDados(dadosSQL);
			//datas de impressão e envio
			obj.setDataEnvio(dadosSQL.getTimestamp("dataEnvio"));
			obj.setDataImpressao(dadosSQL.getTimestamp("dataImpressao"));
			orcamentos.add(obj);
		}
		return orcamentos;
		
	}

	/**
	 * Operação responsável por consultar por codigo BD um objeto da classe <code>ModeloOrcamentoTO</code>.
	 * 
	 * @param codigo
	 *            - inteiro que será consultado no banco de dados.
	 * @throws Execption.
	 */
	public ModeloOrcamentoTO consultarPorCodigo(final Integer codigo) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT MC.codigo AS codigo, ");
		sql.append("MC.descricao AS descricao, ");
		sql.append("MC.nomearquivo AS nomearquivo, ");
		sql.append("MC.arquivo AS arquivo, ");
		sql.append("IMP.data AS dataImpressao, ");
		sql.append("IMP.dataultimoenvio AS dataEnvio ");
		sql.append("FROM perfileventomodeloorcamento MC ");
		//isso irá trazer as impressões caso elas existam,
		//se não existirem o registro ainda será consultado
		sql.append("LEFT JOIN negociacaoeventoimpressaoorcamento IMP ");
		sql.append("ON MC.codigo = IMP.orcamento ");
		sql.append("WHERE MC.codigo = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		dc.setInt(1, codigo);

		ResultSet tabelaResultado = dc.executeQuery();
		tabelaResultado.next();
		return this.montarDados(tabelaResultado);
	}
	

}
