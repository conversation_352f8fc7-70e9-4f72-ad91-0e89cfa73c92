package br.com.pactosolucoes.ce.negocio.interfaces.evento;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import negocio.interfaces.basico.SuperInterface;

@SuppressWarnings("unchecked")
public interface TipoAmbienteInterfaceFacade extends SuperInterface{

	/**
	 * Operação responsável por obter no banco de dados um objeto da classe <code>TipoAmbiente</code>.
	 * 
	 * @param codigo
	 *            Objeto da classe <code>TipoAmbienteTO</code> que será obtido no banco de dados.
	 * @throws Exception
	 * @return caso seja vazio retorna nulo se não retorna o resultado da consulta
	 * <AUTHOR>
	 */
	TipoAmbienteTO obter(Integer codigo) throws Exception;

	/**
	 * Operação responsável por consultar no banco de dados um objeto da classe <code>TipoAmbiente</code>.
	 * 
	 * @param parametro
	 * @return montar dados da consulta
	 */
	List<TipoAmbienteTO> consultar() throws Exception;

	/**
	 * Operação responsável por consultar no banco de dados um objeto da classe <code>TipoAmbiente</code>.
	 * 
	 * @param parametro
	 * @return montar dados da consulta
	 */
	List<TipoAmbienteTO> consultarTA(TipoAmbienteTO tipoAmbiente) throws Exception;

	/**
	 * Operação responsável por consultar por codigo no banco de dados um objeto da classe <code>TipoAmbiente</code>.
	 * 
	 * @param valor
	 *            consulta valor da consulta que será usado para fazer a pesquisa
	 * @return montar dados da consulta
	 */
	public List consultarPorCodigo(Integer valorConsulta) throws Exception;

	/**
	 * Operação responsável por consultar por descrição no banco de dados um objeto da classe <code>TipoAmbiente</code>.
	 * 
	 * @param valor
	 *            consulta valor da consulta que será usado para fazer a pesquisa
	 * @return montar dados da consulta
	 */
	public List consultarPorDescricao(String valorConsulta) throws Exception;

	/**
	 * Operação responsável por consultar, se existe um tipo de negociação de ambiente no banco de dados, um objeto da classe
	 * <code>TipoAmbiente</code>.
	 * 
	 * @param valor
	 *            consulta valor da consulta que será usado para fazer a pesquisa
	 * @return caso exista retorna true se não retorna false
	 */
	public boolean consultarExisteNegociacaoAmbiente(Integer valorConsulta) throws Exception;

}
