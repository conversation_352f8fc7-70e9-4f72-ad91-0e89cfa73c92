package br.com.pactosolucoes.estudio.interfaces;

import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.estudio.modelo.RelatorioComissaoVO;
import java.sql.Date;
import java.sql.Time;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR> GeoInova Soluções
 */
public interface RelatorioComissaoInterfaceFacade extends SuperInterface{

    /**
     * Operação para buscar e calcular a comissão dos colaboradores
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return List<RelatorioComissaoVO> listarComissao
     * @throws Exception
     */
    public List<RelatorioComissaoVO> listarComissao(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal,
            List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus,
            List<Integer> listaTipoHorario,  SituacaoParcelaEnum situacaoParcela) throws Exception;
    /**
     * Operação para contar o número de clientes distintos
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return List<RelatorioComissaoVO> listarComissao
     * @throws Exception
     */
    Integer contagemClientes(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus, List<Integer> listaTipoHorario) throws Exception;

}
