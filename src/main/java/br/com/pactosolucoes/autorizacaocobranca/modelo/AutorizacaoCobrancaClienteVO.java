/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.modelo;

import annotations.arquitetura.ChaveEstrangeira;
import negocio.comuns.basico.AutorizacaoCobrancaClienteWS;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * <AUTHOR>
 */
public class AutorizacaoCobrancaClienteVO extends AutorizacaoCobrancaVO {

    @ChaveEstrangeira
    private ClienteVO cliente = new ClienteVO();

    private boolean gravarLogAlteracaoViaSite = false;
    private boolean gravarLogRegistroB = false;

    private boolean validarAutorizacaoCobrancaSemelhante = true; // atributo transient
    private Integer ordem;

    public AutorizacaoCobrancaClienteVO(boolean iniciarLog) {
        if (iniciarLog) {
            registrarObjetoVOAntesDaAlteracao();
        }
    }

    public AutorizacaoCobrancaClienteVO() {
    }

    public static void validarDados(AutorizacaoCobrancaClienteVO obj) throws Exception {
        validarDados(obj, true);
    }

    public static void validarDados(AutorizacaoCobrancaClienteVO obj, boolean validarCliente) throws Exception {
        AutorizacaoCobrancaVO.validarDados(obj);
        if (validarCliente && UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
            throw new ConsistirException("Cliente deve ser informado (Autorização Cobrança)");
        }
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AutorizacaoCobrancaVO auto = (AutorizacaoCobrancaVO) obj;
        final AutorizacaoCobrancaClienteVO other = (AutorizacaoCobrancaClienteVO) obj;
        return super.equals(auto) && !(this.cliente != other.cliente && (this.cliente == null || !(this.cliente.getCodigo().intValue() == other.cliente.getCodigo().intValue())));
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 47 * hash + (this.cliente != null ? this.cliente.hashCode() : 0);
        hash = 47 * hash + (super.getTipoAutorizacao() != null ? super.getTipoAutorizacao().hashCode() : 0);
        hash = 47 * hash + (super.getNumeroCartao() != null ? super.getNumeroCartao().hashCode() : 0);
        hash = 47 * hash + (super.getValidadeCartao() != null ? super.getValidadeCartao().hashCode() : 0);
        hash = 47 * hash + (super.getBanco() != null ? super.getBanco().hashCode() : 0);
        hash = 47 * hash + (super.getAgencia() != null ? super.getAgencia().hashCode() : 0);
        hash = 47 * hash + (super.getAgenciaDV() != null ? super.getAgenciaDV().hashCode() : 0);
        hash = 47 * hash + (super.getContaCorrente() != null ? super.getContaCorrente().hashCode() : 0);
        hash = 47 * hash + (super.getContaCorrenteDV() != null ? super.getContaCorrenteDV().hashCode() : 0);
        hash = 47 * hash + (super.getTipoACobrar() != null ? super.getTipoACobrar().hashCode() : 0);
        hash = 47 * hash + (super.getListaObjetosACobrar() != null ? super.getListaObjetosACobrar().hashCode() : 0);
        hash = 47 * hash + (super.getConvenio() != null ? super.getConvenio().hashCode() : 0);
        hash = 47 * hash + (super.getOperadoraCartao() != null ? super.getOperadoraCartao().hashCode() : 0);
        return hash;
    }

    public AutorizacaoCobrancaClienteWS toWS() {
        AutorizacaoCobrancaClienteWS autorizacaoCobrancaClienteWS = new AutorizacaoCobrancaClienteWS();
        autorizacaoCobrancaClienteWS.setCodigo(this.codigo);
        autorizacaoCobrancaClienteWS.setCliente(this.getCliente().getCodigo());
        autorizacaoCobrancaClienteWS.setTipoAutorizacaoCobranca(this.getTipoAutorizacao().getDescricao());
        if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            if (this.getOperadoraCartao() != null) {
                autorizacaoCobrancaClienteWS.setOperadoraCartao(this.getOperadoraCartao().getId());
            } else {
                autorizacaoCobrancaClienteWS.setOperadoraCartao(0);
            }

            if (this.getValidadeCartao() != null) {
                autorizacaoCobrancaClienteWS.setValidadeCartao(this.getValidadeCartao());
            } else {
                autorizacaoCobrancaClienteWS.setValidadeCartao("");
            }

            if (this.getMesValidade() != 0) {
                autorizacaoCobrancaClienteWS.setMesValidade(this.getMesValidade());
            } else {
                autorizacaoCobrancaClienteWS.setMesValidade(0);
            }

            if (this.getAnoValidade() != 0) {
                autorizacaoCobrancaClienteWS.setAnoValidade(this.getAnoValidade());
            } else {
                autorizacaoCobrancaClienteWS.setAnoValidade(0);
            }

            if (this.getCartaoMascarado_Apresentar() != null) {
                autorizacaoCobrancaClienteWS.setNumeroCartao(this.getCartaoMascarado_Apresentar());
            } else {
                autorizacaoCobrancaClienteWS.setNumeroCartao("");
            }

            autorizacaoCobrancaClienteWS.setCvv(""); //NUNCA ENVIAR o CVV by Luiz Felipe

            if (this.getNomeTitularCartao() != null) {
                autorizacaoCobrancaClienteWS.setNomeTitularCartao(this.getNomeTitularCartao());
            } else {
                autorizacaoCobrancaClienteWS.setNomeTitularCartao("");
            }

            if (this.getOperadoraCartao() != null) {
                autorizacaoCobrancaClienteWS.setOperadoraCartaoDescricao(this.getOperadoraCartao().getDescricao());
            } else {
                autorizacaoCobrancaClienteWS.setOperadoraCartaoDescricao("");
            }

            autorizacaoCobrancaClienteWS.setTransacaoOnline(this.getConvenio().getTipo().isTransacaoOnline());

        } else if (this.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
            autorizacaoCobrancaClienteWS.setAgencia(this.getAgencia());
            autorizacaoCobrancaClienteWS.setAgenciaDV(this.getAgenciaDV());
            autorizacaoCobrancaClienteWS.setContaCorrente(new Long(this.getContaCorrente()).intValue());
            autorizacaoCobrancaClienteWS.setContaCorrenteDV(this.getContaCorrenteDV());
            autorizacaoCobrancaClienteWS.setCpfTitular(this.getCpfTitular());
            if (this.getApresentarCodigoOperacao()) {
                autorizacaoCobrancaClienteWS.setCodigoOperacao(this.getCodigoOperacao());
            } else {
                autorizacaoCobrancaClienteWS.setCodigoOperacao("");
            }

        }
        autorizacaoCobrancaClienteWS.setBanco(this.getConvenio().getBanco_Apresentar());
        autorizacaoCobrancaClienteWS.setCodigoBanco(this.getConvenio().getBanco().getCodigoBanco().toString());
        autorizacaoCobrancaClienteWS.setListaObjetosACobrar(this.getListaObjetosACobrar());
        autorizacaoCobrancaClienteWS.setConvenio(this.getConvenio().getCodigo());
        return autorizacaoCobrancaClienteWS;
    }

    public boolean isGravarLogAlteracaoViaSite() {
        return gravarLogAlteracaoViaSite;
    }

    public void setGravarLogAlteracaoViaSite(boolean gravarLogAlteracaoViaSite) {
        this.gravarLogAlteracaoViaSite = gravarLogAlteracaoViaSite;
    }

    public boolean isGravarLogRegistroB() {
        return gravarLogRegistroB;
    }

    public void setGravarLogRegistroB(boolean gravarLogRegistroB) {
        this.gravarLogRegistroB = gravarLogRegistroB;
    }

    public boolean isValidarAutorizacaoCobrancaSemelhante() {
        return validarAutorizacaoCobrancaSemelhante;
    }

    public void setValidarAutorizacaoCobrancaSemelhante(boolean validarAutorizacaoCobrancaSemelhante) {
        this.validarAutorizacaoCobrancaSemelhante = validarAutorizacaoCobrancaSemelhante;
    }

    @Override
    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    @Override
    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
