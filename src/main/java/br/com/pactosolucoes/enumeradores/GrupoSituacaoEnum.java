package br.com.pactosolucoes.enumeradores;

public enum GrupoSituacaoEnum {
	ATIVO("AT", "Ativo"),
	INATIVO("IN", "Inativo"),
	TRANCADO("TR", "Trancado"),
	VISITANTE("VI", "Visitante");
	
	
	private String codigo;
	private String descricao;
	
	private GrupoSituacaoEnum(String codigo, String descricao){
		this.codigo = codigo;
		this.descricao = descricao;
	}

	/**
	 * @return the codigo
	 */
	public String getCodigo() {
		return codigo;
	}

	/**
	 * @param codigo the codigo to set
	 */
	public void setCodigo(String codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}

	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	
	
	
}
