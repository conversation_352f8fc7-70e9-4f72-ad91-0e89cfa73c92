package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 */
public enum TypeExtratoStoneOpenBankEnum {
    INTERNAL(1, "Transferência Stone"),
    EXTERNAL(2, "TED"),
    PAYMENT(3, "Pagamento"),
    EXTERNAL_REFUND(4, "Devolução de TED"),
    PAYMENT_REFUND(5, "Devolução de Pagamento"),
    BALANCE_BLOCKED(6, "Valor bloqueado"),
    BALANCE_UNBLOCKED(7, "Valor desbloqueado"),
    PAYROLL(8, "Pagamento de salário"),
    SALARY(9, "Recebimento de salário"),
    SALARY_PORTABILITY(10, "Portabilidade de salário"),
    SALARY_PORTABILITY_REFUND(11, "Devolução de Portabilidade de salário"),
    SALARY_PORTABILITY_EMPLOYER_REFUND(12, "Devolução de salá<PERSON> ou Devolução de salário para empresa"),
    OUTBOUND_STONE_PREPAID_CARD_PAYMENT(13, "Compra feita com Cartão Stone"),
    OUTBOUND_STONE_PREPAID_CARD_PAYMENT_REFUND(14, "Devolução de Compra, feita com Cartão Stone, através do estabelecimento original da compra"),
    OUTBOUND_STONE_PREPAID_CARD_PAYMENT_CHARGEBACK(15, "Devolução de Compra, feita com o Cartão Stone, através da Conta Stone"),
    OUTBOUND_STONE_PREPAID_CARD_WITHDRAWAL(16, "Saque com Cartão Stone feito no Banco24Horas"),
    OUTBOUND_STONE_PREPAID_CARD_WITHDRAWAL_REFUND(17, "Devolução de Saque feito no Banco24Horas"),
    ;


    Integer codigo;
    String descricao;

    private TypeExtratoStoneOpenBankEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TypeExtratoStoneOpenBankEnum getFromCodigo(Integer cod) {
        for (TypeExtratoStoneOpenBankEnum cfg : TypeExtratoStoneOpenBankEnum.values()) {
            if (cod != null && cfg.getCodigo().equals(cod)) {
                return cfg;
            }
        }
        return null;

    }

    public static TypeExtratoStoneOpenBankEnum getFromDescricao(String descricao) {
        for (TypeExtratoStoneOpenBankEnum cfg : TypeExtratoStoneOpenBankEnum.values()) {
            if (descricao != null && cfg.getDescricao().equals(descricao)) {
                return cfg;
            }
        }
        return null;

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
