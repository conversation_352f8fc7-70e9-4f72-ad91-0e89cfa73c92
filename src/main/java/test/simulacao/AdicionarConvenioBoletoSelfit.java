package test.simulacao;

import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cidade;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by johnys on 06/09/2016.
 */
public class AdicionarConvenioBoletoSelfit {

    public static void main(String... args) {

        List<Connection> listaCon = retornarListaConexoes();
        try {
            System.out.println("Inicio do processo em "+ Calendario.hoje());
            for (Connection conZW: listaCon){
                System.out.println("Inicio do processo em "+ Calendario.hoje() + " BD:" + conZW.getClientInfo());
                processarConvenioBoleto(conZW);
                System.out.println("Fim do processo em "+Calendario.hoje() + " BD:" + conZW.getClientInfo());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }finally {
            for (Connection conZW: listaCon){
                try {
                    conZW.close();
                }catch (Exception e){
                    System.out.println("ERRO AO FECHAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
                }

            }

        }
        System.out.println("Fim do processo em "+Calendario.hoje());

    }

    private static void processarConvenioBoleto(Connection conZW) throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" INSERT INTO autorizacaocobrancacliente (cliente,conveniocobranca, tipoAutorizacao,  tipoACobrar) (");
        sql.append(" SELECT c.codigo, e.convenioboletopadrao, 3, 1");
        sql.append(" FROM cliente c");
        sql.append(" INNER JOIN empresa e ON e.codigo = c.empresa");
        sql.append(" WHERE c.situacao <> 'VI'");
        sql.append(" AND NOT EXISTS (");
        sql.append("        SELECT 1");
        sql.append("        FROM autorizacaocobrancacliente");
        sql.append("        WHERE tipoautorizacao = 3");
        sql.append("        AND cliente = c.codigo)");
        sql.append(");");
        conZW.setAutoCommit(false);
        PreparedStatement ps = conZW.prepareStatement(sql.toString());
        ps.executeUpdate();
        conZW.commit();
    }

    public static List<Connection> retornarListaConexoes(){
        List<Connection> lista = new ArrayList<Connection>();
        try{
            lista.add(obterConexao("bdzillyonselfitiguatemi"));
            lista.add(obterConexao("bdzillyonselfitbarra"));
            lista.add(obterConexao("bdzillyonselfitmagshopping"));
            lista.add(obterConexao("bdzillyonselfitparalela"));
            lista.add(obterConexao("bdzillyonselfitpituba2"));
            lista.add(obterConexao("bdzillyonselfitpontaverde"));
            lista.add(obterConexao("bdzillyonselfitcaruaru"));
            lista.add(obterConexao("bdzillyonselfitriverside"));
            lista.add(obterConexao("bdzillyonselfitpaulovi"));
            lista.add(obterConexao("bdzillyonselfitmangabeira"));
            lista.add(obterConexao("bdzillyonselfitcabula"));
            lista.add(obterConexao("bdzillyonselfitiguatemi"));
            lista.add(obterConexao("bdzillyonselfitepitaciopessoa"));
            lista.add(obterConexao("bdzillyonselfitsaoraphael"));
        }catch (Exception e){
            System.out.println("ERRO AO CRIAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
        }
        return lista;
    }

    public static Connection obterConexao(String nomeBD)throws Exception{
        String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        //String hostBD = "localhost";
        String porta = "5432";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        String userBD = "zillyonweb";
        String passwordBD = "pactodb2020";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }


}

