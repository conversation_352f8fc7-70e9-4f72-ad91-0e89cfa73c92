package test.simulacao;

import br.com.pacto.priv.sms.beans.BeanSMSExternal;
import br.com.pactosolucoes.sms.SMSControle;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;

import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class TesteSMS {

    public static void main(String[] args) {
        List<Message> lista = new ArrayList<Message>();
        for (int i = 0; i < 5; i++) {
            Message b = new Message();
            b.setNumero("6284051359");
            b.setMsg("BLA BLA BLA!");
            lista.add(b);
        }
        SmsController smsController = null;
        try {
            smsController = new SmsController("token", "wall", TimeZone.getDefault());
            smsController.sendMessage(null, lista);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }
}
