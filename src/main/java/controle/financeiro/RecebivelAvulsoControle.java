package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.financeiro.PlanoContasControle.TipoConsulta;
import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RecebivelAvulsoTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.PlanoConta;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileOutputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class RecebivelAvulsoControle extends SuperControle {

    private static int TIPO_CHEQUE = 1;
    private int tipo = TIPO_CHEQUE;
    private RecebivelAvulsoTO recebivelAvulso = new RecebivelAvulsoTO();
    private String descricaoPlano = "";
    private String descricaoCentro = "";
    private String nomeResponsavel = "";
    private String nomePessoa = "";
    private String banco = "";
    private Integer codigoBanco = 0;
    private List<SelectItem> empresas;
    private List<RecebivelAvulsoTO> recebiveis = new ArrayList<RecebivelAvulsoTO>();
    private List<RecebivelAvulsoTO> cheques = new ArrayList<RecebivelAvulsoTO>();
    private List<RecebivelAvulsoTO> cartoes = new ArrayList<RecebivelAvulsoTO>();
    private boolean importarViaArquivo = false;
    private boolean edicao = false;
    private File arquivo;
    private Date dataPadrao;
    private boolean semCliente = false;
    private Integer formaPagamento = null;
    private Integer operadoraCartao = null;
    private List<FormaPagamentoVO> formasPagamento;
    ArrayList<PessoaVO> result;
    String pref = "";
    List<RecebivelAvulsoTO> listaNaoImportados = new ArrayList<RecebivelAvulsoTO>();
    private boolean recebivelEmLote = false;

    private String msgAlertAuxiliar = "";
    private BancoVO bancoSelected = null;
    private String msgAlert;

    public void selecionar() {
        try {
            RecebivelAvulsoTO obj = (RecebivelAvulsoTO) context().getExternalContext().getRequestMap().get("recebivel");
            preparaRecebivel(obj);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void preparaRecebivel(RecebivelAvulsoTO obj) throws Exception {
        if (obj != null) {
            arquivo = null;
            edicao = true;
            recebivelAvulso = obj;
            validarExclusao(obj);
            descricaoCentro = obj.getCentroCustos().getDescricao();
            descricaoPlano = obj.getPlanoContas().getDescricao();
            nomeResponsavel = obj.getResponsavel().getNome();
            nomePessoa = obj.getCliente().getPessoa().getNome();
            banco = obj.getCheque().getBanco().getCodigoBanco().toString();
            codigoBanco = obj.getCheque().getBanco().getCodigo();
            obj.getCheque().setCpfOuCnpj(UteisValidacao.emptyString(obj.getCheque().getCnpj()) ? "CPF" : "CNPJ");
        }
    }

    public String editar() {
        formaPagamento = null;
        listaNaoImportados = new ArrayList<RecebivelAvulsoTO>();
        importarViaArquivo = false;
        recebivelEmLote = false;
        recebiveis = new ArrayList<RecebivelAvulsoTO>();
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            RecebivelAvulsoTO consulta = getFacade().getRecebivelAvulso().consultarPorCodigo(tipo == TIPO_CHEQUE, codigoConsulta);
            List<MovContaRateioVO> lista = getFacade().getFinanceiro().getMovContaRateio().consultarPorMovConta(consulta.getMovConta(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            if(lista != null && !lista.isEmpty()){
                formaPagamento = lista.get(0).getFormaPagamentoVO().getCodigo();
            }
            preparaRecebivel(consulta);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void selecionarImportarViaArquivo() {
        try {
            edicao = false;
            setArquivo(null);
            limpar();
            tipo = TIPO_CHEQUE;
            if (importarViaArquivo) {
                cheques = new ArrayList<RecebivelAvulsoTO>();
                cartoes = new ArrayList<RecebivelAvulsoTO>();
                recebiveis = new ArrayList<RecebivelAvulsoTO>();
            } else {
                recebiveis = getFacade().getRecebivelAvulso().consultar();
                separar();
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
        }
    }

    public void prepararDados() {
        try {
            if (arquivo == null) {
                throw new Exception("");
            }
            cheques = new ArrayList<RecebivelAvulsoTO>();
            cartoes = new ArrayList<RecebivelAvulsoTO>();
            recebiveis = new ArrayList<RecebivelAvulsoTO>();
            List<HSSFRow> linhas = LeitorExcel.lerLinhas(arquivo.getPath());
            for (HSSFRow linha : linhas) {
                try {
                    RecebivelAvulsoTO montado = montarExcel(linha);
                    if (!(UteisValidacao.emptyNumber(montado.getValor())
                            && UteisValidacao.emptyString(montado.getCheque().getNomeNoCheque())
                            && (UteisValidacao.emptyString(montado.getCheque().getAgencia()) || montado.getCheque().getAgencia().equals("0"))
                            && (UteisValidacao.emptyString(montado.getCheque().getConta()) || montado.getCheque().getConta().equals("0"))
                            && (UteisValidacao.emptyString(montado.getCheque().getNumero()) || montado.getCheque().getNumero().equals("0")))) {
                        recebiveis.add(montado);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            verificarImportacaoRegistrosSemCliente();
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                setMensagemDetalhada("msg_erro", "Sua planilha deve estar no formato XLS");
            } else {
                setMensagemDetalhada(e);
                setMensagemDetalhada("msg_erro", getMensagemDetalhada());
            }

        }
    }

    public void gravarImportacao() {
        try {
            if(UteisValidacao.emptyNumber(formaPagamento)){
                throw new Exception("Informe a forma de pagamento");
            }
            limparMsg();
            List<RecebivelAvulsoTO> recLista = new ArrayList<RecebivelAvulsoTO>(recebiveis);
            setMsgAlert("");
            int nrCheques = 0;
            for (RecebivelAvulsoTO cheque : recLista) {
                if (!UteisValidacao.emptyNumber(cheque.getCliente().getCodigo())) {
                    try{
                        recebiveis.remove(cheque);
                        gravarRec(cheque);
                        nrCheques++;
                    } catch (Exception e){
                        if(!e.getMessage().equals("Dados Existentes")){
                            cheque.setMsgErro(e.getMessage());
                        }
                        listaNaoImportados.add(cheque);
                    }
                }
            }
            separar();
            montarMsgAlert("Importação de " + nrCheques + " cheques feita com sucesso."
                    + (recebiveis.isEmpty() ? "" : "Verifique os cheques que não foram importados."));

        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemInternalizacao(getMensagemDetalhada()));
            montarMsgAlert(getMensagemDetalhada());
        }

    }

    private RecebivelAvulsoTO montarExcel(HSSFRow linha) throws Exception {
        RecebivelAvulsoTO recebivel = new RecebivelAvulsoTO();
        recebivel.setPlanoContas(getRecebivelAvulso().getPlanoContas());
        recebivel.setCentroCustos(getRecebivelAvulso().getCentroCustos());
        recebivel.setResponsavel(getRecebivelAvulso().getResponsavel());
        recebivel.setEmpresa(getRecebivelAvulso().getEmpresa());

        if (tipo == TIPO_CHEQUE) {
            ChequeVO cheque = new ChequeVO();
            Date faturamento = LeitorExcel.obterData(linha, 0) == null ? LeitorExcel.obterDataEspecifico(linha, 0) : LeitorExcel.obterData(linha, 0);
            recebivel.setFaturamento(faturamento == null ? dataPadrao : faturamento);
            recebivel.setLancamento(Calendario.hoje());

            Date compensacao = LeitorExcel.obterData(linha, 1) == null ? LeitorExcel.obterDataEspecifico(linha, 1) : LeitorExcel.obterData(linha, 1);
            cheque.setDataCompensacao(compensacao);
            recebivel.setCompensacao(compensacao);

            cheque.setValor((Double) LeitorExcel.obterNumero(linha, 2));
            recebivel.setValor(cheque.getValor());


            consultarBancoPorCodigoBanco(LeitorExcel.obterNumero(linha, 3).intValue(), true);
            cheque.setBanco(getRecebivelAvulso().getCheque().getBanco());

            cheque.setAgencia(UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 4)) ? LeitorExcel.obterString(linha, 4) : LeitorExcel.obterStringDoNumero(linha, 4));
            cheque.setConta(UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 5).toString()) ? LeitorExcel.obterString(linha, 5) : LeitorExcel.obterStringDoNumero(linha, 5));
            cheque.setNumero(UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 6)) ? LeitorExcel.obterString(linha, 6) : LeitorExcel.obterStringDoNumero(linha, 6));



            String cpf = UteisValidacao.emptyNumber(LeitorExcel.obterNumero(linha, 7)) ? LeitorExcel.obterString(linha, 7) : LeitorExcel.obterStringDoNumero(linha, 7);
            cpf = cpf == null ? ""
                    : (Formatador.removerMascara(cpf).length() > 11 ? Formatador.formatarString("##.###.###/####-##", Formatador.removerMascara(cpf))
                    : Formatador.formatarString("###.###.###-##", Formatador.removerMascara(cpf)));
            if (Formatador.removerMascara(cpf).length() > 11) {
                cheque.setCnpj(cpf);
            } else {
                cheque.setCpf(cpf);
            }

            cheque.setNomeNoCheque(LeitorExcel.obterString(linha, 8));
            recebivel.setCheque(cheque);

            try {
                Number matricula = LeitorExcel.obterNumero(linha, 9);
                if (UteisValidacao.emptyNumber(matricula)) {
                    String obterString = LeitorExcel.obterString(linha, 9);
                    String[] split = obterString.split("/");
                    if (split.length > 0) {
                        recebivel.setCliente(getFacade().getCliente().consultarPorCodigoMatricula(Integer.valueOf(split[0].trim()), getRecebivelAvulso().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                    } else {
                        recebivel.setCliente(getFacade().getCliente().consultarPorCfp(cheque.getCpf(), Uteis.NIVELMONTARDADOS_MINIMOS));
                    }
                } else {
                    recebivel.setCliente(getFacade().getCliente().consultarPorCodigoMatricula(matricula.intValue(), getRecebivelAvulso().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                }
            } catch (Exception e) {
                recebivel.setCliente(getFacade().getCliente().consultarPorCfp(cheque.getCpf(), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
        } else {
        }
        return recebivel;
    }

    public void verificarImportacaoRegistrosSemCliente() {
        cheques = new ArrayList<RecebivelAvulsoTO>();
        cartoes = new ArrayList<RecebivelAvulsoTO>();
        for (RecebivelAvulsoTO recebivel : recebiveis) {
            if (!semCliente || UteisValidacao.emptyNumber(recebivel.getCliente().getCodigo())) {
                if (tipo == TIPO_CHEQUE) {
                    cheques.add(recebivel);
                } else {
                    cartoes.add(recebivel);
                }
            }
        }
    }

    public void uploadArquivoListener(final UploadEvent event) throws Exception {
        // Obter o arquivo a partir do evento
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        // cria um novo arquivo de imagem
        arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
        // caso exista o arquivo ele é deletado
        arquivo.delete();
        final FileOutputStream out = new FileOutputStream(arquivo);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        // Limpa a memória assim que o arquivo e carregado
        out.flush();
        out.close();
        limpar();
        edicao = false;
    }

    public String buscar() {
        limparMsg();
        return "consultar";
    }

    public void gravar() {
        try {
            if(UteisValidacao.emptyNumber(formaPagamento)){
                throw new Exception("Informe a forma de pagamento");
            }
            recebivelAvulso.setTipoCheque(tipo == TIPO_CHEQUE);
            if (!UteisValidacao.emptyString(descricaoCentro)
                    && UteisValidacao.emptyNumber(recebivelAvulso.getCentroCustos().getCodigo())) {
                throw new ValidacaoException("msg_recAvulso_centrocustos");
            }
            if (!UteisValidacao.emptyString(descricaoPlano)
                    && UteisValidacao.emptyNumber(recebivelAvulso.getPlanoContas().getCodigo())) {
                throw new ValidacaoException("msg_recAvulso_planocontas");
            }
            gravarRec(recebivelAvulso);
            limpar();
            recebiveis = getFacade().getRecebivelAvulso().consultar();
            separar();
            setMensagemID("msg_dados_gravados");
            formaPagamento = null;
        } catch (ValidacaoException e) {
            setMensagemDetalhada("msg_erro", getMensagemInternalizacao(e.getMessage()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 13/08/2013
     */
    private void gravarRec(RecebivelAvulsoTO recebivelAvulso) throws ValidacaoException, Exception {
        if (!getUsuarioLogado().getAdministrador()) {
            recebivelAvulso.setEmpresa(getEmpresaLogado());
        }
        recebivelAvulso.validarDados();
        getFacade().getRecebivelAvulso().incluir(recebivelAvulso, formaPagamento);
            
    }

    public String excluir() {
        try {
            getFacade().getRecebivelAvulso().excluir(recebivelAvulso);
            recebivelAvulso = new RecebivelAvulsoTO();
            novo();
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
//            setMsgAlert("alert('" + e.getMessage() + "');");
            setMensagemDetalhada(e.getMessage());
            return "editar";
        }
    }

    public void excluirImp() {
        try {
            recebiveis.remove(getRecebivelAvulso());
            setRecebivelAvulso(new RecebivelAvulsoTO());
            separar();
            setMsgAlert("alert('Cheque removido da lista');");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public void confirmarAlteracoes() {
        try {
            limparMsg();
            limpar();
            setMsgAlert("");
            arquivo = null;
            recebiveis = new ArrayList<RecebivelAvulsoTO>();
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public String mudarTipo() {
        return "";
    }

    public String novo() {
        try {
            edicao = false;
            recebivelEmLote = false;
            importarViaArquivo = false;
            recebiveis = new ArrayList<RecebivelAvulsoTO>();
            listaNaoImportados = new ArrayList<RecebivelAvulsoTO>();
            recebivelAvulso = new RecebivelAvulsoTO();
            descricaoCentro = "";
            descricaoPlano = "";
            recebivelAvulso.getCheque().setCpfOuCnpj("CPF");
            recebivelAvulso.setEmpresa(getEmpresaLogado());
            recebivelAvulso.setResponsavel(getUsuarioLogado());
            nomeResponsavel = getUsuarioLogado().getNome();
            nomePessoa = "";
            codigoBanco = 0;
            banco = "";
            MovPagamentoControle mpgtoc = (MovPagamentoControle) getControlador(MovPagamentoControle.class);
            mpgtoc.novo();
            mpgtoc.inicializarListasSelectItemTodosComboBox(false);

            limparMsg();
            setSucesso(false);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
            return "";
        }
    }

    public void abrirPopUp() {
        try {
            setMsgAlert("");
            autorizarLancamentosAvulsos();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/recebivelAvulsoCons.jsp', 'RecebivelAvulso', 1000, 670);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }

    }

    private void separar() {
        cartoes = new ArrayList<RecebivelAvulsoTO>();
        cheques = new ArrayList<RecebivelAvulsoTO>();
        for (RecebivelAvulsoTO ra : recebiveis) {
            if (UteisValidacao.emptyNumber(ra.getCartaoCredito().getCodigo())) {
                cheques.add(ra);
            } else {
                cartoes.add(ra);
            }
        }
    }

    private void limpar() {
        recebivelAvulso.setCliente(new ClienteVO());
        nomePessoa = "";
        recebivelAvulso.setMovConta(0);
        recebivelAvulso.setCartaoCredito(new CartaoCreditoVO());
        recebivelAvulso.setCheque(new ChequeVO());
        recebivelAvulso.setFaturamento(null);
        recebivelAvulso.setCompensacao(null);
        recebivelAvulso.setValor(0.0);
        recebivelAvulso.setCodAutorizacao("");
        recebivelAvulso.getCheque().setCpfOuCnpj("CPF");
        codigoBanco = 0;
        banco = "";
    }

    /**
     * <AUTHOR> Alcides
     */
    public List<UsuarioVO> executarAutocompleteUsuario(Object suggest) {
        recebivelAvulso.setResponsavel(new UsuarioVO());
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            } else {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNome(pref, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void consultarBancoCombo() {
        setMensagemDetalhada("");
        setMensagem("");
        setMensagemID("");
        if(bancoSelected != null){
            recebivelAvulso.getCheque().setBanco(new BancoVO());
            setBanco(getRecebivelAvulso().getCheque().getBanco().getCodigoBanco().toString());
        }else{
            setMensagemDetalhada("Banco não encontrado.");
        }
    }

    public void consultarBanco() {
        consultarBancoPorCodigoBanco(UteisValidacao.emptyString(banco) ? 0 : Integer.valueOf(banco), true);
        codigoBanco = getRecebivelAvulso().getCheque().getBanco().getCodigo();
        bancoSelected = getRecebivelAvulso().getCheque().getBanco();
    }

    public void consultarBancoPorCodigoBanco(Integer codigo, boolean codigoBanco) {
        try {
            recebivelAvulso.getCheque().setBanco(new BancoVO());
            if (codigo != null && codigo > 0) {

                BancoVO obj = new BancoVO();

                if (codigoBanco) {
                    obj = getFacade().getBanco().consultarCodigoBanco(codigo, false, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    obj = getFacade().getBanco().consultarCodigo(codigo, false, Uteis.NIVELMONTARDADOS_TODOS);
                }

                if (obj != null && obj.getCodigo().intValue() != 0) {
                    recebivelAvulso.getCheque().setBanco(new BancoVO());
                    recebivelAvulso.getCheque().setBanco(obj);

                    setMensagemDetalhada("");
                    setMensagem("");
                    setMensagemID("");
                } else {
                    setMensagemDetalhada("Banco não encontrado.");
                }
            } else {
                setMensagemDetalhada("Banco não encontrado.");
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void autorizarLancamentosAvulsos() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "LancamentosAvulsos", "9.07 - Lançamentos de Cheques/Cartões Avulsos");
            }
        }
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public void selecionarUsuario() throws Exception {
        UsuarioVO user = (UsuarioVO) request().getAttribute("result");
        recebivelAvulso.setResponsavel(user);
    }

    public void selecionarPessoaSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        if (pessoaVO != null) {
            recebivelAvulso.getCliente().setPessoa(pessoaVO);
            nomePessoa = pessoaVO.getNome();
        }

    }

    /**
     * Método que consulta plano de contas quando o usuário digita no
     * suggestionBox
     *
     * @param suggest
     * @return
     */
    public List<PlanoContaTO> executarAutocompletePlanoContas(Object suggest) {
        recebivelAvulso.setPlanoContas(new PlanoContaTO());
        List<PlanoContaTO> listaPlanoContas = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            PlanoConta pj = new PlanoConta();
            if (somenteNumerosEPontos) {
                listaPlanoContas = pj.consultar(nomePesq, null, null, TipoConsulta.TODOS.name());
            } else {
                listaPlanoContas = pj.consultar(null, nomePesq, null, TipoConsulta.TODOS.name());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaPlanoContas;
    }

    /**
     * Método que seleciona o plano de contas escolhido no suggestionBox
     *
     * @throws SQLException
     * @throws Exception
     */
    public void selecionarPlanoContas() {
        PlanoContaTO obj = (PlanoContaTO) context().getExternalContext().getRequestMap().get("result");
        recebivelAvulso.setPlanoContas(obj);
        descricaoPlano = obj.getCodigoPlano() + " - " + obj.getDescricao();
    }

    /**
     * Método que consulta centro de custos quando o usuário digita no
     * suggestionBox
     *
     * @param suggest
     * @return
     */
    public List<CentroCustoTO> executarAutocompleteCentroCusto(Object suggest) {
        recebivelAvulso.setCentroCustos(new CentroCustoTO());
        List<CentroCustoTO> listaCentroCustos = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
            Integer codigo = null;
            if (somenteNumero){
                codigo = Integer.parseInt(nomePesq);
            }
            CentroCusto pj = new CentroCusto();
            if (somenteNumerosEPontos) {
                listaCentroCustos = pj.consultar(nomePesq, null,codigo);
            } else {
                listaCentroCustos = pj.consultar(null, nomePesq, codigo);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaCentroCustos;
    }

    /**
     * Método que seleciona o centro de custos escolhido no suggestionBox
     *
     * @throws SQLException
     * @throws Exception
     */
    public void selecionarCentroCusto() throws SQLException, Exception {
        CentroCustoTO obj = (CentroCustoTO) context().getExternalContext().getRequestMap().get("result");
        recebivelAvulso.setCentroCustos(obj);
        descricaoCentro = obj.getCodigoCentro() + " - " + obj.getDescricao();
    }

    public List<PessoaVO> executarAutocompleteConsultaPessoa(Object suggest) {
        recebivelAvulso.getCliente().setPessoa(new PessoaVO());
        pref = (String) suggest;
        try {
            if (recebivelAvulso.getEmpresa() == null) {
                setEmpresa(getEmpresaLogado());
            }
            if (UteisValidacao.emptyNumber(recebivelAvulso.getEmpresa().getCodigo().intValue())) {
                throw new Exception("Informe a empresa primeiro");
            }
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                        consultarTodosPessoaComLimiteFinanceiro(recebivelAvulso.getEmpresa().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TIPOPESSOA);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                        consultarPorNomePessoaComLimiteFinanceiro(recebivelAvulso.getEmpresa().getCodigo().intValue(), pref, false, false, Uteis.NIVELMONTARDADOS_TIPOPESSOA);
            }
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void cadastrarNovaPessoa() throws Exception {
        setMsgAlertAuxiliar("");
        if (result != null) {
            if (result.isEmpty() && getRecebivelAvulso().getCliente().getCodigo() != null && getRecebivelAvulso().getEmpresa().getCodigo() != 0 && !getRecebivelAvulso().getEmpresa().getNome().isEmpty()) {
                //processo que preenche o campo de pessoa da tela de cadastro de pessoa
                //e abre modal de pessoa caso a pessoa não exista
                PessoaSimplificadoControle pessoaSimplificadoControle = (PessoaSimplificadoControle) context().getExternalContext().getSessionMap().get(PessoaSimplificadoControle.class.getSimpleName());
                if (pessoaSimplificadoControle == null) {
                    pessoaSimplificadoControle = new PessoaSimplificadoControle();
                    JSFUtilities.storeOnSession(PessoaSimplificadoControle.class.getSimpleName(), pessoaSimplificadoControle);
                }
                getRecebivelAvulso().getCliente().setPessoa(new PessoaVO());
                getRecebivelAvulso().getCliente().getPessoa().setNome(pref);
                pessoaSimplificadoControle.setPessoaVO(getRecebivelAvulso().getCliente().getPessoa());
                pessoaSimplificadoControle.setEmpresaVO(getFacade().getEmpresa().consultarPorCodigo(getRecebivelAvulso().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                pessoaSimplificadoControle.novo();

                setMsgAlertAuxiliar("Richfaces.showModalPanel('modalPanelCadastrarPessoaSimplificada');");
            } else {
                setMsgAlertAuxiliar("");
                if (getRecebivelAvulso().getEmpresa().getCodigo() == null || getRecebivelAvulso().getEmpresa().getCodigo() == 0) {
                    getRecebivelAvulso().getCliente().setPessoa(new PessoaVO());
                }
                if (getRecebivelAvulso().getEmpresa().getNome().isEmpty()) {
                    getRecebivelAvulso().getCliente().setPessoa(new PessoaVO());
                }
            }
        }
    }

    public List<SelectItem> getEmpresas() {
        if (empresas == null) {
            empresas = new ArrayList<SelectItem>();
            empresas.add(new SelectItem(0, ""));
            try {
                List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
                Iterator i = resultadoConsulta.iterator();
                while (i.hasNext()) {
                    EmpresaVO obj = (EmpresaVO) i.next();
                    empresas.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
            } catch (Exception e) {
                setMensagemDetalhada(e.getMessage());
            }
        }

        return empresas;
    }

    public RecebivelAvulsoControle() {
        tipo = TIPO_CHEQUE;
    }

    public void setRecebivelAvulso(RecebivelAvulsoTO recebivelAvulso) {
        this.recebivelAvulso = recebivelAvulso;
    }

    public RecebivelAvulsoTO getRecebivelAvulso() {
        return recebivelAvulso;
    }

    public void setTipo(int tipo) {
        this.tipo = tipo;
    }

    public int getTipo() {
        return tipo;
    }

    public Boolean getApresentarCheque() {
        return tipo == TIPO_CHEQUE;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }

    public String getDescricaoPlano() {
        return descricaoPlano;
    }

    public void setDescricaoCentro(String descricaoCentro) {
        this.descricaoCentro = descricaoCentro;
    }

    public String getDescricaoCentro() {
        return descricaoCentro;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getBanco() {
        return banco;
    }

    public void setCodigoBanco(Integer codigoBanco) {
        this.codigoBanco = codigoBanco;
    }

    public Integer getCodigoBanco() {
        return codigoBanco;
    }

    public void setRecebiveis(List<RecebivelAvulsoTO> recebiveis) {
        this.recebiveis = recebiveis;
    }

    public List<RecebivelAvulsoTO> getRecebiveis() {
        return recebiveis;
    }

    public void setCartoes(List<RecebivelAvulsoTO> cartoes) {
        this.cartoes = cartoes;
    }

    public List<RecebivelAvulsoTO> getCartoes() {
        return cartoes;
    }

    public void setCheques(List<RecebivelAvulsoTO> cheques) {
        this.cheques = cheques;
    }

    public List<RecebivelAvulsoTO> getCheques() {
        return cheques;
    }

    public void setImportarViaArquivo(boolean importarViaArquivo) {
        this.importarViaArquivo = importarViaArquivo;
    }

    public boolean isImportarViaArquivo() {
        return importarViaArquivo;
    }

    public void setEdicao(boolean edicao) {
        this.edicao = edicao;
    }

    public boolean isEdicao() {
        return edicao;
    }

    public void setArquivo(File arquivoUploaded) {
        this.arquivo = arquivoUploaded;
    }

    public File getArquivo() {
        return arquivo;
    }

    public void removerArquivo() {
        arquivo = null;
    }

    public void addArquivo() {
        edicao = false;
    }

    public void setDataPadrao(Date dataPadrao) {
        this.dataPadrao = dataPadrao;
    }

    public Date getDataPadrao() {
        return dataPadrao;
    }

    public void setSemCliente(boolean semCliente) {
        this.semCliente = semCliente;
    }

    public boolean isSemCliente() {
        return semCliente;
    }

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }
    
    public List<SelectItem> getFormasPagamentoSelectItem(){
        try {
            if(formasPagamento == null || formasPagamento.isEmpty()){
                formasPagamento = getFacade().getFormaPagamento().consultarPorDescricao("%", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            List<SelectItem> lista = new ArrayList<SelectItem>();
            for(FormaPagamentoVO formaPagamento : formasPagamento){
                if((getApresentarCheque() && formaPagamento.getTipoFormaPagamento().equals("CH")) ||
                    (!getApresentarCheque() && formaPagamento.getTipoFormaPagamento().equals("CA"))){
                    lista.add(new SelectItem(formaPagamento.getCodigo(),formaPagamento.getDescricao()));
                }
            }
            return lista;
        } catch (Exception e) {
            return new ArrayList<SelectItem>();
        }
    }

    public List<SelectItem> getOperadoraCartaoSelectItem(){
        try {
            List<OperadoraCartaoVO> listaOperadora = getFacade().getOperadoraCartao().consultarTodas(true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<SelectItem> lista = new ArrayList<SelectItem>();

            for(OperadoraCartaoVO operadoraCartaoVO : listaOperadora) {
                lista.add(new SelectItem(operadoraCartaoVO.getCodigo(), operadoraCartaoVO.getDescricao()));
            }

            return lista;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public Integer getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(Integer operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }


    @Override
    public String getMsgAlertAuxiliar() {
        return msgAlertAuxiliar;
    }

    @Override
    public void setMsgAlertAuxiliar(String msgAlertAuxiliar) {
        this.msgAlertAuxiliar = msgAlertAuxiliar;
    }

    public List<RecebivelAvulsoTO> getListaNaoImportados() {
        return listaNaoImportados;
    }

    public void setListaNaoImportados(List<RecebivelAvulsoTO> listaNaoImportados) {
        this.listaNaoImportados = listaNaoImportados;
    }
    
    public void exportar(ActionEvent evt) throws Exception {
        setMensagemDetalhada("", "");
        setMsgAlert("");
        try{
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String extensao = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            extensao = extensao.equals("xls") ? "vnd.ms-excel" : "pdf";

            exportadorListaControle.exportar(evt, getListaNaoImportados(), "",null );
            setMsgAlert("abrirPopup('../UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/" + extensao + "','ChequesAvulsos', 800,200);" + exportadorListaControle.getMsgAlert());

            setSucesso(Boolean.TRUE);
        }catch(Exception e){
            setMsgAlert("alert('"+getMensagemDetalhada()+"')");
            montarErro(e);
        }
            
    }

    private void validarExclusao(RecebivelAvulsoTO obj) throws Exception {
        recebivelEmLote = Boolean.FALSE;
        if(UteisValidacao.notEmptyNumber(obj.getMovConta())) {
            ConfiguracaoFinanceiroVO confFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            if (confFinan.getUsarMovimentacaoContas()) {
                if (getFacade().getCheque().verificarContemLoteMovConta(obj.getMovConta())
                        || getFacade().getCartaoCredito().verificarContemLoteMovConta(obj.getMovConta())) {
                    recebivelEmLote = Boolean.TRUE;
                }
            }
        }
    }

    public boolean isRecebivelEmLote() {
        return recebivelEmLote;
    }

    public void setRecebivelEmLote(boolean recebivelEmLote) {
        this.recebivelEmLote = recebivelEmLote;
    }


    public BancoVO getBancoSelected() {
        return bancoSelected;
    }

    public void setBancoSelected(BancoVO bancoSelected) {
        this.bancoSelected = bancoSelected;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Cheque",
                "Deseja excluir o Cheque?",
                this, "excluir", "", "", "", "grupoBtnExcluir,form");
    }

    public void confirmarExcluirImp(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Cheque Importado",
                "Deseja excluir o Cheque Importado?",
                this, "excluirImp", "", "", "", "grupoBtnExcluir,painelCC, listaCheques,listaCartoes, scrollerlistacartoes, scrollerlistacheques, mensagens");
    }

}
