/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import controle.arquitetura.SuperControle;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.integracao.OamdWSConsumer;

/**
 *
 * <AUTHOR> Estou automatizando um processo muito comum que é excluior o
 * financeiro da academia Esse controle foge de todos os padrões do sistema, tem
 * acesso a banco e tudo mais NÃO LEVE EM CONSIDERAÇÃO ISSO POR AQUI
 *
 */
public class ExcluirFinanceiroControle extends SuperControle {

    private Date diaInicio;
    private Date dataLimite;
    private List<SelectItem> contas = new ArrayList<SelectItem>();
    private Integer contaSelecionada = 0;
    private List<ContaVO> contasSelecionadas = new ArrayList<ContaVO>();
    private List<LogVO> logs = new ArrayList<LogVO>();
    private boolean detodasascontas = false;
    private String user = "";
    private String senha = "";
    

    private void iniciar() throws Exception {
        contas = new ArrayList<SelectItem>();
        contas.add(new SelectItem(0, ""));
        contaSelecionada = 0;
        contasSelecionadas = new ArrayList<ContaVO>();
        logs = new ArrayList<LogVO>();
        
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, descricao FROM conta ", getFacade().getRisco().getCon());
        ResultSet rslog = SuperFacadeJDBC.criarConsulta("SELECT responsavelalteracao, dataalteracao, valorcampoalterado from log where nomeentidade = 'EXCLUIR FINAN' order by dataalteracao desc", getFacade().getRisco().getCon());
        while (rs.next()) {
            contas.add(new SelectItem(rs.getInt("codigo"), rs.getString("descricao")));
        }
        while (rslog.next()) {
            LogVO l = new LogVO();
            l.setResponsavelAlteracao(rslog.getString("responsavelalteracao"));
            l.setDataAlteracao(rslog.getDate("dataalteracao"));
            l.setValorCampoAlterado(rslog.getString("valorcampoalterado"));
            logs.add(l);
        }
        diaInicio = Calendario.hoje();
        dataLimite = Calendario.hoje();
    }

    public ExcluirFinanceiroControle() {
        try {
            iniciar();
        } catch (Exception ex) {
            Logger.getLogger(ExcluirFinanceiroControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void removerConta() throws Exception {
        ContaVO contaVO = (ContaVO) request().getAttribute("conta");
        contasSelecionadas.remove(contaVO);
    }

    public void adicionarConta() throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, descricao FROM conta  where codigo = " + contaSelecionada, getFacade().getRisco().getCon());
        if (rs.next()) {
            ContaVO conta = new ContaVO();
            conta.setCodigo(rs.getInt("codigo"));
            conta.setDescricao(rs.getString("descricao"));
            contasSelecionadas.add(conta);
        }
    }

    public void excluir() {
        try {
            String validarUsuarioOamd = OamdWSConsumer.validarUsuarioOamd(user, senha);
            if (!validarUsuarioOamd.equals("sucesso")) {
                throw new Exception("erro ao validar usuario:" + validarUsuarioOamd);
            }

            String contass = "0";
            if (!detodasascontas) {
                for (ContaVO c : contasSelecionadas) {
                    contass += "," + c.getCodigo();
                }
            }

            StringBuilder sqlcontar = new StringBuilder();
            sqlcontar.append(" SELECT count(codigo) as cont FROM movconta where datalancamento  BETWEEN '");
            sqlcontar.append(Uteis.getDataFormatoBD(diaInicio)).append("' AND '").append(Uteis.getDataFormatoBD(dataLimite)).append("' ");
            if (!detodasascontas) {
                sqlcontar.append(" AND conta in (").append(contass).append(")");
            }
            Integer cont = 0;
            ResultSet criarConsulta = SuperFacadeJDBC.criarConsulta(sqlcontar.toString(), getFacade().getRisco().getCon());
            if (criarConsulta.next()) {
                cont = criarConsulta.getInt("cont");
            }
            StringBuilder sqlExcluir = new StringBuilder();
            sqlExcluir.append("DELETE FROM caixamovconta  where movconta in (select codigo from movconta where datalancamento  BETWEEN '");
            sqlExcluir.append(Uteis.getDataFormatoBD(diaInicio)).append("' AND '").append(Uteis.getDataFormatoBD(dataLimite)).append("' ");
            if (!detodasascontas) {
                sqlExcluir.append(" AND conta in (").append(contass).append(")");
            }
            sqlExcluir.append(");");
            sqlExcluir.append(" DELETE FROM movconta where datalancamento  BETWEEN '");
            sqlExcluir.append(Uteis.getDataFormatoBD(diaInicio)).append("' AND '").append(Uteis.getDataFormatoBD(dataLimite)).append("' ");
            if (!detodasascontas) {
                sqlExcluir.append(" AND conta in (").append(contass).append(")");
            }
            SuperFacadeJDBC.executarConsulta(sqlExcluir.toString(), getFacade().getRisco().getCon());

            LogVO obj = new LogVO();
            obj.setChavePrimaria("");
            obj.setNomeEntidade("EXCLUIR FINAN");
            obj.setNomeEntidadeDescricao("EXCLUIR FINAN");
            obj.setOperacao("EXCLUIR FINAN");
            obj.setResponsavelAlteracao(user);
            obj.setUserOAMD(user);
            obj.setNomeCampo("TODOS");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");
            StringBuilder campo = new StringBuilder();
            campo.append("-inicio:").append(Uteis.getDataFormatoBD(diaInicio));
            campo.append("-fim:").append(Uteis.getDataFormatoBD(dataLimite));
            campo.append("-contas:");
            for (ContaVO cvo : contasSelecionadas) {
                campo.append(cvo.getDescricao()).append(";");

            }
            obj.setValorCampoAlterado(campo.toString() + "-Número de lançamentos deletados: " + cont);
            getFacade().getLog().incluir(obj);
            setMensagem("Número de lançamentos deletados: " + cont);
            iniciar();
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }

    public Date getDataLimite() {
        return dataLimite;
    }

    public void setDataLimite(Date dataLimite) {
        this.dataLimite = dataLimite;
    }

    public List<SelectItem> getContas() {
        return contas;
    }

    public void setContas(List<SelectItem> contas) {
        this.contas = contas;
    }

    public Integer getContaSelecionada() {
        return contaSelecionada;
    }

    public void setContaSelecionada(Integer contaSelecionada) {
        this.contaSelecionada = contaSelecionada;
    }

    public List<ContaVO> getContasSelecionadas() {
        return contasSelecionadas;
    }

    public void setContasSelecionadas(List<ContaVO> contasSelecionadas) {
        this.contasSelecionadas = contasSelecionadas;
    }

    public boolean isDetodasascontas() {
        return detodasascontas;
    }

    public void setDetodasascontas(boolean detodasascontas) {
        this.detodasascontas = detodasascontas;
    }

    public Date getDiaInicio() {
        return diaInicio;
    }

    public void setDiaInicio(Date diaInicio) {
        this.diaInicio = diaInicio;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public List<LogVO> getLogs() {
        return logs;
    }

    public void setLogs(List<LogVO> logs) {
        this.logs = logs;
    }
    
    
}
