package controle.basico;


import controle.arquitetura.SuperControle;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PactoPayComunicacaoControle extends SuperControle {

    private Date dataInicialRegistro;
    private Date dataFinalRegistro;
    private Date dataInicialExecucao;
    private Date dataFinalExecucao;
    private Date dataInicialClicou;
    private Date dataFinalClicou;
    private Date dataInicialLido;
    private Date dataFinalLido;
    private List<PactoPayComunicacaoVO> lista = new ArrayList<>();
    private PactoPayComunicacaoVO pactoPayComunicacaoVO;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private String onComplete;

    public PactoPayComunicacaoControle() {
        this.setDataInicialRegistro(Calendario.hoje());
        this.setDataFinalRegistro(Calendario.hoje());
    }

    public void consultarRel() {
        try {
            lista = new ArrayList<>();
            limparMsg();
            setOnComplete("");

            lista = getFacade().getPactoPayComunicacao().consultar(getDataInicialRegistro(), getDataFinalRegistro(),
                    getDataInicialExecucao(), getDataFinalExecucao(),
                    getDataInicialClicou(), getDataFinalClicou(),
                    getDataInicialLido(), getDataFinalLido(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(lista)) {
                montarAviso("Nenhum registro encontrado");
            }
            setOnComplete("Notifier.cleanAll();" + getMensagemNotificar());
        } catch (Exception ex) {
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public void exibirParams(ActionEvent evt) {
        try {
            limparMsg();
            setOnComplete("");
            pactoPayComunicacaoVO = null;
            listaParametrosSelecionado = null;

            String params = (String) evt.getComponent().getAttributes().get("params");
            PactoPayComunicacaoVO obj = (PactoPayComunicacaoVO) evt.getComponent().getAttributes().get("item");
            if (params == null || obj == null) {
                throw new Exception("Erro ao obter params");
            }

            pactoPayComunicacaoVO = obj;
            if (params.equals("envio")) {
                listaParametrosSelecionado = Uteis.obterListaParametrosValores(obj.getComunicacao());
            } else if (params.equals("resposta")) {
                JSONObject json = new JSONObject();
                try {
                    json = new JSONObject(obj.getResultado());
                } catch (Exception ex) {
                    ex.printStackTrace();
                    json = new JSONObject();
                    json.put("resultado", obj.getResultado());
                }
                listaParametrosSelecionado = Uteis.obterListaParametrosValores(json.toString());
            }
            setOnComplete("Notifier.cleanAll();Richfaces.showModalPanel('modalParamsComunicacao')");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
        }
    }

    public List<PactoPayComunicacaoVO> getLista() {
        if (lista == null) {
            lista = new ArrayList<>();
        }
        return lista;
    }

    public void setLista(List<PactoPayComunicacaoVO> lista) {
        this.lista = lista;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public PactoPayComunicacaoVO getPactoPayComunicacaoVO() {
        if (pactoPayComunicacaoVO == null) {
            pactoPayComunicacaoVO = new PactoPayComunicacaoVO();
        }
        return pactoPayComunicacaoVO;
    }

    public void setPactoPayComunicacaoVO(PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }

    public Date getDataInicialExecucao() {
        return dataInicialExecucao;
    }

    public void setDataInicialExecucao(Date dataInicialExecucao) {
        this.dataInicialExecucao = dataInicialExecucao;
    }

    public Date getDataFinalExecucao() {
        return dataFinalExecucao;
    }

    public void setDataFinalExecucao(Date dataFinalExecucao) {
        this.dataFinalExecucao = dataFinalExecucao;
    }

    public Date getDataInicialClicou() {
        return dataInicialClicou;
    }

    public void setDataInicialClicou(Date dataInicialClicou) {
        this.dataInicialClicou = dataInicialClicou;
    }

    public Date getDataFinalClicou() {
        return dataFinalClicou;
    }

    public void setDataFinalClicou(Date dataFinalClicou) {
        this.dataFinalClicou = dataFinalClicou;
    }

    public Date getDataInicialLido() {
        return dataInicialLido;
    }

    public void setDataInicialLido(Date dataInicialLido) {
        this.dataInicialLido = dataInicialLido;
    }

    public Date getDataFinalLido() {
        return dataFinalLido;
    }

    public void setDataFinalLido(Date dataFinalLido) {
        this.dataFinalLido = dataFinalLido;
    }

    public Date getDataInicialRegistro() {
        return dataInicialRegistro;
    }

    public void setDataInicialRegistro(Date dataInicialRegistro) {
        this.dataInicialRegistro = dataInicialRegistro;
    }

    public Date getDataFinalRegistro() {
        return dataFinalRegistro;
    }

    public void setDataFinalRegistro(Date dataFinalRegistro) {
        this.dataFinalRegistro = dataFinalRegistro;
    }
}
