package relatorio.negocio.jdbc.financeiro;

import negocio.comuns.financeiro.RepasseTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import relatorio.negocio.comuns.financeiro.DetalhamentoLancamentoDF_VO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RelatorioRepasseRel extends SuperRelatorio {

    public RelatorioRepasseRel() throws Exception {
        super();
    }

    public List<RepasseTO> consultarPorReceita(List<Integer> planosIgnorar, boolean emitirRelatorioRepasse,
            Date inicio, Date fim, double taxaRepasse, int empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        List<RepasseTO> lista = new ArrayList<RepasseTO>();
        sql.append("SELECT datacompesancao as compensacao, movpagamento as codigoMP, valor, movconta FROM cheque WHERE situacao NOT LIKE 'CA' AND datacompesancao BETWEEN ? AND ? ");
        sql.append("UNION ALL ");
        sql.append("SELECT datacompesancao as compensacao, movpagamento as codigoMP, valor, movconta  FROM cartaocredito  WHERE situacao NOT LIKE 'CA' and datacompesancao BETWEEN ? AND ? ");
        sql.append("UNION ");
        sql.append("SELECT movpagamento.dataquitacao as compensacao, movpagamento.codigo as codigoMP, movpagamento.valor, null FROM movpagamento INNER JOIN formapagamento ");
        sql.append("ON formapagamento.codigo = movpagamento.formapagamento AND formapagamento.tipoformapagamento ");
        sql.append("NOT IN ('CA', 'CH') WHERE datapagamento BETWEEN ? AND ? ");
        PreparedStatement statement = con.prepareStatement(sql.toString());
        int i = 1;
        while (i < 6) {
            statement.setTimestamp(i++, Uteis.getDataHoraJDBC(inicio, "00:00:00"));
            statement.setTimestamp(i++, Uteis.getDataHoraJDBC(fim, "23:59:59"));
        }

        ResultSet result = statement.executeQuery();
        while (result.next()) {
            if (UteisValidacao.emptyNumber(result.getInt("codigoMP"))) {
                StringBuilder dadosMovConta = new StringBuilder();
                dadosMovConta.append(" select mc.codigo, fp.tipoformapagamento, mc.pessoa,  mc.valor,\n");
                dadosMovConta.append(" p.nome as nomep, c.matricula, mc.empresa  from movconta mc\n");
                dadosMovConta.append(" inner join movcontarateio mcr on mcr.movconta = mc.codigo\n");
                dadosMovConta.append(" inner join formapagamento fp on fp.codigo = mcr.formapagamento\n");
                dadosMovConta.append(" inner join pessoa p on p.codigo = mc.pessoa\n");
                dadosMovConta.append(" inner join cliente c on c.pessoa = p.codigo");
                dadosMovConta.append(" where mc.codigo = " + result.getInt("movconta") + " and mc.empresa = " + empresa + " and mcr.tipoES = 1");

                ResultSet resultDadosPag = SuperFacadeJDBC.criarConsulta(dadosMovConta.toString(), con);
                while (resultDadosPag.next()) {
                    RepasseTO repasse = new RepasseTO();
                    repasse.setValor(result.getDouble("valor"));
                    repasse.setValorCompensado(result.getDouble("valor"));
                    repasse.setTotalDescontar(repasse.getValor() - repasse.getValorCompensado());
                    repasse.setValorRepasse(repasse.getValorCompensado() * (taxaRepasse / 100));
                    repasse.setDataCompensacao(result.getDate("compensacao"));
                    repasse.setNomeAluno(resultDadosPag.getString("nomeP"));
                    repasse.setMatricula(resultDadosPag.getString("matricula"));
                    repasse.setMovpagamento(null);
                    repasse.setPlano("(Lançamento avulso)");
                    lista.add(repasse);
                }
            } else {
                StringBuilder dadosPagamento = new StringBuilder();
                dadosPagamento.append("SELECT\n"
                        + "  mp.codigo,\n"
                        + "  fp.tipoformapagamento,\n"
                        + "  fp.descricao formaPagamento,\n"
                        + "  con.codigo                AS contrato,\n"
                        + "  con.pessoa,\n"
                        + "  mp.pessoa as pessoap,\n"
                        + "  mp.nomepagador               AS nomeP,\n"
                        + "  cliente.matricula,\n"
                        + "  mp.recibopagamento,\n"
                        + "  mp.empresa,\n"
                        + "  con.situacao,\n"
                        + "  con.vigenciaateajustada, "
                        + "  plano.descricao           AS plano,\n"
                        + "  mp.nrparcelacartaocredito AS nrparcela,\n"
                        + "  oc.taxa as taxa\n"
                        + "FROM movpagamento mp\n"
                        + "  INNER JOIN recibopagamento rp\n"
                        + "    ON rp.codigo = mp.recibopagamento\n"
                        + "  INNER JOIN formapagamento fp\n"
                        + "    ON fp.codigo = mp.formapagamento\n"
                        + "  INNER JOIN pagamentomovparcela pmp\n"
                        + "    ON pmp.movpagamento = mp.codigo\n"
                        + "  INNER JOIN movparcela mpc\n"
                        + "    ON pmp.movparcela = mpc.codigo\n"
                        + "  INNER JOIN contrato con\n"
                        + "    ON mpc.contrato = con.codigo\n"
                        + "  INNER JOIN plano\n"
                        + "    ON plano.codigo = con.plano\n"
                        + "  INNER JOIN pessoa\n"
                        + "    ON pessoa.codigo = mp.pessoa\n"
                        + "  INNER JOIN cliente\n"
                        + "    ON pessoa.codigo = cliente.pessoa\n"
                        + "  LEFT JOIN operadoracartao oc\n"
                        + "    ON mp.operadoracartao = oc.codigo ");
                dadosPagamento.append(" where mp.codigo = ").append(result.getInt("codigoMP"))
                        .append(" and con.empresa = ").append(empresa)
                        .append(" and plano.empresa = ").append(empresa);
                String planos = "0";
                for (Integer cod : planosIgnorar) {
                    planos = planos + "," + cod;
                }
                dadosPagamento.append(" and con.plano").append(emitirRelatorioRepasse ? " not " : "").append(" in (" + planos + ")");

                dadosPagamento.append("  GROUP BY mp.codigo, fp.tipoformapagamento, con.codigo, con.pessoa,mp.pessoa, mp.nomepagador, cliente.matricula, ");
                dadosPagamento.append("  mp.recibopagamento, mp.empresa, con.situacao,con.vigenciaateajustada, plano.descricao, fp.descricao, mp.nrparcelacartaocredito, oc.taxa");

                ResultSet resultDadosPag = SuperFacadeJDBC.criarConsulta(dadosPagamento.toString(), con);

                while (resultDadosPag.next()) {
                    Date vigencia = resultDadosPag.getDate("vigenciaateajustada");
                    String situacao = resultDadosPag.getString("situacao");
                    //regra para a club22:não apresentar os pagamentos que compensam depois do término do contrato
                    //se o mesmo estiver cancelado, mas apenas para cancelamentos feitos até 10/04/2014.
                    if (situacao != null && situacao.equals("CA")
                            && Calendario.maior(result.getDate("compensacao"), vigencia)
                            && Calendario.menor(vigencia, Uteis.getDate("10/04/2014"))) {
                        continue;
                    }
                    LancamentoDF lancamento = new LancamentoDF();
                    lancamento.setDadosResumidos(true);
                    lancamento.setMovPagamento(result.getInt("codigoMP"));
                    lancamento.setCodigoPessoa(resultDadosPag.getInt("pessoa"));
                    lancamento.setContrato(resultDadosPag.getInt("contrato"));

                    lancamento.setDescricaoLancamento("LANÇAMENTO");
                    lancamento.setEmpresa(resultDadosPag.getInt("empresa"));
                    lancamento.setLancamentoEhNaoAtribuido(true);
                    lancamento.setTipoProduto("PM");
                    lancamento.setRecibo(resultDadosPag.getInt("recibopagamento"));
                    lancamento.setDescricaoFormaPagamento(resultDadosPag.getString("formaPagamento"));
                    //
                    String tipo = resultDadosPag.getString("tipoformapagamento");
                    if (tipo.equals("CH")) {
                        lancamento.setTipoFormaPagto(TipoFormaPagto.CHEQUE);
                    } else if (tipo.equals("CA")) {
                        lancamento.setTipoFormaPagto(TipoFormaPagto.CARTAOCREDITO);
                    } else {
                        lancamento.setTipoFormaPagto(TipoFormaPagto.AVISTA);
                    }

                    lancamento.setTipoES(TipoES.ENTRADA);

                    DetalhamentoLancamentoDF_VO detalhe = DetalhamentoLancamentoDF.consultarDetalhesDoLancamento(lancamento,
                            TipoVisualizacaoRelatorioDF.PLANOCONTA, new DemonstrativoFinanceiro(), new ArrayList<Integer>(), inicio, fim,
                            TipoRelatorioDF.RECEITA, null, true);

                    RepasseTO repasse = new RepasseTO();
                    repasse.setNrParcelas(resultDadosPag.getInt("nrparcela"));
                    repasse.setTaxa(resultDadosPag.getDouble("taxa"));
                    repasse.setValor(result.getDouble("valor"));

                    if (detalhe.getListaLancamentoDF().size() > 1) {
                        for (LancamentoDF lancamentoDF : detalhe.getListaLancamentoDF()) {
                            if (detalhe.getContratoVO().getCodigo().equals(lancamentoDF.getContrato())) {
                                repasse.setValorCompensado(lancamentoDF.getValorLancamento());
                            }
                        }
                    } else {
                        repasse.setValorCompensado(detalhe.getTotalPagoPlano() > result.getDouble("valor") ? result.getDouble("valor")
                                : detalhe.getTotalPagoPlano());
                    }

                    if (repasse.getValorCompensado() > 0) {
                        repasse.setValorCompensado((repasse.getValorCompensado() - (repasse.getValorCompensado() * (repasse.getTaxa() / 100))));
                    }

                    repasse.setTotalDescontar(repasse.getValor() - repasse.getValorCompensado());
                    repasse.setValorRepasse(repasse.getValorCompensado() * (taxaRepasse / 100));
                    repasse.setDataCompensacao(result.getDate("compensacao"));
                    repasse.setNomeAluno(resultDadosPag.getString("nomeP"));
                    repasse.setMatricula(resultDadosPag.getString("matricula"));
                    repasse.setPlano(resultDadosPag.getString("plano"));
                    repasse.setMovpagamento(resultDadosPag.getInt("codigo"));
                    lista.add(repasse);
                }

            }
        }
        Ordenacao.ordenarLista(lista, "nomeAluno");
        return lista;
    }

    public List<RepasseTO> consultarPorCompetencia(List<Integer> planosIgnorar, boolean emitirRelatorioRepasse,
            Date inicio, Date fim, double taxaRepasse, int empresa, boolean competenciaPaga) throws Exception {
        StringBuilder sql = new StringBuilder();
        List<RepasseTO> lista = new ArrayList<RepasseTO>();

        String planos = "0";
        for (Integer cod : planosIgnorar) {
            planos = planos + "," + cod;
        }
        String datas = "";
        List<Date> mesesEntreDatas = Uteis.getMesesEntreDatas(inicio, fim);
        for (Date data : mesesEntreDatas) {
            datas = datas + ",'" + Uteis.getMesReferenciaData(data) + "'";
        }

        sql.append("SELECT cli.matricula, pes.nome, mp.codigo as codigomovproduto, pl.descricao as plano, \n");
        sql.append("mp.totalfinal as valor, mp.mesreferencia,   \n");

        sql.append("(SELECT mpg.produtospagos\n");
        sql.append("   FROM movpagamento mpg\n");
        sql.append("     INNER JOIN movprodutoparcela mpp ON mpp.recibopagamento = mpg.recibopagamento\n");
        sql.append("   WHERE mpg.produtospagos LIKE ('%' || mp.codigo || '%') and movproduto = mp.codigo\n");
        sql.append("   LIMIT 1) as produtoPago,\n");
        //TAXA DE CARTÃO PARA OS PRODUTOS QUE FORAM PAGOS EM CARTÃO
        sql.append("(SELECT (CASE\n");
        sql.append("           WHEN fp.tipoformapagamento = 'CD'\n");
        sql.append("             THEN fp.taxacartao\n");
        sql.append("           WHEN fp.tipoformapagamento = 'CA'\n");
        sql.append("             THEN ((SELECT tc.taxa\n");
        sql.append("                    FROM taxacartao tc\n");
        sql.append("                    WHERE tc.formapagamento = mpg.formapagamento AND tc.nrmeses = mpg.nrparcelacartaocredito \n");
        sql.append("                    AND ((mpg.datalancamento >= tc.vigenciainicial AND tc.vigenciafinal is null) \n");
        sql.append("                    OR (mpg.datalancamento  >= tc.vigenciainicial AND mpg.datalancamento <= tc.vigenciafinal))limit 1))\n");
        sql.append("           END)\n");
        sql.append("   FROM formapagamento fp\n");
        sql.append("     INNER JOIN movpagamento mpg ON mpg.formapagamento = fp.codigo\n");
        sql.append("     INNER JOIN movprodutoparcela mpp ON mpp.recibopagamento = mpg.recibopagamento\n");
        sql.append("   WHERE mpg.produtospagos LIKE ('%' || mp.codigo || '%') and movproduto = mp.codigo\n");
        sql.append("   LIMIT 1) as taxa\n");
        //TAXA DE CARTÃO PARA OS PRODUTOS QUE FORAM PAGOS EM CARTÃO
        sql.append("FROM movproduto mp\n");
        sql.append("INNER JOIN produto p ON p.codigo = mp.produto AND p.tipoproduto = 'PM'\n");
        sql.append("INNER JOIN contrato c ON c.codigo = mp.contrato\n ");
        sql.append(" and c.plano").append(emitirRelatorioRepasse ? " not " : "").append(" in (").append(planos).append(")");
        sql.append("INNER JOIN plano pl ON c.plano = pl.codigo\n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = mp.pessoa\n");
        sql.append("INNER JOIN cliente cli ON pes.codigo = cli.pessoa\n");
        sql.append("WHERE mp.descricao NOT LIKE 'PLANO TRANSFERIDO%' AND mesreferencia IN (").append(datas.replaceFirst(",", "")).append(")\n");
        sql.append("AND mp.situacao NOT LIKE 'CA' AND mp.empresa = ").append(empresa)
                .append(" and pl.empresa = ").append(empresa);
        sql.append("\nORDER BY pes.nome");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            RepasseTO repasse = new RepasseTO();
            repasse.setValor(rs.getDouble("valor"));
            repasse.setTaxa(rs.getDouble("taxa"));
            repasse.setValorCompensado(rs.getDouble("valor"));
            if (repasse.getValorCompensado() > 0) {
                repasse.setValorCompensado((repasse.getValorCompensado() - (repasse.getValorCompensado() * (repasse.getTaxa() / 100))));
            }
            repasse.setTotalDescontar(repasse.getValor() - repasse.getValorCompensado());
            repasse.setValorRepasse(repasse.getValorCompensado() * (taxaRepasse / 100));
            repasse.setMesReferencia(rs.getString("mesreferencia"));
            repasse.setNomeAluno(rs.getString("nome"));
            repasse.setMatricula(rs.getString("matricula"));
            repasse.setPlano(rs.getString("plano"));
            repasse.setMovproduto(rs.getInt("codigomovproduto"));
            if (!competenciaPaga || !UteisValidacao.emptyString(rs.getString("produtoPago"))) {
                lista.add(repasse);
            }
        }
        return lista;
    }
}
