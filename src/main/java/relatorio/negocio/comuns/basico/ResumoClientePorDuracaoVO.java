/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.facade.jdbc.basico.Empresa;

public class ResumoClientePorDuracaoVO extends SuperVO {

    protected Integer quantidade;
    protected Integer numeroMeses;
    protected String Descricao;
    protected String situacao;
    private String percentual;
    private EmpresaVO empresaVO;

    public ResumoClientePorDuracaoVO() {
        super();
        inicializarDados();
    }

    public String getNumero_Apresentar() {
        return getNumeroMeses() + " meses";
    }

    public String getPercentual_Apresentar() {
        return getPercentual() + " %";
    }

    public void inicializarDados() {
        setDescricao("");
        setNumeroMeses(0);
        setQuantidade(0);
        setSituacao("");
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public Integer getNumeroMeses() {
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            return "";
        }
        if (getSituacao().equals("NO")) {
            return "Normal";
        }
        if (getSituacao().equals("AV")) {
            return "A Vencer";
        }
        if (getSituacao().equals("VE")) {
            return "Vencido";

        }
        if (getSituacao().equals("CR")) {
            return "Férias";

        }
        if (getSituacao().equals("AT")) {
            return "Atestado";

        }
        if (getSituacao().equals("TR")) {
            return "Trancado";
        }
        return "";
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getPercentual() {
        return percentual;
    }

    public void setPercentual(String percentual) {
        this.percentual = percentual;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }
}
