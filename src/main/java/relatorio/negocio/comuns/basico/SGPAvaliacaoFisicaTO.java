package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;

import java.util.ArrayList;
import java.util.List;

public class SGPAvaliacaoFisicaTO extends SuperTO {
    private int qtdeComerciario;
    private List<ClienteVO> listaComerciario;
    private int qtdeDependente;
    private List<ClienteVO> listaDependentes;
    private int qtdeUsuario;
    private List<ClienteVO> listaUsuarios;
    private int totalCategorias;

    public SGPAvaliacaoFisicaTO() {
    }

    public void calculaTotalCategorias() {
        totalCategorias = qtdeComerciario + qtdeDependente + qtdeUsuario;
    }

    public int getQtdeComerciario() {
        return qtdeComerciario;
    }

    public void setQtdeComerciario(int qtdeComerciario) {
        this.qtdeComerciario = qtdeComerciario;
    }

    public void addQtdeComerciario(int qtdeComerciario) {
        this.qtdeComerciario += qtdeComerciario;
    }

    public int getQtdeDependente() {
        return qtdeDependente;
    }

    public void setQtdeDependente(int qtdeDependente) {
        this.qtdeDependente = qtdeDependente;
    }

    public void addQtdeDependente(int qtdeDependente) {
        this.qtdeDependente += qtdeDependente;
    }

    public int getQtdeUsuario() {
        return qtdeUsuario;
    }

    public void setQtdeUsuario(int qtdeUsuario) {
        this.qtdeUsuario = qtdeUsuario;
    }

    public void addQtdeUsuario(int qtdeUsuario) {
        this.qtdeUsuario += qtdeUsuario;
    }

    public int getTotalCategorias() {
        return totalCategorias;
    }

    public void setTotalCategorias(int totalCategorias) {
        this.totalCategorias = totalCategorias;
    }

    public List<ClienteVO> getListaComerciario() {
        if (listaComerciario == null) {
            listaComerciario = new ArrayList<>();
        }
        return listaComerciario;
    }

    public void setListaComerciario(List<ClienteVO> listaComerciario) {
        this.listaComerciario = listaComerciario;
    }

    public List<ClienteVO> getListaDependentes() {
        if (listaDependentes == null) {
            listaDependentes = new ArrayList<>();
        }
        return listaDependentes;
    }

    public void setListaDependentes(List<ClienteVO> listaDependentes) {
        this.listaDependentes = listaDependentes;
    }

    public List<ClienteVO> getListaUsuarios() {
        if (listaUsuarios == null) {
            listaUsuarios = new ArrayList<>();
        }
        return listaUsuarios;
    }

    public void setListaUsuarios(List<ClienteVO> listaUsuarios) {
        this.listaUsuarios = listaUsuarios;
    }

}
