package relatorio.negocio.comuns.sad;

import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.EmpresaVO;

/**
 * Reponsável por manter os dados da entidade RotatividadeSinteticoDW. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class RotatividadeSinteticoDWVO extends SuperVO {

    protected Integer codigo;
    protected Date dia;
    protected Integer mes;
    protected Integer ano;
    protected EmpresaVO empresa;     
    protected String situacao;
    protected Integer qtdVencido;
    protected Integer qtdVencidoFimMes;
    protected Integer qtdVigentesMesAnterior;
    private Integer qtdeVigenteMesAtual;
    private Integer qtdeFinalMesAtual;
    protected Integer qtdDependentesMesAtual;
    protected Integer qtdDependentesFinalMesAtual;
    protected Integer qtdTotal;
    private Integer qtdClienteAtivosDependentesMesAtual;
    private Integer qtdClienteAtivosDependentesFinalMesAtual;
    private Integer qtdBolsistasFinalMesAtual;
    protected Integer qtdAgregadoresVinculadosMes;
    protected Integer qtdAgregadoresVinculadosHoje;


    /**
     * Construtor padrão da classe <code>RotatividadeSinteticoDW</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public RotatividadeSinteticoDWVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(RotatividadeSinteticoDWVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setSituacao(getSituacao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setDia(negocio.comuns.utilitarias.Calendario.hoje());
        setMes(0);
        setEmpresa(new EmpresaVO());
        setSituacao("");       
        setQtdVencido(0);
        setQtdVigentesMesAnterior(0);
        setQtdeVigenteMesAtual(0);
        setQtdeFinalMesAtual(0);
        setQtdTotal(0);
        setAno(0);
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Integer getMes() {
        if (mes == null) {
            mes = 0;
        }
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Date getDia() {
        if (dia == null) {
            dia = Calendario.hoje();
        }
        return dia;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDia_Apresentar() {
        return (Uteis.getData(dia));
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    
    public Integer getQtdVencido() {
        return qtdVencido;
    }

    public void setQtdVencido(Integer qtdVencido) {
        this.qtdVencido = qtdVencido;
    }

    public Integer getQtdVigentesMesAnterior() {
        return qtdVigentesMesAnterior;
    }

    public void setQtdVigentesMesAnterior(Integer qtdVigentesMesAnterior) {
        this.qtdVigentesMesAnterior = qtdVigentesMesAnterior;
    }

    public Integer getQtdTotal() {
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Boolean getMatriculado() {
         return situacao.equals("MA");
    }

    public Boolean getRematriculado() {
        return situacao.equals("RE");
    }

    public Boolean getCancelado() {
        return situacao.equals("CA");
    }

    public Boolean getDesistente() {
        return situacao.equals("RE");
    }

    public Boolean getTrancado() {
        return situacao.equals("TR");
    }

    public Boolean getRetornoTrancado() {
        return situacao.equals("RT");
    }

    public Boolean getVencido() {
        return situacao.equals("VE");
    }

    public Integer getQtdeVigenteMesAtual() {
        return qtdeVigenteMesAtual;
    }

    public void setQtdeVigenteMesAtual(Integer qtdeVigenteMesAtual) {
        this.qtdeVigenteMesAtual = qtdeVigenteMesAtual;
    }

    public Integer getQtdeFinalMesAtual() {
        return qtdeFinalMesAtual;
    }

    public void setQtdeFinalMesAtual(Integer qtdeFinalMesAtual) {
        this.qtdeFinalMesAtual = qtdeFinalMesAtual;
    }

    public Integer getQtdVencidoFimMes() {
        if (qtdVencidoFimMes == null) {
            qtdVencidoFimMes = 0;
        }
        return qtdVencidoFimMes;
    }

    public void setQtdVencidoFimMes(Integer qtdVencidoFimMes) {
        this.qtdVencidoFimMes = qtdVencidoFimMes;
    }


    public Integer getQtdDependentesMesAtual() {
        if (qtdDependentesMesAtual == null) {
            qtdDependentesMesAtual = 0;
        }
        return qtdDependentesMesAtual;
    }

    public void setQtdDependentesMesAtual(Integer qtdDependentesMesAtual) {
        this.qtdDependentesMesAtual = qtdDependentesMesAtual;
    }

    public Integer getQtdAgregadoresVinculadosMes() {
        if (qtdAgregadoresVinculadosMes == null) {
            qtdAgregadoresVinculadosMes = 0;
        }
        return qtdAgregadoresVinculadosMes;
    }

    public void setQtdAgregadoresVinculadosMes(Integer qtdAgregadoresVinculadosMes) {
        this.qtdAgregadoresVinculadosMes = qtdAgregadoresVinculadosMes;
    }

    public Integer getQtdAgregadoresVinculadosHoje() {
        if (qtdAgregadoresVinculadosHoje == null) {
            qtdAgregadoresVinculadosHoje = 0;
        }
        return qtdAgregadoresVinculadosHoje;
    }

    public void setQtdAgregadoresVinculadosHoje(Integer qtdAgregadoresVinculadosHoje) {
        this.qtdAgregadoresVinculadosHoje = qtdAgregadoresVinculadosHoje;
    }

    public Integer getQtdDependentesFinalMesAtual() {
        if (qtdDependentesFinalMesAtual == null) {
            qtdDependentesFinalMesAtual = 0;
        }
        return qtdDependentesFinalMesAtual;
    }

    public void setQtdDependentesFinalMesAtual(Integer qtdDependentesFinalMesAtual) {
        this.qtdDependentesFinalMesAtual = qtdDependentesFinalMesAtual;
    }

    public Integer getQtdClienteAtivosDependentesMesAtual() {
        if (qtdClienteAtivosDependentesMesAtual == null) {
            qtdClienteAtivosDependentesMesAtual = 0;
        }
        return qtdClienteAtivosDependentesMesAtual;
    }

    public void setQtdClienteAtivosDependentesMesAtual(Integer qtdClienteAtivosDependentesMesAtual) {
        this.qtdClienteAtivosDependentesMesAtual = qtdClienteAtivosDependentesMesAtual;
    }

    public Integer getQtdClienteAtivosDependentesFinalMesAtual() {
        if (qtdClienteAtivosDependentesFinalMesAtual == null) {
            qtdClienteAtivosDependentesFinalMesAtual = 0;
        }
        return qtdClienteAtivosDependentesFinalMesAtual;
    }

    public void setQtdClienteAtivosDependentesFinalMesAtual(Integer qtdClienteAtivosDependentesFinalMesAtual) {
        this.qtdClienteAtivosDependentesFinalMesAtual = qtdClienteAtivosDependentesFinalMesAtual;
    }

    public Integer getQtdBolsistasFinalMesAtual() {
        if (qtdBolsistasFinalMesAtual == null) {
            qtdBolsistasFinalMesAtual = 0;
        }
        return qtdBolsistasFinalMesAtual;
    }

    public void setQtdBolsistasFinalMesAtual(Integer qtdBolsistasFinalMesAtual) {
        this.qtdBolsistasFinalMesAtual = qtdBolsistasFinalMesAtual;
    }
}
