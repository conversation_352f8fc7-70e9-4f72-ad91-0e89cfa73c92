    package servlet.arquitetura;

    import org.json.JSONObject;
    import servicos.propriedades.PropsService;

    import javax.servlet.ServletException;
    import javax.servlet.http.HttpServletRequest;
    import javax.servlet.http.HttpServletResponse;
    import java.io.IOException;

    public class HealthCheckServlet extends SuperServlet {
        @Override
        protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET");
            response.setContentType("application/json");

            JSONObject result = new JSONObject();
            result.put("versao", PropsService.getPropertyValue(PropsService.VERSAO_SISTEMA));
            response.getWriter().append(result.toString());

        }
    }