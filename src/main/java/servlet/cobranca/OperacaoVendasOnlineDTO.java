package servlet.cobranca;

import org.json.JSONObject;
import servicos.vendasonline.dto.VendaDTO;

import javax.servlet.http.HttpServletRequest;

public class OperacaoVendasOnlineDTO {

    private String operacao;
    private Integer empresa;
    private Integer produto;
    private Integer categoria;
    private Integer cliente;
    private Integer pessoa;
    private String chave;
    private String matricula;
    private Integer codigoPix;
    private Integer tipoCobranca;
    private Integer codigo;
    private String dataCorteTurma;
    private Integer usuario;
    private Integer origemCobranca;
    private Integer cobrancaAntecipada;
    private Integer pactoPayComunicacao;
    private Double valorCobrar;
    private VendaDTO venda;
    private double valorVenda = 0.0; //usado para obter o valor total com parcelamento pela operadora
    private boolean pactoFlow;
    private String parcelasSelecionadas;
    private Boolean enviarEmail;
    private Boolean todasEmAberto;
    private String urlZw;
    //Hibrael 19/02/2025 - Referente a origem do request se vem do aplicativo, vendas online etc.
    private Integer origem;

    public OperacaoVendasOnlineDTO() {
    }

    public OperacaoVendasOnlineDTO(JSONObject json) {
        this.operacao = json.optString("operacao");
        this.empresa = json.optInt("empresa");
        this.produto = json.optInt("produto");
        this.categoria = json.optInt("categoria");
        this.cliente = json.optInt("cliente");
        this.pessoa = json.optInt("pessoa");
        this.matricula = json.optString("matricula");
        this.codigoPix= json.optInt("codigoPix");
        this.chave = json.optString("key");
        this.tipoCobranca = json.optInt("tipoCobranca");
        this.codigo = json.optInt("codigo");
        this.usuario = json.optInt("usuario");
        this.origemCobranca = json.optInt("origemCobranca");
        this.cobrancaAntecipada = json.optInt("cobrancaAntecipada");
        this.pactoPayComunicacao = json.optInt("pactoPayComunicacao");
        this.valorCobrar = json.optDouble("valorCobrar");
        this.valorVenda = json.optDouble("valorVenda");
        if (json.has("parcelasSelecionadas")) {
            this.parcelasSelecionadas = json.optString("parcelasSelecionadas");
        }
        if (json.has("enviarEmail")) {
            this.enviarEmail = json.optBoolean("enviarEmail");
        }

        try {
            this.dataCorteTurma = String.valueOf(json.get("dataCorteTurma"));
        } catch (Exception e) {}

        try {
            this.pactoFlow = json.getBoolean("pactoFlow");
        } catch (Exception e) {}

        try {
            this.venda = new VendaDTO(json.optJSONObject("venda"));
        } catch (Exception e) {}

        try {
            this.todasEmAberto = json.optBoolean("todasEmAberto");
        } catch (Exception e) {}

        this.origem = json.optInt("origem");
    }

    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCliente() {
        if (cliente == null) {
            cliente = 0;
        }
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getProduto() {
        if (produto == null) {
            produto = 0;
        }
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getCategoria() {
        if (categoria == null) {
            categoria = 0;
        }
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public Integer getPessoa() {
        if (pessoa == null) {
            pessoa = 0;
        }
        return pessoa;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigoPix() {
        return codigoPix;
    }

    public void setCodigoPix(Integer codigoPix) {
        this.codigoPix = codigoPix;
    }

    public Integer getTipoCobranca() {
        return tipoCobranca;
    }

    public void setTipoCobranca(Integer tipoCobranca) {
        this.tipoCobranca = tipoCobranca;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataCorteTurma() {
        return dataCorteTurma;
    }

    public void setDataCorteTurma(String dataCorteTurma) {
        this.dataCorteTurma = dataCorteTurma;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getOrigemCobranca() {
        return origemCobranca;
    }

    public void setOrigemCobranca(Integer origemCobranca) {
        this.origemCobranca = origemCobranca;
    }

    public Integer getCobrancaAntecipada() {
        return cobrancaAntecipada;
    }

    public void setCobrancaAntecipada(Integer cobrancaAntecipada) {
        this.cobrancaAntecipada = cobrancaAntecipada;
    }

    public Double getValorCobrar() {
        return valorCobrar;
    }

    public void setValorCobrar(Double valorCobrar) {
        this.valorCobrar = valorCobrar;
    }

    public VendaDTO getVenda() {
        return venda;
    }

    public void setVenda(VendaDTO venda) {
        this.venda = venda;
    }

    public double getValorVenda() {
        return valorVenda;
    }

    public void setValorVenda(double valorVenda) {
        this.valorVenda = valorVenda;
    }

    public boolean isPactoFlow() {
        return pactoFlow;
    }

    public void setPactoFlow(boolean pactoFlow) {
        this.pactoFlow = pactoFlow;
    }

    public String getParcelasSelecionadas() {
        return parcelasSelecionadas;
    }

    public void setParcelasSelecionadas(String parcelasSelecionadas) {
        this.parcelasSelecionadas = parcelasSelecionadas;
    }

    public Boolean getEnviarEmail() {
        return enviarEmail;
    }

    public void setEnviarEmail(Boolean enviarEmail) {
        this.enviarEmail = enviarEmail;
    }

    public String getUrlZw() {
        return urlZw;
    }

    public void setUrlZw(String urlZw) {
        this.urlZw = urlZw;
    }

    public Integer getPactoPayComunicacao() {
        if (pactoPayComunicacao == null) {
            pactoPayComunicacao = 0;
        }
        return pactoPayComunicacao;
    }

    public void setPactoPayComunicacao(Integer pactoPayComunicacao) {
        this.pactoPayComunicacao = pactoPayComunicacao;
    }

    public Boolean getTodasEmAberto() {
        return todasEmAberto;
    }

    public void setTodasEmAberto(Boolean todasEmAberto) {
        this.todasEmAberto = todasEmAberto;
    }

    public Integer getOrigem() {
        return origem;
    }

    public void setOrigem(Integer origem) {
        this.origem = origem;
    }
}
