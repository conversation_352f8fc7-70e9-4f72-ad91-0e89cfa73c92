/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.*;
import java.net.URL;
import java.sql.Connection;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import servlet.arquitetura.SuperServlet;

import static controle.arquitetura.SuperControle.getConfiguracaoSMTPNoReply;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoReciboServlet extends SuperServlet  {


    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        PrintWriter out = response.getWriter();
        JSONObject json = new JSONObject();
        try {
            Integer codigoRecibo = obterParametro(request.getParameter("recibo"));
            String chave = request.getParameter("chave");
            Boolean base64 = Boolean.parseBoolean(request.getParameter("base64"));
            boolean termica = (request.getParameter("term") != null && request.getParameter("term").equalsIgnoreCase("s"));
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);
            ReciboPagamento rcdao = new ReciboPagamento(con);
            Usuario usdao = new Usuario(con);
            request.getSession().setAttribute("key", chave);
            ReciboPagamentoVO reciboVO = rcdao.consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TODOS);
            String url = rcdao.imprimirReciboPDF(Boolean.FALSE, termica,
                    reciboVO.getEmpresa(),
                    reciboVO.getEmpresa(),
                    usdao.getUsuarioRecorrencia(),
                    reciboVO, null, "PDF", request);
            if(base64) {
                byte[] pdfbytes = baixarPDFDaURL(url);
                String bytesConvertidosBase64 = Base64.getEncoder().encodeToString(pdfbytes);
                json.put("base64", bytesConvertidosBase64);
            }
            json.put("url", url);

            if (request.getParameter("enviarEmail") != null && request.getParameter("enviarEmail").equalsIgnoreCase("TRUE")) {
                json = new JSONObject();
                String emails = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
                String matricula = request.getParameter("matricula");
                Cliente clienteDAO = new Cliente(con);
                List cliente = null;
                if(isNotBlank(matricula)) {
                    cliente = clienteDAO.consultarPorMatricula(matricula, 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                clienteDAO = null;
                if (UteisValidacao.emptyList(cliente)) {
                    json.put("sucesso", "false");
                    json.put("mensagem", "Cliente nao encontrado.");
                } else {
                    enviarReciboEmail(usdao, rcdao, reciboVO, ((ClienteVO) cliente.get(0)).getPessoa(), emails, request);
                    json.put("sucesso", "true");
                    json.put("mensagem", "E-mail enviado com sucesso");
                }
            }
            usdao = null;
            rcdao = null;
        } catch (Exception e) {
            try {
                json.put("sucesso", "false");
                json.put("mensagem", e.getMessage());
            } catch (JSONException ex) {
                Logger.getLogger(ImpressaoReciboServlet.class.getName()).log(Level.SEVERE, null, ex);
                ex.printStackTrace();
            }
        }
        out.println(json);
    }

    private byte[] baixarPDFDaURL(String url) throws IOException {
        try (InputStream in = new URL(url).openStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return out.toByteArray();
        }
    }

    private void enviarReciboEmail(Usuario usdao, ReciboPagamento reciboDAO, ReciboPagamentoVO recibo, PessoaVO pessoa, String emailsEnviarRecibo, HttpServletRequest request) throws Exception {
        if (!recibo.getPessoaPagador().getCodigo().equals(pessoa.getCodigo()) && !reciboDAO.reciboPagaProdutosPessoa(recibo.getCodigo(), pessoa.getCodigo())) {
            throw new Exception("Esse recibo não paga produtos dessa pessoa. Certifique-se que não está trabalhando com duas abas do navegador. Consulte novamente o aluno e tente novamente!");
        }

        if (UteisValidacao.emptyString(emailsEnviarRecibo)) {
            throw new Exception("Não foi possível enviar o recibo pois a pessoa não possui um email válido.");
        } else {
            File arquivo = new File(new File(request.getSession().getServletContext().getRealPath("/")).getAbsolutePath() + "/relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString());
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail email = new UteisEmail();
            email.novo("RECIBO", configuracaoSistemaCRMVO);
            email.setRemetente(usdao.getUsuarioRecorrencia());
            email = email.addAnexo(request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString(), arquivo);

            String[] emails = emailsEnviarRecibo.split(";");

            email.enviarEmailN(emails, getTextoEmail(pessoa, recibo), "RECIBO", recibo.getEmpresa_Apresentar());
        }
    }

    private String getTextoEmail(PessoaVO pessoa, ReciboPagamentoVO recibo) {
        return "Pagador: " + pessoa.getNome() + "<br/>" +
                "Data do recibo: " + Uteis.getData(recibo.getData());
    }
}
