¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî                       n  ¨        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~    w   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ *L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî                 pq ~ q ~ "pt line-1pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ *L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ 2p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ .L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ *L bottomBorderq ~ L bottomBorderColorq ~ *L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ GL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ EL isItalicq ~ EL 
isPdfEmbeddedq ~ EL isStrikeThroughq ~ EL isStyledTextq ~ EL isUnderlineq ~ EL 
leftBorderq ~ L leftBorderColorq ~ *L leftPaddingq ~ GL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ GL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ *L rightPaddingq ~ GL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ *L 
topPaddingq ~ GL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ )  wî   -              pq ~ q ~ "pt 
textField-214pppp~q ~ 4t FLOATppppq ~ 8  wîpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   
p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERsr java.lang.BooleanÍ rÕúî Z valuexpq ~ Ypppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ GL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ GL leftPenq ~ [L paddingq ~ GL penq ~ [L rightPaddingq ~ GL rightPenq ~ [L 
topPaddingq ~ GL topPenq ~ [xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Ixq ~ :  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ cxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ S?   q ~ ]q ~ ]q ~ Mpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ _  wîppq ~ fsq ~ h?   q ~ ]q ~ ]psq ~ _  wîppppq ~ ]q ~ ]psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ _  wîppq ~ fsq ~ h?   q ~ ]q ~ ]psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ _  wîppq ~ fsq ~ h?   q ~ ]q ~ ]pppppt Helvetica-BoldObliquepppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt filtrost java.lang.Stringppppppsq ~ X pppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ F  wî           Q   V   0pq ~ q ~ "pt staticText-2ppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ q ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ _  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ q  wîppppq ~ q ~ pppppt Helvetica-Boldppppppppppq ~ vt Plano sq ~   wî           5   ¦   0pq ~ q ~ "pt staticText-1ppppq ~ 5pppp~q ~ 7t RELATIVE_TO_TALLEST_OBJECT  wîppppppppq ~ Vq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ q ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ _  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ q  wîppppq ~ q ~ pppppt Helvetica-Boldppppppppppq ~ vt 	Cont/Prodsq ~   wî           t   Û   0pq ~ q ~ "pt staticText-8ppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ q ~ q ~ psq ~ j  wîppppq ~ q ~ psq ~ _  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ q  wîppppq ~ q ~ pppppt Helvetica-Boldppppppppppq ~ vt Clientesq ~   wî           4  L   0pq ~ q ~ "pt staticText-8ppppq ~ 5ppppq ~ 8  wîppppppppq ~ Vq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ ¨q ~ ¨q ~ ¦psq ~ j  wîppppq ~ ¨q ~ ¨psq ~ _  wîppppq ~ ¨q ~ ¨psq ~ n  wîppppq ~ ¨q ~ ¨psq ~ q  wîppppq ~ ¨q ~ ¨pppppt Helvetica-Boldppppppppppq ~ vt 	Valor(R$)sq ~ $  wî                 >pq ~ q ~ "pt line-1ppppq ~ 5ppppq ~ 8  wîppsq ~ :  wîppppq ~ °p  wî q ~ @sq ~   wî           !  +   0pq ~ q ~ "pt staticText-8ppppq ~ 5ppppq ~ 8  wîppppppppq ~ Vq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ µq ~ µq ~ ³psq ~ j  wîppppq ~ µq ~ µpsq ~ _  wîppppq ~ µq ~ µpsq ~ n  wîppppq ~ µq ~ µpsq ~ q  wîppppq ~ µq ~ µpppppt Helvetica-Boldppppppppppq ~ vt Tiposq ~   wî           w  O   0pq ~ q ~ "ppppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ ¾q ~ ¾q ~ ½psq ~ j  wîppppq ~ ¾q ~ ¾psq ~ _  wîppppq ~ ¾q ~ ¾psq ~ n  wîppppq ~ ¾q ~ ¾psq ~ q  wîppppq ~ ¾q ~ ¾pppppt Helvetica-Boldppppppppppq ~ vt 
Forma de Pag.sq ~   wî           V       0pq ~ q ~ "ppppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Ypq ~ ppppppsq ~ Zpsq ~ ^  wîppppq ~ Çq ~ Çq ~ Æpsq ~ j  wîppppq ~ Çq ~ Çpsq ~ _  wîppppq ~ Çq ~ Çpsq ~ n  wîppppq ~ Çq ~ Çpsq ~ q  wîppppq ~ Çq ~ Çpppppt Helvetica-Boldppppppppppq ~ vt Plano de Contassq ~ B  wî           e  Æ   0pq ~ q ~ "pt 
textField-208ppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ Ñq ~ Ñq ~ Ïpsq ~ j  wîppppq ~ Ñq ~ Ñpsq ~ _  wîppppq ~ Ñq ~ Ñpsq ~ n  wîppppq ~ Ñq ~ Ñpsq ~ q  wîppppq ~ Ñq ~ Ñpppppt Helvetica-Boldppppppppppp  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
tituloDatat java.lang.Stringppppppq ~ pppxp  wî   @ppq ~ ur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ /L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ /L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ /L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ ìppt 
JASPER_REPORTpsq ~ ïpppt (net.sf.jasperreports.engine.JasperReportpsq ~ ìppt REPORT_CONNECTIONpsq ~ ïpppt java.sql.Connectionpsq ~ ìppt REPORT_MAX_COUNTpsq ~ ïpppt java.lang.Integerpsq ~ ìppt REPORT_DATA_SOURCEpsq ~ ïpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ ìppt REPORT_SCRIPTLETpsq ~ ïpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ ìppt 
REPORT_LOCALEpsq ~ ïpppt java.util.Localepsq ~ ìppt REPORT_RESOURCE_BUNDLEpsq ~ ïpppt java.util.ResourceBundlepsq ~ ìppt REPORT_TIME_ZONEpsq ~ ïpppt java.util.TimeZonepsq ~ ìppt REPORT_FORMAT_FACTORYpsq ~ ïpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ ìppt REPORT_CLASS_LOADERpsq ~ ïpppt java.lang.ClassLoaderpsq ~ ìppt REPORT_URL_HANDLER_FACTORYpsq ~ ïpppt  java.net.URLStreamHandlerFactorypsq ~ ìppt REPORT_FILE_RESOLVERpsq ~ ïpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ ìppt REPORT_TEMPLATESpsq ~ ïpppt java.util.Collectionpsq ~ ïppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ .L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ .L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ {    uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ þpsq ~*  wî   q ~0ppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þpt 
COLUMN_NUMBERp~q ~:t PAGEq ~ þpsq ~*  wî   ~q ~/t COUNTsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(0)q ~ þpt REPORT_COUNTpq ~;q ~ þpsq ~*  wî   q ~Fsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(0)q ~ þpt 
PAGE_COUNTpq ~Cq ~ þpsq ~*  wî   q ~Fsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(0)q ~ þpt COLUMN_COUNTp~q ~:t COLUMNq ~ þp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    	w   	sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ Gxq ~ &  wî                sq ~ a    ÿ´ÍÍpppq ~ q ~opt rectangle-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 5sq ~ {   uq ~ ~   sq ~ t 
new Boolean((sq ~ t COLUMN_COUNTsq ~ t .intValue()%2)==0)t java.lang.Booleanppppq ~ 8  wîppsq ~ :  wîppq ~ fsq ~ h    q ~rppsq ~ B  wî           0  L   pq ~ q ~opt 	textFieldppppq ~ Opppp~q ~ 7t RELATIVE_TO_BAND_HEIGHT  wîppppppsq ~ R   pq ~ Vpppppppppsq ~ Zpsq ~ ^  wîppppq ~q ~q ~psq ~ j  wîppppq ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ q  wîppppq ~q ~ppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t valorLancamentoApresentart java.lang.Stringppppppq ~ Yppt  sq ~ B  wî           s   Ü   pq ~ q ~opt 
textField-224ppppq ~ 5ppppq ~ 8  wîppppppq ~p~q ~ Ut LEFTq ~ ppppppppsq ~ Zpsq ~ ^  wîppppq ~q ~q ~psq ~ j  wîppppq ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ q  wîppppq ~q ~ppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
nomePessoat java.lang.Stringppppppq ~ Ypppsq ~ B  wî           Q   U   pq ~ q ~opt 
textField-224ppppq ~ 5ppppq ~ 8  wîppppppq ~pq ~q ~ ppppppppsq ~ Zpsq ~ ^  wîppppq ~¥q ~¥q ~£psq ~ j  wîppppq ~¥q ~¥psq ~ _  wîppppq ~¥q ~¥psq ~ n  wîppppq ~¥q ~¥psq ~ q  wîppppq ~¥q ~¥ppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t descricaoLancamentot java.lang.Stringppppppq ~ Ypppsq ~ B  wî           5   ¦   pq ~ q ~opt 
textField-223ppppq ~ 5ppppq ~ 8  wîppppppq ~pq ~ Vpppppppppsq ~ Zpsq ~ ^  wîppppq ~²q ~²q ~°psq ~ j  wîppppq ~²q ~²psq ~ _  wîppppq ~²q ~²psq ~ n  wîppppq ~²q ~²psq ~ q  wîppppq ~²q ~²ppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t contratosq ~ t  != 0 ? sq ~ t contratosq ~ t  : sq ~ t 
codProdutot java.lang.Integerppppppq ~ Ypppsq ~ B  wî             +   pq ~ q ~oppppppq ~ 5ppppq ~ 8  wîppppppq ~pq ~ Vpppppppppsq ~ Zpsq ~ ^  wîppppq ~Æq ~Æq ~Åpsq ~ j  wîppppq ~Æq ~Æpsq ~ _  wîppppq ~Æq ~Æpsq ~ n  wîppppq ~Æq ~Æpsq ~ q  wîppppq ~Æq ~Æppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
tipoDescricaot java.lang.Stringppppppppppsq ~ B  wî           w  O   pq ~ q ~oppppppq ~ 5ppppq ~ 8  wîppppppq ~pppppppppppsq ~ Zpsq ~ ^  wîppppq ~Òq ~Òq ~Ñpsq ~ j  wîppppq ~Òq ~Òpsq ~ _  wîppppq ~Òq ~Òpsq ~ n  wîppppq ~Òq ~Òpsq ~ q  wîppppq ~Òq ~Òppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t descricaoFormaPagamentot java.lang.Stringppppppq ~ Ypppsq ~ B  wî           q       pq ~ q ~oppppppq ~ 5ppppq ~ 8  wîppppppq ~pppppppppppsq ~ Zpsq ~ ^  wîppppq ~Þq ~Þq ~Ýpsq ~ j  wîppppq ~Þq ~Þpsq ~ _  wîppppq ~Þq ~Þpsq ~ n  wîppppq ~Þq ~Þpsq ~ q  wîppppq ~Þq ~Þppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
planoFilhot java.lang.Stringppppppppppsq ~ B  wî           e  Æ   pq ~ q ~oppppppq ~ 5ppppq ~ 8  wîppppppq ~pq ~ Vpppppppppsq ~ Zpsq ~ ^  wîppppq ~êq ~êq ~épsq ~ j  wîppppq ~êq ~êpsq ~ _  wîppppq ~êq ~êpsq ~ n  wîppppq ~êq ~êpsq ~ q  wîppppq ~êq ~êppppppppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   
sq ~ t (sq ~ t tipoRelatorioDFsq ~ t .equals(2) || sq ~ t tipoRelatorioDFsq ~ t .equals(5) || sq ~ t tipoRelatorioDFsq ~ t .equals(6)) ? sq ~ t dataMesApresentarsq ~ t  : sq ~ t dataApresentart java.lang.Stringppppppppppxp  wî   ppq ~ ppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javasq ~ sq ~    
w   
sq ~   wî           P  Ê   0pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîpppppppp~q ~ Ut RIGHTpppppppppsq ~ Zpsq ~ ^  wîppppq ~q ~q ~psq ~ j  wîppppq ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ q  wîppppq ~q ~pppppt 	Helveticapppppppppppt Total SaÃ­da(-):sq ~ $  wî                pq ~ q ~
pt line-4ppppq ~ 5ppppq ~ 8  wîppsq ~ :  wîppppq ~p  wî q ~ @sq ~ $  wî                [pq ~ q ~
pt line-5ppppq ~ 5ppppq ~ 8  wîppsq ~ :  wîppppq ~p  wî q ~ @sq ~ $  wî                Ypq ~ q ~
pt line-6ppppq ~ 5ppppq ~ 8  wîppsq ~ :  wîppppq ~ p  wî q ~ @sq ~ B  wî                epq ~ q ~
pt 
textField-207ppppq ~ 5ppppq ~ 8  wîpppppt Arialq ~pppq ~ Ypppppppsq ~ Zpsq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~&q ~&q ~#psq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~&q ~&psq ~ _  wîppppq ~&q ~&psq ~ n  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~&q ~&psq ~ q  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~&q ~&pppppt Helvetica-Obliqueppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t " "+" UsuÃ¡rio:" + sq ~ t usuariot java.lang.Stringppppppq ~ ppq ~sq ~ B  wî   
        ~      hsq ~ a    ÿÿÿÿpppq ~ q ~
pt 	dataRel-1pq ~vppq ~ 5ppppq ~ 8  wîpppppt Arialq ~pq ~q ~ q ~ Ypppq ~ pppsq ~ Zsq ~ R   sq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~@q ~@q ~<psq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~@q ~@psq ~ _  wîppppq ~@q ~@psq ~ n  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~@q ~@psq ~ q  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~@q ~@p~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEpppt Helvetica-Obliqueppppppppppq ~ v  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
new Date()t java.util.Dateppppppq ~ Yppt dd/MM/yyyy HH.mm.sssq ~   wî           Q  Ê    pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppq ~pppppppppsq ~ Zpsq ~ ^  wîppppq ~Zq ~Zq ~Ypsq ~ j  wîppppq ~Zq ~Zpsq ~ _  wîppppq ~Zq ~Zpsq ~ n  wîppppq ~Zq ~Zpsq ~ q  wîppppq ~Zq ~Zpppppt 	Helveticapppppppppppt Total Entrada(+):sq ~   wî           Q  Ê   @pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppq ~pppppppppsq ~ Zpsq ~ ^  wîppppq ~cq ~cq ~bpsq ~ j  wîppppq ~cq ~cpsq ~ _  wîppppq ~cq ~cpsq ~ n  wîppppq ~cq ~cpsq ~ q  wîppppq ~cq ~cpppppt 	Helveticapppppppppppt 
Resultado(=):sq ~ B  wî           U      pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppq ~pppppppppsq ~ Zpsq ~ ^  wîppppq ~lq ~lq ~kpsq ~ j  wîppppq ~lq ~lpsq ~ _  wîppppq ~lq ~lpsq ~ n  wîppppq ~lq ~lpsq ~ q  wîppppq ~lq ~lppppppppppppppppp  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t totalEntradat java.lang.Stringppppppppppsq ~ B  wî           U     0pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppq ~pppppppppsq ~ Zpsq ~ ^  wîppppq ~xq ~xq ~wpsq ~ j  wîppppq ~xq ~xpsq ~ _  wîppppq ~xq ~xpsq ~ n  wîppppq ~xq ~xpsq ~ q  wîppppq ~xq ~xppppppppppppppppp  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
totalSaidat java.lang.Stringppppppppppsq ~ B  wî           U     ?pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppq ~pppppppppsq ~ Zpsq ~ ^  wîppppq ~q ~q ~psq ~ j  wîppppq ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ q  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~ ysq ~ {    uq ~ ~   sq ~ t 	resultadot java.lang.Stringppppppppppsq ~   wî           ]   ü   pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppppppppppppsq ~ Zpsq ~ ^  wîppppq ~q ~q ~psq ~ j  wîppppq ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ q  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Total de registros:sq ~ B  wî           3  Y   pq ~ q ~
ppppppq ~ 5ppppq ~ 8  wîppppppppppppppppppsq ~ Zpsq ~ ^  wîppppq ~q ~q ~psq ~ j  wîppppq ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ q  wîppppq ~q ~pppppt Helvetica-Boldppppppppppp  wî        ppq ~ ysq ~ {   !uq ~ ~   sq ~ t totalRegistrost java.lang.Integerppppppppppxp  wî   |ppq ~ sq ~ ß  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   
sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ /L valueClassNameq ~ L valueClassRealNameq ~ xpt  t descricaoLancamentosq ~ ïpppt java.lang.Stringpsq ~¨pt contratosq ~ ïpppt java.lang.Integerpsq ~¨t  t 
nomePessoasq ~ ïpppt java.lang.Stringpsq ~¨t  t 
movProdutosq ~ ïpppt java.lang.Integerpsq ~¨t  t tipoFormaPagto.descricaosq ~ ïpppt java.lang.Stringpsq ~¨pt movPagamentosq ~ ïpppt java.lang.Integerpsq ~¨pt valorLancamentoApresentarsq ~ ïpppt java.lang.Stringpsq ~¨pt tipoES.descricaosq ~ ïpppt java.lang.Stringpsq ~¨pt descricaoFormaPagamentosq ~ ïpppt java.lang.Stringpsq ~¨pt dataApresentarsq ~ ïpppt java.lang.Stringpsq ~¨pt dataMesApresentarsq ~ ïpppt java.lang.Stringpsq ~¨pt valorLancamentosq ~ ïpppt java.lang.Doublepsq ~¨pt 
codProdutosq ~ ïpppq ~ þpppt DemonstrativoFinanceiroReluq ~ ê   sq ~ ìppq ~ îpsq ~ ïpppq ~ òpsq ~ ìppq ~ ôpsq ~ ïpppq ~ öpsq ~ ìppq ~ øpsq ~ ïpppq ~ úpsq ~ ìppq ~ üpsq ~ ïpppq ~ þpsq ~ ìppq ~ psq ~ ïpppq ~psq ~ ìppq ~psq ~ ïpppq ~psq ~ ìppq ~psq ~ ïpppq ~
psq ~ ìppq ~psq ~ ïpppq ~psq ~ ìppq ~psq ~ ïpppq ~psq ~ ìppq ~psq ~ ïpppq ~psq ~ ìppq ~psq ~ ïpppq ~psq ~ ìppq ~psq ~ ïpppq ~psq ~ ìppq ~ psq ~ ïpppq ~"psq ~ ìppq ~$psq ~ ïpppq ~&psq ~ ìppt REPORT_VIRTUALIZERpsq ~ ïpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ ìppt IS_IGNORE_PAGINATIONpsq ~ ïpppq ~psq ~ ì  ppt logoPadraoRelatoriopsq ~ ïpppt java.io.InputStreampsq ~ ì  ppt tituloRelatoriopsq ~ ïpppt java.lang.Stringpsq ~ ì  ppt versaoSoftwarepsq ~ ïpppt java.lang.Stringpsq ~ ì  ppt usuariopsq ~ ïpppt java.lang.Stringpsq ~ ì  ppt filtrospsq ~ ïpppt java.lang.Stringpsq ~ ì sq ~ {    uq ~ ~   sq ~ t q"D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ ïpppq ~psq ~ ì ppt nomeEmpresapsq ~ ïpppt java.lang.Stringpsq ~ ì ppt enderecoEmpresapsq ~ ïpppt java.lang.Stringpsq ~ ì ppt 
cidadeEmpresapsq ~ ïpppt java.lang.Stringpsq ~ ì ppt totalEntradapsq ~ ïpppt java.lang.Stringpsq ~ ì ppt 
totalSaidapsq ~ ïpppt java.lang.Stringpsq ~ ì ppt 
tituloDatapsq ~ ïpppt java.lang.Stringpsq ~ ì ppt 	resultadopsq ~ ïpppt java.lang.Stringpsq ~ ì ppt tipoRelatorioDFpsq ~ ïpppt java.lang.Integerpsq ~ ì ppt 
planoFilhopsq ~ ïpppt java.lang.Stringpsq ~ ïpsq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~It 1.8150000000000062q ~Ht 
ISO-8859-1q ~Jt 0q ~Kt 0q ~Gt 0xpppppuq ~(   sq ~*  wî   q ~0ppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þpq ~9pq ~;q ~ þpsq ~*  wî   q ~0ppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þpq ~Bpq ~Cq ~ þpsq ~*  wî   q ~Fsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(0)q ~ þpq ~Ppq ~;q ~ þpsq ~*  wî   q ~Fsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(0)q ~ þpq ~Zpq ~Cq ~ þpsq ~*  wî   q ~Fsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(1)q ~ þppq ~3ppsq ~ {   uq ~ ~   sq ~ t new java.lang.Integer(0)q ~ þpq ~dpq ~eq ~ þpsq ~*  wî    q ~Fsq ~ {   	uq ~ ~   sq ~ t contratot java.lang.Objectppq ~3pppt totalRegistrospq ~;t java.lang.Integerpsq ~*  wî    ~q ~/t NOTHINGsq ~ {   
uq ~ ~   sq ~ t ( sq ~ t valorLancamentosq ~ t <0.0 ?  "SaÃ­da":"Entrada" )t java.lang.Stringppq ~3pppt 
tipoDescricaopq ~;q ~p~q ~gt EMPTYq ~àp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~    	w   	sq ~ B  wî             X   3pq ~ q ~pt 
textField-212ppppq ~ 5ppppq ~ 8  wîpppppt Arialq ~ Tppq ~ Yppppppppsq ~ Zq ~Asq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~q ~q ~psq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~q ~psq ~ q  wîsq ~ a    ÿ   ppppq ~ fsq ~ h    q ~q ~pppppt Helvetica-Boldppppppppppp  wî        pp~q ~ xt REPORTsq ~ {   uq ~ ~   sq ~ t " " + sq ~ t PAGE_NUMBERsq ~ t  + ""t java.lang.Stringppppppq ~ pppsq ~   wî           o  
   #pq ~ q ~pt 
staticText-15pq ~vppq ~ 5ppppq ~ 8  wîpppppt Microsoft Sans Serifsq ~ R   	pq ~q ~ Yq ~ Ypq ~ pq ~ pppsq ~ Zpsq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~¸q ~¸q ~´psq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~¸q ~¸psq ~ _  wîppppq ~¸q ~¸psq ~ n  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~¸q ~¸psq ~ q  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~¸q ~¸pq ~Ppppt Helvetica-BoldObliquepppppppppp~q ~ ut TOPt (0xx62) 3251-5820sq ~ B  wî           K     3pq ~ q ~pt 
textField-211ppppq ~ 5ppppq ~ 8  wîpppppt Arialq ~ Tpq ~q ~ Yppppppppsq ~ Zq ~Asq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~Íq ~Íq ~Êpsq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~Íq ~Ípsq ~ _  wîppppq ~Íq ~Ípsq ~ n  wîsq ~ a    ÿ   ppppq ~ fsq ~ h    q ~Íq ~Ípsq ~ q  wîsq ~ a    ÿ   ppppq ~ fsq ~ h    q ~Íq ~Ípppppt Helvetica-Boldppppppppppp  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t "PÃ¡gina: " + sq ~ t PAGE_NUMBERsq ~ t 	 + " de "t java.lang.Stringppppppq ~ pppsq ~   wî             Ï   #pq ~ q ~pt 
staticText-13ppppq ~ 5ppppq ~ 8  wîppppppsq ~ R   pq ~ Vq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~èq ~èq ~åpsq ~ j  wîppppq ~èq ~èpsq ~ _  wîppppq ~èq ~èpsq ~ n  wîppppq ~èq ~èpsq ~ q  wîppppq ~èq ~èpppppt Helvetica-Boldpppppppppppt LanÃ§amentossq ~ B  wî           q   V   pq ~ q ~pt 
textField-209ppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~òq ~òq ~ðpsq ~ j  wîppppq ~òq ~òpsq ~ _  wîppppq ~òq ~òpsq ~ n  wîppppq ~òq ~òpsq ~ q  wîppppq ~òq ~òpppppt Helvetica-Boldppppppppppp  wî        ppq ~ ysq ~ {   
uq ~ ~   sq ~ t enderecoEmpresat java.lang.Stringppppppq ~ pppsq ~ B  wî           q   V   pq ~ q ~pt 
textField-208ppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~ q ~ q ~þpsq ~ j  wîppppq ~ q ~ psq ~ _  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ q  wîppppq ~ q ~ pppppt Helvetica-Boldppppppppppp  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t nomeEmpresat java.lang.Stringppppppq ~ pppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ *L bottomBorderq ~ L bottomBorderColorq ~ *L 
bottomPaddingq ~ GL evaluationGroupq ~ .L evaluationTimeValueq ~ CL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ HL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ DL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ EL 
leftBorderq ~ L leftBorderColorq ~ *L leftPaddingq ~ GL lineBoxq ~ IL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ GL rightBorderq ~ L rightBorderColorq ~ *L rightPaddingq ~ GL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ *L 
topPaddingq ~ GL verticalAlignmentq ~ L verticalAlignmentValueq ~ Lxq ~ &  wî   .       R       pq ~ q ~pt image-1ppppq ~ 5ppppq ~ 8  wîppsq ~ :  wîppppq ~p  wî         ppppppp~q ~ xt PAGEsq ~ {   uq ~ ~   sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Ypppsq ~ Zpsq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~q ~q ~psq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~q ~psq ~ _  wîppppq ~q ~psq ~ n  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~q ~psq ~ q  wîsq ~ a    ÿfffppppq ~ fsq ~ h?   q ~q ~pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppppsq ~   wî            s   pq ~ q ~pt 
staticText-14pq ~vppq ~ 5ppppq ~ 8  wîpppppt Microsoft Sans Serifq ~·pq ~q ~ Yq ~ Ypq ~ pq ~ pppsq ~ Zpsq ~ ^  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~0q ~0q ~-psq ~ j  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~0q ~0psq ~ _  wîppppq ~0q ~0psq ~ n  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~0q ~0psq ~ q  wîsq ~ a    ÿfffppppq ~ fsq ~ h    q ~0q ~0pq ~Ppppt Helvetica-BoldObliqueppppppppppq ~Çt eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ B  wî           q   V   /pq ~ q ~pt 
textField-210ppppq ~ 5ppppq ~ 8  wîpppppppppq ~ Yppppppppsq ~ Zpsq ~ ^  wîppppq ~Bq ~Bq ~@psq ~ j  wîppppq ~Bq ~Bpsq ~ _  wîppppq ~Bq ~Bpsq ~ n  wîppppq ~Bq ~Bpsq ~ q  wîppppq ~Bq ~Bpppppt Helvetica-Boldppppppppppp  wî        ppq ~ ysq ~ {   uq ~ ~   sq ~ t 
cidadeEmpresat java.lang.Stringppppppq ~ pppxp  wî   Eppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~     w    xp  wî    ppq ~ psq ~ sq ~     w    xp  wî    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ ðL datasetCompileDataq ~ ðL mainDatasetCompileDataq ~ xpsq ~L?@     w       xsq ~L?@     w      q ~ éur [B¬óøTà  xp  Êþº¾   .  5DemonstrativoFinanceiroRel_Teste_1746538040325_371070  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c   F G     4     *+· K*,· N*-· Q±    E       :  ; 
 <  =  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    E  F $ G 6 H H I Z J l K ~ L  M ¢ N ´ O Æ P Ø Q ê R ü S  L I           ±    E       [  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       c  d $ e 6 f H g Z h              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    p  r 0 v 9 w < { E | H  Q  T  ]  `  i  l  u  x       ¡              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ª  ¬ 0 ° 9 ± < µ E ¶ H º Q » T ¿ ] À ` Ä i Å l É u Ê x Î  Ï  Ó  Û              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ä  æ 0 ê 9 ë < ï E ð H ô Q õ T ù ] ú ` þ i ÿ l u x 	 
       xuq ~\  'ñÊþº¾   .{ /DemonstrativoFinanceiroRel_1746538040325_371070  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_tituloData parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_planoFilho parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_resultado parameter_totalSaida parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_tipoRelatorioDF parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa parameter_totalEntrada parameter_cidadeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_nomePessoa .Lnet/sf/jasperreports/engine/fill/JRFillField; field_valorLancamento field_valorLancamentoApresentar field_dataApresentar field_movPagamento field_dataMesApresentar field_tipoES46descricao field_contrato field_movProduto field_tipoFormaPagto46descricao field_descricaoFormaPagamento field_codProduto field_descricaoLancamento variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_totalRegistros variable_tipoDescricao <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r   	  t ! 	  v " 	  x # 	  z $ 	  | % &	  ~ ' &	   ( &	   ) &	   * &	   + &	   , &	   - &	   . &	   / &	   0 &	   1 &	   2 &	   3 4	   5 4	   6 4	   7 4	   8 4	    9 4	  ¢ : 4	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± enderecoEmpresa ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » 
tituloData ½ 
JASPER_REPORT ¿ REPORT_TIME_ZONE Á usuario Ã REPORT_FILE_RESOLVER Å 
planoFilho Ç REPORT_PARAMETERS_MAP É REPORT_CLASS_LOADER Ë REPORT_URL_HANDLER_FACTORY Í REPORT_DATA_SOURCE Ï IS_IGNORE_PAGINATION Ñ REPORT_MAX_COUNT Ó REPORT_TEMPLATES Õ 	resultado × 
totalSaida Ù 
REPORT_LOCALE Û REPORT_VIRTUALIZER Ý logoPadraoRelatorio ß REPORT_SCRIPTLET á tipoRelatorioDF ã REPORT_CONNECTION å 
SUBREPORT_DIR ç REPORT_FORMAT_FACTORY é tituloRelatorio ë nomeEmpresa í totalEntrada ï 
cidadeEmpresa ñ REPORT_RESOURCE_BUNDLE ó versaoSoftware õ filtros ÷ 
nomePessoa ù ,net/sf/jasperreports/engine/fill/JRFillField û valorLancamento ý valorLancamentoApresentar ÿ dataApresentar movPagamento dataMesApresentar tipoES.descricao contrato	 
movProduto tipoFormaPagto.descricao
 descricaoFormaPagamento 
codProduto descricaoLancamento PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT COLUMN_COUNT totalRegistros! 
tipoDescricao# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( dD:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\java\relatorio\designRelatorio\outros\* java/lang/Integer, (I)V ;.
-/ getValue ()Ljava/lang/Object;12
 ü3 java/lang/Double5 doubleValue ()D78
69 SaÃ­da; Entrada= java/lang/StringBuffer?  A (Ljava/lang/String;)V ;C
@D
3 append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;GH
@I toString ()Ljava/lang/String;KL
@M 	PÃ¡gina: O  de Q ,(Ljava/lang/String;)Ljava/lang/StringBuffer;GS
@T
 ¼3 java/lang/StringW java/io/InputStreamY java/lang/Boolean[ intValue ()I]^
-_ (Z)V ;a
\b valueOf (I)Ljava/lang/Integer;de
-f equals (Ljava/lang/Object;)Zhi
-j   UsuÃ¡rio:l java/util/Daten
o > evaluateOld getOldValuer2
 üs
s evaluateEstimated getEstimatedValuew2
x 
SourceFile !     3                 	     
               
                                                                                                     !     "     #     $     % &    ' &    ( &    ) &    * &    + &    , &    - &    . &    / &    0 &    1 &    2 &    3 4    5 4    6 4    7 4    8 4    9 4    : 4     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       Z  [ 
 \  ]  © ª  =  Ã    /*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _*+Ü¹ º À ¼À ¼µ a*+Þ¹ º À ¼À ¼µ c*+à¹ º À ¼À ¼µ e*+â¹ º À ¼À ¼µ g*+ä¹ º À ¼À ¼µ i*+æ¹ º À ¼À ¼µ k*+è¹ º À ¼À ¼µ m*+ê¹ º À ¼À ¼µ o*+ì¹ º À ¼À ¼µ q*+î¹ º À ¼À ¼µ s*+ð¹ º À ¼À ¼µ u*+ò¹ º À ¼À ¼µ w*+ô¹ º À ¼À ¼µ y*+ö¹ º À ¼À ¼µ {*+ø¹ º À ¼À ¼µ }±    ¦        e  f $ g 6 h H i Z j l k ~ l  m ¢ n ´ o Æ p Ø q ê r ü s t  u2 vD wV xh yz z { |° }Â ~Ô æ ø 
  .   ­ ª  =  B     ö*+ú¹ º À üÀ üµ *+þ¹ º À üÀ üµ *+ ¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ *+
¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ *+¹ º À üÀ üµ ±    ¦   :       $  7  J  ]  p      ©  ¼  Ï  â  õ   ° ª  =   º     *+¹ º ÀÀµ *+¹ º ÀÀµ *+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦   "    ¡  ¢ & £ 9 ¤ L ¥ _ ¦ r §  ¨ %& '    ) =  G    Mª         !         ¨   ´   À   Ì   Ø   ä   ð   ü  
  )  G  k  y      £  ±  ¿  á  ï  ý    6  D  R  `  ¸  Ö  á  ï  ý  +M§}»-Y·0M§q»-Y·0M§e»-Y·0M§Y»-Y·0M§M»-Y·0M§A»-Y·0M§5»-Y·0M§)»-Y·0M§*´ ¶4À-M§*´ ¶4À6¶: 	<§ >M§ð»@YB·E*´ ¶FÀ-¶J¶NM§Ò»@YP·E*´ ¶FÀ-¶JR¶U¶NM§®*´ A¶VÀXM§ *´ s¶VÀXM§*´ e¶VÀZM§*´ w¶VÀXM§v*´ }¶VÀXM§h*´ C¶VÀXM§Z»\Y*´ ¡¶FÀ-¶`p § ·cM§8*´ ¶4ÀXM§**´ ¶4ÀXM§*´ ¶4ÀXM§*´ ¶4À-¶` *´ ¶4À-§ 
*´ ¶4À-M§ ã*´ ¥¶FÀXM§ Õ*´ ¶4ÀXM§ Ç*´ M¶VÀXM§ ¹*´ i¶VÀ-¸g¶k ,*´ i¶VÀ-¸g¶k *´ i¶VÀ-¸g¶k *´ ¶4ÀX§ 
*´ ¶4ÀXM§ a»@Ym·E*´ I¶VÀX¶U¶NM§ C»oY·pM§ 8*´ u¶VÀXM§ **´ _¶VÀXM§ *´ ]¶VÀXM§ *´ £¶FÀ-M,°    ¦   F   °  ²  ¶  ·  » ¨ ¼ « À ´ Á · Å À Æ Ã Ê Ì Ë Ï Ï Ø Ð Û Ô ä Õ ç Ù ð Ú ó Þ ü ß ÿ ã
 ä
 è) é, íG îJ òk ón ÷y ø| ü ý£¦±´¿Âáäïòý  $%)6*9.D/G3R4U8`9c=¸>»BÖCÙGáHäLïMòQýR VW[c q& '    ) =  G    Mª         !         ¨   ´   À   Ì   Ø   ä   ð   ü  
  )  G  k  y      £  ±  ¿  á  ï  ý    6  D  R  `  ¸  Ö  á  ï  ý  +M§}»-Y·0M§q»-Y·0M§e»-Y·0M§Y»-Y·0M§M»-Y·0M§A»-Y·0M§5»-Y·0M§)»-Y·0M§*´ ¶tÀ-M§*´ ¶tÀ6¶: 	<§ >M§ð»@YB·E*´ ¶uÀ-¶J¶NM§Ò»@YP·E*´ ¶uÀ-¶JR¶U¶NM§®*´ A¶VÀXM§ *´ s¶VÀXM§*´ e¶VÀZM§*´ w¶VÀXM§v*´ }¶VÀXM§h*´ C¶VÀXM§Z»\Y*´ ¡¶uÀ-¶`p § ·cM§8*´ ¶tÀXM§**´ ¶tÀXM§*´ ¶tÀXM§*´ ¶tÀ-¶` *´ ¶tÀ-§ 
*´ ¶tÀ-M§ ã*´ ¥¶uÀXM§ Õ*´ ¶tÀXM§ Ç*´ M¶VÀXM§ ¹*´ i¶VÀ-¸g¶k ,*´ i¶VÀ-¸g¶k *´ i¶VÀ-¸g¶k *´ ¶tÀX§ 
*´ ¶tÀXM§ a»@Ym·E*´ I¶VÀX¶U¶NM§ C»oY·pM§ 8*´ u¶VÀXM§ **´ _¶VÀXM§ *´ ]¶VÀXM§ *´ £¶uÀ-M,°    ¦   F  l n r s w ¨x «| ´} · À Ã Ì Ï Ø Û ä ç ð ó ü ÿ
 
¤)¥,©GªJ®k¯n³y´|¸¹½¾Â£Ã¦Ç±È´Ì¿ÍÂÑáÒäÖï×òÛýÜ àáå6æ9êDëGïRðUô`õcù¸ú»þÖÿÙáäï	ò
ý  v& '    ) =  G    Mª         !         ¨   ´   À   Ì   Ø   ä   ð   ü  
  )  G  k  y      £  ±  ¿  á  ï  ý    6  D  R  `  ¸  Ö  á  ï  ý  +M§}»-Y·0M§q»-Y·0M§e»-Y·0M§Y»-Y·0M§M»-Y·0M§A»-Y·0M§5»-Y·0M§)»-Y·0M§*´ ¶4À-M§*´ ¶4À6¶: 	<§ >M§ð»@YB·E*´ ¶yÀ-¶J¶NM§Ò»@YP·E*´ ¶yÀ-¶JR¶U¶NM§®*´ A¶VÀXM§ *´ s¶VÀXM§*´ e¶VÀZM§*´ w¶VÀXM§v*´ }¶VÀXM§h*´ C¶VÀXM§Z»\Y*´ ¡¶yÀ-¶`p § ·cM§8*´ ¶4ÀXM§**´ ¶4ÀXM§*´ ¶4ÀXM§*´ ¶4À-¶` *´ ¶4À-§ 
*´ ¶4À-M§ ã*´ ¥¶yÀXM§ Õ*´ ¶4ÀXM§ Ç*´ M¶VÀXM§ ¹*´ i¶VÀ-¸g¶k ,*´ i¶VÀ-¸g¶k *´ i¶VÀ-¸g¶k *´ ¶4ÀX§ 
*´ ¶4ÀXM§ a»@Ym·E*´ I¶VÀX¶U¶NM§ C»oY·pM§ 8*´ u¶VÀXM§ **´ _¶VÀXM§ *´ ]¶VÀXM§ *´ £¶yÀ-M,°    ¦   F  ( * . / 3 ¨4 «8 ´9 ·= À> ÃB ÌC ÏG ØH ÛL äM çQ ðR óV üW ÿ[
\
`)a,eGfJjkknoyp|tuyz~£¦±´¿Âáäïòý ¡6¢9¦D§G«R¬U°`±cµ¸¶»ºÖ»Ù¿áÀäÄïÅòÉýÊ ÎÏÓÛ z    t _1746538040325_371070t 2net.sf.jasperreports.engine.design.JRJavacCompiler