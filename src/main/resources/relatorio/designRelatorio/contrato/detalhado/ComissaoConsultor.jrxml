<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ComissaoConsultor" pageWidth="823" pageHeight="535" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="823" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="modoVisualizacao" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<field name="configuracao_apresentar" class="java.lang.String"/>
	<field name="listaComissoes" class="java.lang.Object"/>
	<field name="valorTotalConfiguracao" class="java.lang.String"/>
	<field name="valorComissaoConfiguracao" class="java.lang.String"/>
	<field name="qtdAlunos" class="java.lang.Integer"/>
	<field name="qtdComissoes" class="java.lang.Integer"/>
	<field name="qtdContratos" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="45" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="20" width="823" height="25">
					<printWhenExpression><![CDATA[$P{modoVisualizacao}.equals( "A" ) || $P{modoVisualizacao}.equals( "AP" )]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="modoVisualizacao">
					<subreportParameterExpression><![CDATA[$P{modoVisualizacao}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[$F{listaComissoes}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "ComissaoConsultorItem.jasper"]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement isPrintRepeatedValues="false" x="30" y="0" width="237" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{configuracao_apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="20" splitType="Prevent">
			<printWhenExpression><![CDATA[$F{qtdComissoes} > 1]]></printWhenExpression>
			<textField>
				<reportElement x="70" y="0" width="140" height="20"/>
				<textElement textAlignment="Center">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdContratos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="778" y="0" width="45" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorComissaoConfiguracao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="733" y="0" width="45" height="20"/>
				<textElement textAlignment="Right">
					<font isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotalConfiguracao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="735" y="0" width="43" height="1"/>
				<graphicElement>
					<pen lineWidth="0.7"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="780" y="0" width="43" height="1"/>
				<graphicElement>
					<pen lineWidth="0.7"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="70" y="0" width="140" height="1"/>
				<graphicElement>
					<pen lineWidth="0.7" lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
