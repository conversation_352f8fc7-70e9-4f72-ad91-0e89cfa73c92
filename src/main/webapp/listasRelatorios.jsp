<%--
    Document   : listasRelatorios
    Created on : 20/06/2013, 16:03:21
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<jsp:include page="include_head.jsp" flush="true" />

<style type="text/css">
    .rich-stglpanel-header {
        color: #0f4c6b;
    }

</style>
<style type="text/css">
    .rich-stglpanel-header {
        background-color: #ACBECE;
        border-color: #ACBECE;
        font-size: 12px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatórios"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">

        <c:if test="${empty contexto}">

        </c:if>
        <c:if test="${not empty contexto}">
            <%@include file="pages/ce/includes/include_head.jsp" %>
        </c:if>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Relatório Geral de Clientes" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-utilizar-os-filtros-do-geral-de-clientes/"
                                                      title="Clique e saiba mais: Relatório Geral de Clientes"
                                                      target="_blank" >
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup id="panelGeralClientes" layout="block" styleClass="margin-box">
                                    <h:outputText styleClass="text" value="Filtros" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                                    <rich:spacer height="25px"/>
                                    <rich:simpleTogglePanel switchType="client" label="Dados pessoais" style="margin-top: 8px" opened="true" onexpand="false">
                                        <h:panelGrid columns="2" cellspacing="5px" cellpadding="5px" >
                                            <h:panelGroup id="nomeCliente">
                                                <h:outputText styleClass="text"  value="Nome: "/>
                                                <h:inputText id="nome" size="46" value="#{ListasERelatoriosControle.nome}"/>


                                                <h:outputText rendered="#{ListasERelatoriosControle.usuarioLogado.administrador}" style="margin-left: 5px"
                                                              styleClass="text" value="Empresa: "/>
                                                <h:selectOneMenu rendered="#{ListasERelatoriosControle.usuarioLogado.administrador}"
                                                                 id="empresa" value="#{ListasERelatoriosControle.codigoEmpresa}">
                                                    <f:selectItem itemValue="" itemLabel=""/>
                                                    <f:selectItems value="#{ListasERelatoriosControle.listaEmpresa}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                            <h:panelGroup>
                                                <h:outputText styleClass="text"  value="#{ListasERelatoriosControle.displayIdentificadorFront[1]}: "/>
                                                <h:inputText id="rg" size="17" value="#{ListasERelatoriosControle.rg}"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid columns="2" cellspacing="5px" cellpadding="5px">
                                            <h:panelGroup id="dataCadastro">
                                                <h:outputText styleClass="text" value="Data de Cadastro: "/>
                                                <rich:calendar id="inicioCadastro"
                                                               value="#{ListasERelatoriosControle.inicioCadastro}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="fimCadastro"
                                                               value="#{ListasERelatoriosControle.fimCadastro}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <a4j:commandButton  id="limparCadastro" style="margin-left: 5px"
                                                                    action="#{ListasERelatoriosControle.limparCadastro}"
                                                                    image="/images/limpar.gif" title="Limpar período de cadastro."
                                                                    reRender="inicioCadastro, fimCadastro"/>

                                                <h:outputText style="margin-left: 7px" styleClass="text" value="Idade: "/>
                                                <f:selectItems value="#{ListasERelatoriosControle.inicioIdade}"/>
                                                <h:inputText id="inicioIdade" size="5"  value="#{ListasERelatoriosControle.inicioIdade}"/>

                                                <h:outputText styleClass="text" value=" a "/>
                                                <h:inputText id="fimIdade" size="5" value="#{ListasERelatoriosControle.fimIdade}"/>
                                                <f:selectItems value="#{ListasERelatoriosControle.fimIdade}"/>


                                                <h:outputText style="margin-left: 7px" styleClass="text" value="Sexo biológico: "/>
                                                <h:selectOneMenu id="sexo"   value="#{ListasERelatoriosControle.sexo}">
                                                    <f:selectItem itemValue="" itemLabel=""/>
                                                    <f:selectItems  value="#{ListasERelatoriosControle.listaSelectItemSexo}" />
                                                </h:selectOneMenu>

                                                <h:outputText style="margin-left: 7px" styleClass="text" value="Categoria: "/>
                                                <h:selectOneMenu id="categoria"   value="#{ListasERelatoriosControle.categoria}">
                                                    <f:selectItems  value="#{ListasERelatoriosControle.listaCategorias}" />
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid columns="1" cellspacing="5px" cellpadding="5px">
                                            <h:panelGroup id="bairroCliente">
                                                <h:outputText styleClass="text" value="Bairro: "/>
                                                <f:selectItems value="#{ListasERelatoriosControle.bairro}"/>
                                                <h:inputText id="bairro" size="42" value="#{ListasERelatoriosControle.bairro}"/>

                                                <h:outputText style="margin-left: 5px;" styleClass="text" value="Aniversáriantes do Mês: "/>
                                                <h:selectOneMenu id="anivMes" value="#{ListasERelatoriosControle.codigoMes}">
                                                    <f:selectItems value="#{ListasERelatoriosControle.listaMeses}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </rich:simpleTogglePanel>
                                    <rich:simpleTogglePanel switchType="client" label="Dados de contrato" opened="false" onexpand="true">
                                        <h:panelGrid cellspacing="5px" cellpadding="5px" >
                                            <h:panelGroup id="dataVencimento">
                                                <h:outputText styleClass="text" value="Data de Vencimento: "/>
                                                <rich:calendar id="inicioVencimento"
                                                               value="#{ListasERelatoriosControle.inicioVencimento}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <rich:spacer width="5px"/>
                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="fimVencimento"
                                                               value="#{ListasERelatoriosControle.fimVencimento}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />
                                                <a4j:commandButton  id="limparVencimento" style="margin-left: 5px; vertical-align: middle"
                                                                    action="#{ListasERelatoriosControle.limparVencimento}"
                                                                    image="/images/limpar.gif" title="Limpar período de vencimento."
                                                                    reRender="inicioVencimento, fimVencimento"/>

                                                <h:outputText style="margin-left: 20px" styleClass="text" value="Data de Matrícula: "/>
                                                <rich:calendar id="inicioMatricula"
                                                               value="#{ListasERelatoriosControle.inicioMatricula}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="fimMatricula"
                                                               value="#{ListasERelatoriosControle.fimMatricula}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <a4j:commandButton  id="limparMatricula" style="margin-left: 5px; vertical-align: middle"
                                                                    action="#{ListasERelatoriosControle.limparMatricula}"
                                                                    image="/images/limpar.gif" title="Limpar período de matrícula."
                                                                    reRender="inicioMatricula, fimMatricula"/>

                                            </h:panelGroup>

                                            <h:panelGroup id="dataRematriculaRenovacao">
                                                <h:outputText styleClass="text" value="Data de Rematrícula: "/>
                                                <rich:calendar id="inicioRematricula"
                                                               value="#{ListasERelatoriosControle.inicioRematricula}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <rich:spacer width="5px"/>
                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="finalRematricula"
                                                               value="#{ListasERelatoriosControle.finalRematricula}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />
                                                <a4j:commandButton  id="limparRematricula" style="margin-left: 5px; vertical-align: middle"
                                                                    action="#{ListasERelatoriosControle.limparRematricula}"
                                                                    image="/images/limpar.gif" title="Limpar período de vencimento."
                                                                    reRender="inicioRematricula, finalRematricula"/>

                                                <h:outputText style="margin-left: 20px" styleClass="text" value="Data de Renovação: "/>
                                                <rich:calendar id="inicioRenovacao"
                                                               value="#{ListasERelatoriosControle.inicioRenovacao}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="finalRenovacao"
                                                               value="#{ListasERelatoriosControle.finalRenovacao}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />
                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                <a4j:commandButton  id="limparRenovacao" style="margin-left: 5px; vertical-align: middle"
                                                                    action="#{ListasERelatoriosControle.limparRenovacao}"
                                                                    image="/images/limpar.gif" title="Limpar período de matrícula."
                                                                    reRender="inicioRenovacao, finalRenovacao"/>
                                            </h:panelGroup>

                                            <h:panelGroup id="panelDataLancamento">
                                                <h:outputText styleClass="text" value="Data de Lançamento: "/>
                                                <rich:calendar id="inicioLancamento"
                                                               value="#{ListasERelatoriosControle.inicioLancamento}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />

                                                <rich:spacer width="5px"/>
                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="finalLancamento"
                                                               value="#{ListasERelatoriosControle.finalLancamento}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />
                                                <a4j:commandButton  id="limparLancamento" style="margin-left: 5px; vertical-align: middle"
                                                                    action="#{ListasERelatoriosControle.limparLancamento}"
                                                                    status="false"
                                                                    image="/images/limpar.gif" title="Limpar período de lançamento."
                                                                    reRender="panelDataLancamento"/>

                                                <h:panelGroup style="margin-left: 20px">
                                                    <h:selectBooleanCheckbox styleClass="text" value="#{ListasERelatoriosControle.ultimoContratoCliente}" id="ultimoContratoCliente"/>
                                                    <h:outputText  styleClass="text" value="Apenas o último contrato do cliente" />
                                                </h:panelGroup>
                                            </h:panelGroup>

                                        </h:panelGrid>

                                        <h:panelGrid cellspacing="5px" cellpadding="5px">
                                            <h:panelGroup id="linhaEvento">
                                                <h:outputText styleClass="text" value="Evento: "/>
                                                <h:selectOneMenu id="nomeEvento"
                                                                 value="#{ListasERelatoriosControle.codigoEventoSelecionado}">
                                                    <f:selectItems
                                                            value="#{ListasERelatoriosControle.listaSelectItemEvento}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGrid cellspacing="5px" cellpadding="5px">
                                            <h:panelGroup id="diasSemComp">
                                                <h:outputText styleClass="text" value="Dias sem Comparecer: "/>
                                                <h:inputText id="inicioDias" size="5"  value="#{ListasERelatoriosControle.inicioDiasSemAparecer}"/>
                                                <h:outputText styleClass="text" value="até: "/>
                                                <h:inputText id="fimDias" size="5" value="#{ListasERelatoriosControle.fimDiasSemAparecer}"/>


                                                <h:panelGroup style="margin-left: 20px">
                                                    <h:outputText styleClass="text" value="Duração: "/>
                                                    <h:selectOneMenu  id="duracao"
                                                                      value="#{ListasERelatoriosControle.duracao}" >
                                                        <f:selectItems  value="#{ListasERelatoriosControle.listaDuracoesContratos}" />
                                                    </h:selectOneMenu>
                                                </h:panelGroup>

                                                <h:panelGroup style="margin-left: 20px">
                                                    <h:outputText styleClass="text" value="Convênio: "/>
                                                    <h:selectOneMenu  id="convenio"
                                                                      value="#{ListasERelatoriosControle.convenio}" >
                                                        <f:selectItems  value="#{ListasERelatoriosControle.listaSelectItemConvenios}" />
                                                    </h:selectOneMenu>
                                                </h:panelGroup>

                                                <h:panelGroup style="margin-left: 20px">
                                                    <h:outputText styleClass="text" value="Plano: "/>
                                                    <h:selectOneMenu  id="nomePlano"
                                                                      value="#{ListasERelatoriosControle.plano}" >
                                                        <f:selectItems  value="#{ListasERelatoriosControle.listaSelectItemPlanos}" />
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGrid cellspacing="5px" cellpadding="5px">
                                            <h:panelGroup id="linhaPacote">
                                                <h:outputText styleClass="text" value="Pacote: "/>
                                                <h:selectOneMenu id="nomePacote"
                                                                 value="#{ListasERelatoriosControle.pacote}">
                                                    <f:selectItems
                                                            value="#{ListasERelatoriosControle.listaSelectItemPacotes}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGrid cellspacing="5px" cellpadding="5px">
                                            <h:panelGroup id="linhaParq">
                                                <h:outputText styleClass="text" value="Par-Q: "/>
                                                <h:selectOneMenu id="parq" value="#{ListasERelatoriosControle.filtroParQ}">
                                                    <f:selectItem itemValue="" itemLabel=""/>
                                                    <f:selectItems  value="#{ListasERelatoriosControle.listaSelectItemParQ}" />
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGrid columns="8" style="margin-top:6px;">
                                            <h:panelGroup>
                                                <fieldset style="height: 100%;width: 100%;vertical-align: top;">
                                                    <legend style="font-size:9pt;">Situação: </legend>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoAtivo}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_ativo}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoNormal}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_normal}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoInativo}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_inativo}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoVisitante}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_visitante}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoTrancado}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_trancado}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoDesistente}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_desitente}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoVencido}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_vencido}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoAVencer}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_avencer}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoCancelado}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_cancelado}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoAtestado}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_atestado}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoCarencia}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_ferias}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" rendered="#{ListasERelatoriosControle.configuracaoSistema.usaPlanoRecorrenteCompartilhado}" value="#{ListasERelatoriosControle.situacaoDependente}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText  styleClass="text" style="margin-right: 3px" rendered="#{ListasERelatoriosControle.configuracaoSistema.usaPlanoRecorrenteCompartilhado}" value="#{msg_aplic.prt_Relatorio_situacao_dependente}" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoGympass}">
                                                        <a4j:support event="onclick" action="#{ListasERelatoriosControle.apresentarAbaSituacao}" reRender="painelSituacao"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText styleClass="text" style="margin-right: 3px" value="#{msg_aplic.prt_Relatorio_situacao_gympas}" />
                                                </fieldset>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid columns="8" style="margin-top:6px;">
                                            <h:panelGroup>
                                                <fieldset style="height: 100%;width: 100%;vertical-align: top;">
                                                    <legend style="font-size:9pt;">Tipo de Plano: </legend>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.tipoPlanoBolsa}"/>
                                                    <h:outputText  styleClass="text" title="Se estiver marcado ira trazer somente planos do tipo bolsa" value="Bolsa" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.tipoPlanoCredito}"/>
                                                    <h:outputText  styleClass="text" title="Se estiver marcado ira trazer somente planos do tipo credito" value="Crédito de Treino" />
                                                </fieldset>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid columns="10" style="margin-top:6px;">
                                            <h:panelGroup>
                                                <fieldset style="height: 100%;width: 100%;vertical-align: top;" >
                                                    <legend style="font-size:9pt;">Origem do Contrato: </legend>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemZW}" id="origemZW"/>
                                                    <h:outputText  styleClass="text" value="Pacto ADM" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemAulaCheia}" id="origemAulaCheia" rendered="false"/>
                                                    <h:outputText  styleClass="text" value="Aula Cheia"  rendered="false"/>
                                                    <rich:spacer width="5px" rendered="false"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemTreino}" id="origemTreino" rendered="false"/>
                                                    <h:outputText  styleClass="text" value="Pacto Treino" rendered="false" />
                                                    <rich:spacer width="5px" rendered="false"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemAppTreino}" id="origemAppTreino"/>
                                                    <h:outputText  styleClass="text" value="App Treino" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemAppProfessor}" id="origemAppProfessor" rendered="false"/>
                                                    <h:outputText  styleClass="text" value="App Professor"  rendered="false"/>
                                                    <rich:spacer width="5px" rendered="false"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemAutoAtendimento}" id="origemAutoAtendimento"/>
                                                    <h:outputText  styleClass="text" value="Autoatendimento" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemBuzzLead}" id="origemBuzzLead" rendered="false"/>
                                                    <h:outputText  styleClass="text" value="Buzz Lead"  rendered="false"/>
                                                    <rich:spacer width="5px" rendered="false"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.origemVendasOnline}" id="origemVendasOnline"/>
                                                    <h:outputText  styleClass="text" value="Vendas Online 2.0" />
                                                    <rich:spacer width="5px"/>
                                                </fieldset>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid columns="10" style="margin-top:6px;" id="painelSituacao">
                                            <h:panelGroup rendered="#{ListasERelatoriosControle.mostrarSituacao}">
                                                <fieldset style="height: 100%;width: 100%;vertical-align: top;">
                                                    <legend style="font-size:9pt;">Situação financeira: </legend>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoAdimplentes}" id="situacaoAdimplentes"/>
                                                    <h:outputText  styleClass="text" value="Adimplentes" />
                                                    <rich:spacer width="5px"/>
                                                    <h:selectBooleanCheckbox styleClass="text" style="margin-right: 3px" value="#{ListasERelatoriosControle.situacaoInadimplentes}" id="situacaoInadimplentes"/>
                                                    <h:outputText  styleClass="text" value="Inadimplentes" />
                                                    <rich:spacer width="5px"/>
                                                </fieldset>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </rich:simpleTogglePanel>
                                    <%-------------------------------ABA CONSULTORES ---------------------------------%>
                                    <rich:simpleTogglePanel switchType="client" label="Consultores" opened="false" onexpand="true">
                                        <h:panelGroup id="co">
                                            <rich:dataGrid value="#{ListasERelatoriosControle.consultores}"
                                                           var="consultor" width="100%" columns="3">
                                                <rich:column>
                                                    <h:selectBooleanCheckbox value="#{consultor.colaboradorEscolhido}"/>
                                                    <h:outputText value="#{consultor.pessoa.nome}">

                                                    </h:outputText>
                                                </rich:column>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-------------------------------ABA PROFESSORES ---------------------------------%>
                                    <rich:simpleTogglePanel switchType="client" label="Professores" opened="false" onexpand="true">
                                        <h:panelGroup id="pr">
                                            <rich:dataGrid value="#{ListasERelatoriosControle.professores}"
                                                           var="professor" width="100%" columns="4">
                                                <rich:column >
                                                    <h:selectBooleanCheckbox value="#{professor.colaboradorEscolhido}"/>
                                                    <h:outputText value="#{professor.pessoa.nome} ">

                                                    </h:outputText>
                                                </rich:column>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-------------------------------ABA PROFESSORES(TREINO WEB) ---------------------------------%>
                                    <rich:simpleTogglePanel switchType="client" label="Professores(TreinoWeb)" opened="false" onexpand="true">
                                        <h:panelGroup id="prT">
                                            <rich:dataGrid value="#{ListasERelatoriosControle.professoresTreino}"
                                                           var="professor" width="100%" columns="4">
                                                <rich:column >
                                                    <h:selectBooleanCheckbox value="#{professor.colaboradorEscolhido}"/>
                                                    <h:outputText value="#{professor.pessoa.nome} ">

                                                    </h:outputText>
                                                </rich:column>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-------------------------------ABA MODALIDADES ---------------------------------%>
                                    <rich:simpleTogglePanel switchType="client" label="Modalidades" opened="false" onexpand="true" id="modalidadesGeralCliente">
                                        <h:panelGroup id="md">
                                            <h:panelGrid columns="2">
                                                <h:outputText  styleClass="text"  style="font-weight: bold" value="Situação Modalidades:" />
                                                <h:selectOneRadio styleClass="text"  value="#{ListasERelatoriosControle.situacaoModalidade}">
                                                    <f:selectItems value="#{ListasERelatoriosControle.situacaoModalidades}"/>
                                                    <a4j:support event="onchange" reRender="pnlListaModalidades" action="#{ListasERelatoriosControle.povoarListaModalidades}"/>
                                                </h:selectOneRadio>
                                            </h:panelGrid>
                                            <h:panelGroup layout="block" id="pnlListaModalidades">
                                            <rich:dataTable id="listaModalidades"
                                                            value="#{ListasERelatoriosControle.listaModalidades}"
                                                            var="modalidade" width="100%" columns="4"
                                                            rows="15"
                                                            reRender="pnlListaModalidades">

                                                <rich:column filterBy="#{modalidade.nome}" filterEvent="onblur">
                                                    <h:panelGroup>
                                                        <h:selectBooleanCheckbox
                                                                value="#{modalidade.modalidadeEscolhida}"
                                                                id="modalidadeCheckBox"/>
                                                        <h:outputText style="font-size:11px;color: #474747;"
                                                                      value="#{modalidade.nome} "/>

                                                    </h:panelGroup>
                                                </rich:column>

                                            </rich:dataTable>
                                            <rich:datascroller id="scListaModalidade" align="center"
                                                               style="margin-top: 10px" styleClass="scrollPureCustom"
                                                               renderIfSinglePage="false"
                                                               for="listaModalidades"
                                                               maxPages="10"/>
                                            </h:panelGroup>

                                            <div style="height: 50px; line-height: 50px;">
                                                <div style="display: inline-block">
                                                    <h:panelGrid columns="2" style="margin-top: 10px">
                                                        <h:outputText  styleClass="text"  style="font-weight: bold" value="Vezes na semana: " />
                                                    </h:panelGrid>
                                                </div>

                                                <a4j:repeat value="#{ListasERelatoriosControle.vezesSemana}" var="vz">
                                                    <div style="display: inline-block">
                                                        <h:panelGrid columns="2" style="margin-top: 10px">
                                                            <h:selectBooleanCheckbox value="#{vz.selecionado}"/>
                                                            <h:outputText style="font-size:11px;color: #474747;" value="#{vz.label} "/>
                                                        </h:panelGrid>
                                                    </div>

                                                </a4j:repeat>
                                            </div>

                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <rich:simpleTogglePanel switchType="client" label="Empresas" opened="false" onexpand="true" id="GeralEmpresas">
                                        <h:panelGroup id="emp">
                                            <h:panelGroup layout="block" style="" rendered="#{ListasERelatoriosControle.utilizaConfiguracaoSesc}">
                                                <h:selectBooleanCheckbox value="#{ListasERelatoriosControle.consultarTodasEmpresas}" />
                                                <h:outputText value="Consultar todas empresas"/>
                                            </h:panelGroup>
                                            <h:panelGroup rendered="#{ListasERelatoriosControle.permissaoConsultaTodasEmpresas}"
                                                          style="padding-left: 10px;padding-top: 15px;padding-bottom: 15px;border: 1px solid #ACBECE;"
                                                          layout="block">
                                                <h:selectOneMenu value="#{ListasERelatoriosControle.filtroEmpresa}">
                                                    <f:selectItems  value="#{ListasERelatoriosControle.listaEmpresas}" />
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <rich:spacer height="10px"/>

                                    <br/>

                                    <a4j:commandLink styleClass="botoes nvoBt"
                                                     id="consultarGeralClientes"
                                                       style="margin-left: 0px"
                                                       action="#{ListasERelatoriosControle.consultarGeralAlunos}"
                                                       reRender="groupResultados,imprimir,msgContratoConcomitante,tabPanelGeral">
                                        Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink style="margin-left:5px;margin-top: 8px; padding:5px 7px;"
                                                       id="limparFiltros"
                                                       action="#{ListasERelatoriosControle.limparFiltros}"
                                                       styleClass="botoes nvoBt btSec"
                                                       reRender="panelGeralClientes">
                                        Limpar filtros&nbsp <i class="fa-icon-eraser"></i>
                                    </a4j:commandLink>
                                    <h:panelGroup id="imprimir" >
                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                         value="Imprimir"
                                                         rendered="#{not empty ListasERelatoriosControle.resultado}"
                                                         reRender="relatorioImprimir"
                                                         action="#{ListasERelatoriosControle.prepararImpr}"
                                                         styleClass="botoes nvoBt btSec"
                                                         oncomplete="Richfaces.showModalPanel('relatorioImprimir');">
                                            <i class="fa-icon-print"></i>
                                        </a4j:commandLink>
                                        <a4j:commandLink id="exportarExcelGeral"
                                                         style="margin-left:5px;margin-top: 8px;"
                                                         title="Exportar para excel"
                                                         reRender="relatorioImprimir,exportarExcel" styleClass="linkPadrao" action="#{ListasERelatoriosControle.acaoImprimirExcel}"
                                                         rendered="#{not empty ListasERelatoriosControle.resultado}"
                                                         oncomplete="#{ListasERelatoriosControle.mensagemNotificar}#{ListasERelatoriosControle.msgAlert}">
                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <rich:spacer width="25px"/>
                                    <h:panelGroup id="msgContratoConcomitante">
                                        <h:panelGrid rendered="#{ListasERelatoriosControle.empresaVO.permiteContratosConcomintante}">
                                            <rich:panel>
                                                <f:facet name="header">
                                                    <h:outputText value="Atenção!"/>
                                                </f:facet>
                                                <h:panelGrid columns="2">
                                                    <h:graphicImage url="imagens/atencao.png" />
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="text" value="Essa empresa permite contratos concomitantes e este relatório trabalha com a situação atual dos alunos. Essa situação leva em consideração apenas um contrato. No caso de contratos concomitantes, o contrato vigente que se inicia primeiro é o que determina a situação atual do aluno. Portanto pode acontecer de não apresentar todas as modalidades do cliente devido ao contrato que está em concomitância."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>
                                            </rich:panel>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <h:panelGroup id="msgContratoConcomitanteTdEmpresas">
                                        <h:panelGrid rendered="#{ListasERelatoriosControle.contratoConcomVarEmpresas}" id="panelContratosConcomitantesTdEmpresas">
                                            <rich:panel>
                                                <f:facet name="header">
                                                    <h:outputText value="Atenção!"/>
                                                </f:facet>
                                                <h:panelGrid columns="2">
                                                    <h:graphicImage url="imagens/atencao.png" />
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="text" value="Umas das empresas permite contratos concomitantes e este relatório trabalha com a situação atual dos alunos. Essa situação leva em consideração apenas um contrato. No caso de contratos concomitantes, o contrato vigente que se inicia primeiro é o que determina a situação atual do aluno. Portanto pode acontecer de não apresentar todas as modalidades do cliente devido ao contrato que está em concomitância."/>
                                                    </h:panelGroup>
                                                </h:panelGrid>
                                            </rich:panel>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%---------------------------------TABELA DO RELATORIO---------------------------------------%>

                                    <rich:tabPanel id="tabPanelGeral" width="100%" style="margin-top: 10px;" selectedTab="#{ListasERelatoriosControle.tabSelecionada}" >
                                        <rich:tab name="tabAnalitico" switchType="client" styleClass="titulo3" label="Analítico" id="abaAnaliticoRelGeralClientes" >
                                            <h:panelGroup id="groupResultados">
                                                <rich:dataTable width="100%" value="#{ListasERelatoriosControle.resultado}" id="resultados"
                                                                var="item"
                                                                rows="30"
                                                                style="margin-top: 8px"
                                                                rendered="#{not empty ListasERelatoriosControle.resultado}">

                                                    <rich:column id="nome" sortBy="#{item.nome}">
                                                        <f:facet name="header">
                                                            <h:outputText value="Nome"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.nome}" id="nomeResultadoGeralClientes"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>

                                                    <rich:column rendered="#{ListasERelatoriosControle.permissaoConsultaTodasEmpresas and ListasERelatoriosControle.filtroEmpresa == 0}"
                                                                 id="nomeEmpresa"
                                                                 sortBy="#{item.nomeEmpresa}">
                                                        <f:facet name="header">
                                                            <h:outputText value="Empresa"/>
                                                        </f:facet>
                                                        <h:outputText value="#{item.nomeEmpresa}"/>
                                                    </rich:column>
                                                    <rich:column id="documento" sortBy="#{item.documento}">
                                                        <f:facet name="header">
                                                            <h:outputText value="DOCUMENTO"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{ListasERelatoriosControle.displayIdentificadorFront[0]} #{item.documento}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>
                                                    <rich:column id="cnpj" sortBy="#{item.cnpj}">
                                                        <f:facet name="header">
                                                            <h:outputText value="CNPJ"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.cnpj}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>
                                                    <rich:column id="rg" sortBy="#{item.rg}">
                                                        <f:facet name="header">
                                                            <h:outputText value="#{ListasERelatoriosControle.displayIdentificadorFront[1]}"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.rg}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>

                                                    <rich:column id="telefone" sortBy="#{item.telefone}">
                                                        <f:facet name="header">
                                                            <h:outputText value="Telefone"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.telefone}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>

                                                    <rich:column id="email" sortBy="#{item.email}">
                                                        <f:facet name="header">
                                                            <h:outputText value="E-mail"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.email}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>

                                                    <rich:column id="dataCadastro" sortBy="#{item.dataCadastro}">
                                                        <f:facet name="header">
                                                            <h:outputText value="Cadastro"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.dataCadastroApresentar}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>

                                                    <rich:column id="dataNascimento" sortBy="#{item.dataNascimento}">
                                                        <f:facet name="header">
                                                            <h:outputText value="Nascimento"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.dataNascimentoApresentar}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>

                                                    <rich:column id="situacaoCliente" sortBy="#{item.situacaoClienteApresentar}">
                                                        <f:facet name="header">
                                                            <h:outputText value="Situação"/>
                                                        </f:facet>
                                                        <a4j:commandLink
                                                                value="#{item.situacaoClienteApresentar}"
                                                                actionListener="#{ListasERelatoriosControle.prepareEditar}"
                                                                action="#{ListasERelatoriosControle.irParaTelaCliente}"
                                                                oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                                    </rich:column>
                                                </rich:dataTable>
                                            </h:panelGroup>

                                            <table align="right" border="0" cellspacing="0" cellpadding="0">
                                                <tr >
                                                    <td align="center" valign="middle"><h5>
                                                        <h:outputText rendered="#{not empty ListasERelatoriosControle.resultado}" value=" [Itens:#{ListasERelatoriosControle.totalItens}]"/>
                                                    </h5>
                                                </tr>
                                            </table>
                                            <rich:datascroller for="resultados"  rendered="#{not empty ListasERelatoriosControle.resultado}"  id="scrollResultados"/>
                                        </rich:tab>
                                        <rich:tab    name="tabSintetico" label="Sintético" reRender="chartdiv"   styleClass="titulo3" id="abaSinteticoRelGeralClientes">

                                            <center>
                                                <h:panelGrid columns="1">
                                                    <h:panelGroup >
                                                        <%@include file="include_grafico_bi_geralClientes.jsp"  %>

                                                    </h:panelGroup>
                                                    <h:panelGrid columns="2" style="border-style:solid;
                                                         margin-left:262px;border-color:rgb(149, 194, 179);
                                                         border-radius:5px;padding:4px;  width: 340px;">
                                                        <h:outputLabel styleClass="text" value="Total de registros"/>
                                                        <h:outputLabel styleClass="text" value="#{ListasERelatoriosControle.registrosTotais}"/>
                                                        <h:outputLabel styleClass="text" value="Total de registros sem sexo biológico"/>
                                                        <h:outputLabel styleClass="text" value="#{ListasERelatoriosControle.registrosSemSexo}"/>
                                                        <h:outputLabel styleClass="text" value="Total de registros sem data nascimento"/>
                                                        <h:outputLabel styleClass="text" value="#{ListasERelatoriosControle.registrosSemDataNasc}"/>
                                                        <h:outputLabel styleClass="text" value="Total de pessoas com mais de 120 anos"/>
                                                        <h:outputLabel styleClass="text" value="#{ListasERelatoriosControle.registrosPessoasVelhas}"/>
                                                        <h:outputLabel styleClass="text" value="Total de registros desconsiderados"/>
                                                        <h:outputLabel styleClass="text" value="#{ListasERelatoriosControle.registrosDesconsiderados}"/>
                                                        <h:outputLabel styleClass="text" value="Total de registros considerados no gráfico"/>
                                                        <h:outputLabel styleClass="text" value="#{ListasERelatoriosControle.registrosConsiderados}"/>
                                                    </h:panelGrid>
                                                </h:panelGrid>
                                            </center>
                                        </rich:tab>
                                    </rich:tabPanel>
                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
    </table>

</h:form>

<%------------------------- MODAL 'IMPRIMIR' ----------------------------------------%>
<rich:modalPanel id="relatorioImprimir" autosized="true" shadowOpacity="true" width="450" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Dados da Impressão" />
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    id="hidelinkrelatorioImprimir" styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
            <rich:componentControl for="relatorioImprimir" attachTo="hidelinkrelatorioImprimir" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form >

        <h:outputText styleClass="text" style="font-weight: bold" value="Por Favor, selecione as informações que deseja imprimir:"/><br/><br/>
        <h:selectBooleanCheckbox value="#{ListasERelatoriosControle.apresentarLinha1}"/>
        <h:outputText styleClass="text"  value="Dados Cadastrais" /><br/>
        <h:selectBooleanCheckbox value="#{ListasERelatoriosControle.apresentarLinha2}"/>
        <h:outputText styleClass="text"  value="Endereço" /><br/>
        <h:selectBooleanCheckbox value="#{ListasERelatoriosControle.apresentarLinha3}"/>
        <h:outputText styleClass="text"  value="Informações de Plano"/><br/><br/>

        <h:panelGroup layout="block" id="panelInforDadosRelatrio" style="text-align: center; padding-top: 10px; padding-bottom: 12px;">

            <a4j:commandLink id="imprimirPDF"
                             styleClass="botaoPrimario texto-size-16-real"
                             action="#{ListasERelatoriosControle.imprimirListaRelatorio}"
                             title="Exportar para o formato PDF"
                             reRender="mensagem"
                             oncomplete="#{ListasERelatoriosControle.mensagemNotificar}#{ListasERelatoriosControle.msgAlert}">
                <i class="fa-icon-print"></i> &nbsp ${msg_aplic.IMPRIMIR}
            </a4j:commandLink>

            <a4j:commandLink
                    id="btnImprimirNovo"
                    styleClass="botaoSecundario texto-size-16-real"
                    style="margin-left: 5px"
                    reRender="modalTituloRelatorio"
                    oncomplete="Richfaces.hideModalPanel('relatorioImprimir');Richfaces.showModalPanel('modalTituloRelatorio');">
                <i class="fa-icon-print"></i> &nbsp Imprimir Resumido
            </a4j:commandLink>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<%------------------------- MODAL 'CONSULTAR' ----------------------------------------%>
<rich:modalPanel id="listasRelatorios" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Relátorio Geral de Alunos" />
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="relatorios" />
            <rich:componentControl for="listasRelatorios" attachTo="relatorios" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <jsp:include page="topoReduzido.jsp"/><br/>
    <a4j:form >
        <h:panelGrid columns="2" columnClasses="colunaDireita, colunaEsquerda">

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.nome}" value="Nome: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.nome}" value="#{ListasERelatoriosControle.itemRelatorio.nome}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.sexo}" value="Sexo biológico: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.sexo}" value="#{ListasERelatoriosControle.itemRelatorio.sexo}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.logradouro}" value="Endereço: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.logradouro}" value="#{ListasERelatoriosControle.itemRelatorio.logradouro}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.numero}" value="Número: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.numero}" value="#{ListasERelatoriosControle.itemRelatorio.numero}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.bairro}" value="Bairro: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.bairro}" value="#{ListasERelatoriosControle.itemRelatorio.bairro}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.cidade}" value="Cidade: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.cidade}" value="#{ListasERelatoriosControle.itemRelatorio.cidade}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.cep}" value="Cep: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.cep}" value="#{ListasERelatoriosControle.itemRelatorio.cep}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.complemento}" value="Complemento: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.complemento}" value="#{ListasERelatoriosControle.itemRelatorio.complemento}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataMatriculaApresentar}" value="Data Matrícula: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataMatriculaApresentar}" value="#{ListasERelatoriosControle.itemRelatorio.dataMatriculaApresentar}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataInicioPlanoApresentar}" value="Inicio Plano: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataInicioPlanoApresentar}" value="#{ListasERelatoriosControle.itemRelatorio.dataInicioPlanoApresentar}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataVencimentoPlanoApresentar}" value="Vencimento Plano: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataVencimentoPlanoApresentar}" value="#{ListasERelatoriosControle.itemRelatorio.dataVencimentoPlanoApresentar}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataUltimoAcessoApresentar}" value="Último Acesso: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.dataUltimoAcessoApresentar}" value="#{ListasERelatoriosControle.itemRelatorio.dataUltimoAcessoApresentar}"/>

            <h:outputText styleClass="text" style="font-weight: bold" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.plano}" value="Plano: "/>
            <h:outputText styleClass="text" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.plano}" value="#{ListasERelatoriosControle.itemRelatorio.plano}"/>


            <h:outputText styleClass="text" style="font-weight: bold;" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.consultores}" value="Consultor: "/>
            <h:panelGroup>
                <c:forEach items="${ListasERelatoriosControle.itemRelatorio.consultores}" var="consultor">
                    <span rendered="${not empty ListasERelatoriosControle.itemRelatorio.consultores}" class="text">${consultor.pessoa.nome}</span><br/>
                </c:forEach>
            </h:panelGroup>

            <h:outputText styleClass="text" style="font-weight: bold;" rendered="#{not empty ListasERelatoriosControle.itemRelatorio.professores}" value="Professor: "/>
            <h:panelGroup>
                <c:forEach items="${ListasERelatoriosControle.itemRelatorio.professores}" var="professor">
                    <span rendered="${not empty ListasERelatoriosControle.itemRelatorio.professores}" class="text">${professor.pessoa.nome}</span><br/>
                </c:forEach>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

    <rich:modalPanel id="modalTituloRelatorio" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Imprimir"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        id="hidelinkTituloRelatorio" styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="modalTituloRelatorio" attachTo="hidelinkTituloRelatorio" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formModalTituloRelatorio">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_titulo_relatorio}:"/>

                        <h:inputText id="inputTituloRelatorio" size="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{ListasERelatoriosControle.nomeRelatorio}"/>
                        <br/>
                        <br/>
                        <h:outputText id="msgDetalhadaTituloRelatorio"
                                      style="color: red"
                                      value="#{ListasERelatoriosControle.mensagemDetalhada}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:panelGroup styleClass="margin-box">

                        <a4j:commandLink id="btnImprimirTituloRelatorio"
                                         styleClass="botaoPrimario texto-size-16-real"
                                         actionListener="#{ExportadorListaControle.exportar}"
                                         title="Exportar para o formato PDF"
                                         rendered="#{not empty ListasERelatoriosControle.resultado}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert};fireElement('hidelinkTituloRelatorio');">
                            <f:attribute name="lista" value="#{ListasERelatoriosControle.resultado}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos" value="matricula=Matricula,nome=Nome,telefone=Telefone,email=Email"/>
                            <f:attribute name="prefixo" value="ListaCliente"/>
                            <f:attribute name="itemExportacao" value="geralClientes"/>
                            <f:attribute name="titulo" value="#{ListasERelatoriosControle.nomeRelatorio}"/>
                            <i class="fa-icon-print"></i> &nbsp ${msg_aplic.IMPRIMIR}
                        </a4j:commandLink>

                        <a4j:commandLink
                                id="btnFecharTituloRelatorio"
                                style="margin-left: 12px"
                                status="false"
                                onclick="fireElement('hidelinkTituloRelatorio');"
                                value="#{msg_aplic.FECHAR}"
                                styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>
</f:view>
