<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<!-- inicio box -->
<div class="box">
    <div class="boxtop"><img src="./images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top">
                    <a class="titulo2">Cadastros</a>
                </td>
            </tr>
        </table>
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

            <tr>
                <td width="20" align="left" valign="top">
                    <img src="./images/shim.gif">
                </td>
                <!-- inicio item-->
                <td align="left" valign="top">

                    <div>
                        <h:outputLink
                                value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Movimento_do_Pagamento"
                                title="Clique e saiba mais: Movimento de Pagamento" target="_blank"
                                rendered="#{LoginControle.permissaoAcessoMenuVO.movPagamento}">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="1px"/>
                        <h:outputLink styleClass="titulo3"
                                      onclick="abrirPopup('movPagamentoCons.jsp', 'MovPagamento', 1000, 650);"
                                      value="#">
                            <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.movPagamento}"
                                          value="#{msg_menu.Menu_movPagamento}"/>
                        </h:outputLink>
                    </div>

                    <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.movPagamento}">
                        <div class="sepmenu"><img src="./images/shim.gif"></div>
                    </h:panelGroup>
                    <!-- fim item-->
                    <!-- inicio item mov parcela-->
                    <div>
                        <h:outputLink value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Movimento_da_Parcela"
                                      title="Clique e saiba mais: Movimento da Parcela" target="_blank"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.movParcela}">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="1px"/>
                        <h:outputLink styleClass="titulo3"
                                      onclick="abrirPopup('movParcelaCons.jsp', 'MovParcela', 1000, 650);"
                                      value="#">
                            <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.movParcela}"
                                          value="#{msg_menu.Menu_movParcela}"/>
                        </h:outputLink>
                    </div>

                    <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.movParcela}">
                        <div class="sepmenu">
                            <img src="./images/shim.gif">
                        </div>
                    </h:panelGroup>

                </td>
            </tr>
        </table>
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top">
                    <a class="titulo2">Relat�rios</a>
                </td>
            </tr>
        </table>
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

            <tr>
                <td width="20" align="left" valign="top">
                    <img src="./images/shim.gif">
                </td>
                <!-- inicio item-->
                <td align="left" valign="top">

                    <div>
                        <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.receitaPorPeriodoSinteticoRel}">
                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-verificar-a-receita-por-periodo-da-empresa/"
                                          title="Clique e saiba mais: Receita por Per�odo" target="_blank">
                                <img alt="" class="linkWiki" src="./imagens/wiki_link2.gif"/>
                            </h:outputLink>
                            <rich:spacer width="2px"/>
                            <a4j:commandLink styleClass="titulo3"
                                             oncomplete="abrirPopup('relatorio/receitaPorPeriodoSinteticoRel.jsp', 'ReceitaPorPeriodoSintetico', 780, 595);"
                                             action="#{ReceitaPorPeriodoSinteticoRelControleRel.novo}">
                                <h:outputText value="#{msg_menu.Menu_receitaPorPeriodoSintetico}"/>
                            </a4j:commandLink>
                            <div class="sepmenu"><img alt="" src="./images/shim.gif"></div>
                        </h:panelGroup>
                    </div>


                </td>
            </tr>
        </table>
    </div>
    <div class="boxbottom">
        <img src="images/box_bottom.png">
    </div>
</div>
<!-- fim box -->