<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>

<script type="text/javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<script type="text/javascript" language="javascript" src="script/chart_amcharts.js"></script>
<script src="script/chart_pie.js" type="text/javascript"></script>
<script src="script/chart_light.js" type="text/javascript"></script>
<script src="script/chart_serial.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<style type="text/css">
    .painelMetadeTelaBoxCanalPacto{
        min-width: 330px;
        width: 47%;
        height: 45vh;
        background-color: white;
        -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
        -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
        display: inline-table;
        margin: 1.5% 0 0 1.5%;
        position: relative;
    }

    .tituloBoxCanalPacto{
        width: 100%;
        border-bottom: #E5E5E5 1px solid;
        display: table;
        line-height: 50px;
        font-family: Arial;
        text-decoration: none;
        font-weight: bold;
        font-size: 1.1em;
        color: #333333;
        margin-left: 10px;
    }
    .rowCanalPacto{
        cursor: help;
    }

    .labelAtendimento{
        font-size: 1.1em!important;
    }



</style>


<%
            pageContext.setAttribute("modulo", "zillyonWeb");
%>

<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Solicitações em aberto"/>
    </title>
    <h:form id="form" >
        <input type="hidden" value="${modulo}" name="modulo"/>
        <html>
            <jsp:include page="include_head.jsp" flush="true" />

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">

                            <div id="divAtendimentos" class="painelMetadeTelaBoxCanalPacto" style="width: 96%; height: 21vh; margin-top: 1.5em!important;">
                                <jsp:include page="include_minhacontapacto_atendimento.jsp" flush="true" />
                            </div>

                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral_atendimentoPacto.jsp" flush="true" />
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp"/>
</f:view>

