<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }

    .to-uper-case{
        text-transform: uppercase;
    }
</style>
<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-um-trancamento-para-um-aluno/"/>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_trancamentoContrato_tituloForm}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panel" styleClass="novaModal" width="350" height="120" autosized="true" shadowOpacity="true"
                     showWhenRendered="#{TrancamentoContratoControle.trancamentoContratoVO.mensagemErro}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkMensagemGenerica" rendered="#{MensagemGenericaControle.mostrarBotaoFechar}"/>
                <rich:componentControl for="panel" attachTo="hidelinkMensagemGenerica" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup>
        <h:outputText id="msgTrancamentoDet1" styleClass="mensagemDetalhada"
                value="#{TrancamentoContratoControle.mensagemDetalhada}"/>
                </h:panelGroup>
        <a4j:form prependId="true" id="formMdlMensagemGenerica">
        <h:panelGroup>
                        <h:outputText id="msgDetalhada"
                          value="#{MensagemGenericaControle.mensagemDetalhada}"/>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup rendered="#{MensagemGenericaControle.labelBotaoFecharTela == null}"
                              styleClass="margin-box">

                    <rich:spacer width="30px;"/>

                </h:panelGroup>
                <a4j:commandLink value="#{MensagemGenericaControle.labelBotaoFecharTela}"
                                 id="btnFecharModalGenerico"
                                 rendered="#{MensagemGenericaControle.labelBotaoFecharTela != null}"
                                 styleClass="botaoPrimario texto-size-16-real"
                                 oncomplete="#{MensagemGenericaControle.onCompleteBotaoFechar};Richfaces.hideModalPanel('mdlMensagemGenerica');"
                                 reRender="#{TrancamentoContratoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <h:panelGroup>

            </h:panelGroup>
        </h:panelGroup>
        </a4j:form>


    </rich:modalPanel>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form" styleClass="overflow-visible">
        <h:panelGrid columns="2" width="100%" styleClass="panelTrancamentoContrato">
            <h:panelGrid columns="1" cellpadding="0" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:outputText value="NOME DO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"   />
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.contratoVO.pessoa.nome}"/>
                <rich:spacer height="10px;"/>
                <h:outputText value="#{msg_aplic.prt_trancamentoContrato_dataDeTrancamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"/>
                <rich:spacer height="5px;"/>
                <h:panelGroup id="pnlDtTrancamento" styleClass="dateTimeCustom" >
                    <rich:calendar id="dataTrancamento"
                                   value="#{TrancamentoContratoControle.trancamentoContratoVO.dataTrancamento}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   onchanged="validar_Data(this.id);validarDataRetroativa(#{rich:component('dataTrancamento')}.getCurrentDate());"
                                   oninputchange="validar_Data(this.id);validarDataRetroativa(#{rich:component('dataTrancamento')}.getCurrentDate());"
                                   datePattern="dd/MM/yyyy"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                </h:panelGroup>
                <h:message for="dataTrancamento" styleClass="mensagemDetalhada"/>
                <a4j:jsFunction name="validarDataRetroativa"
                                action="#{TrancamentoContratoControle.validarDataTrancamento}"
                                reRender="panelAutorizacaoFuncionalidade,pnlDtTrancamento"/>
                <rich:spacer height="30px;"/>
                <div class="texto-size-14-real texto-cor-cinza-2 texto-font" >
                    <%-- O <b>TRANCAMENTO</b> é uma operação que é realizada caso o cliente deseje interromper
                     seu acesso por um tempo determinado alterando seu período contratual.<br>
                     No caso de suspensão contratual por um período é cobrado uma<br> <u>taxa referente ao trancamento</u>
                     do contrato.--%>
                    O <b>Trancamento</b> é uma operação que é realizada caso o cliente deseje interromper seu acesso
                    por um tempo determinado alterando seu período contratual. Neste caso a suspensão contratual
                    é realizada na data exata do trancamento, mas a data base para cálculo do crédito que ficará
                    para o aluno pode ser com base em uma data retroativa.
                </div>
                <br>
                <br>
                <h:panelGroup style="width: 100%">
                    <h:commandLink id="proximo"
                                   style="float: right;"
                                   action="#{TrancamentoContratoControle.calculoTrancamentoCliente}"
                                   styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-arrow-right" ></i>
                    </h:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:dataTrancamento").focus();
</script>
