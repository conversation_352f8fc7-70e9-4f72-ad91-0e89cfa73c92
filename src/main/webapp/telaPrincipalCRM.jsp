<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">

<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>

<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:form id="form">
        <html>
        <jsp:include page="include_headCRM.jsp" flush="true"/>
        <head>
            <style type="text/css">
                .w25 {
                    padding: 0 !important;
                }

                .w50 {
                    padding: 0 !important;
                }

                .container-meta-diaria {
                    /* width: 100%; */
                    margin: 15px;
                    padding: 10px;
                    height: auto;
                    position: relative;
                    -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
                    -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
                    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
                    background-color: #ffffff;
                    transition: transform .1s ease-out;
                    -moz-transition: -moz-transform .1s ease-out;
                    -o-transition: -o-transform .1s ease-out;
                    -webkit-transition: -webkit-transform .1s ease-out;
                }

                .rich-menu-list-bg{
                    overflow-y:auto;
                    max-height: 500px;
                }
            </style>
        </head>


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_crm_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <c:if test="${SuperControle.menuZwUi}">
                <jsp:include page="include_box_menulateral.jsp">
                    <jsp:param name="menu" value="CRM-INICIO" />
                </jsp:include>

            </c:if>
            <c:if test="${not SuperControle.menuZwUi}">
                <style>
                    .container-imagem{
                        float: none !important;
                        width: 100% !important;
                    }
                </style>
            </c:if>
            <div class="container-imagem">
                <h:panelGroup layout="block" styleClass="container-meta-diaria container-box zw_ui especial" id="telaPrincipalCRM"
                              style="width: 96%; height: auto; margin-left: auto; margin-right: auto; display: block;">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned" style="padding: 10px">

                        <h:panelGrid id="superiorPrincipalCRM" columns="2" width="100%">

                            <h:panelGrid columns="10">

                                <rich:calendar id="dataInicialCRM"
                                               buttonClass="margin-left-right-7"
                                               showWeeksBar="false"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               inputClass="forcarSemBorda"
                                               styleClass="dateTimeCustom"
                                               oninputchange="return validar_Data(this.id);"
                                               disabled="#{!LoginControle.permissaoAcessoMenuVO.pesquisarPeriodoCRM && !LoginControle.permissaoAcessoMenuVO.pesquisarMetaPassadaCRM}"
                                               value="#{MetaCRMControle.dataInicio}"
                                               inputSize="8"
                                               datePattern="dd/MM/yyyy">
                                    <a4j:support event="onchanged"
                                                 rendered="#{!LoginControle.permissaoAcessoMenuVO.pesquisarPeriodoCRM}"
                                                 action="#{MetaCRMControle.consultarMetasUsuariosSelecionadosBtnFront}"
                                                 reRender="colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita, mdlMensagemGenerica"
                                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"/>
                                </rich:calendar>
                                <rich:jQuery
                                        query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                        selector=".btn-toggle-calendar"/>

                                <rich:calendar id="dataFinalCRM"
                                               buttonClass="margin-left-right-7"
                                               showWeeksBar="false"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               inputClass="forcarSemBorda"
                                               styleClass="dateTimeCustom"
                                               oninputchange="return validar_Data(this.id);"
                                               rendered="#{LoginControle.permissaoAcessoMenuVO.pesquisarPeriodoCRM}"
                                               value="#{MetaCRMControle.dataFim}"
                                               inputSize="8"
                                               datePattern="dd/MM/yyyy"/>
                                <rich:jQuery
                                        query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                        selector=".btn-toggle-calendar"/>


                                <h:inputHidden id="totalUsuariosSelecionados"
                                               value="#{MetaCRMControle.totalUsuariosSelecionados}"/>

                                <h:panelGroup layout="block" id="panellistaGrupoColaboradorHabili" rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">

                                    <rich:dropDownMenu id="listaGrupoColaboradorHabi"
                                                       styleClass="listaConsultorCRM pure-button pure-button-secundary"
                                                       selectItemClass="itemSelecionado">

                                        <f:facet name="label">
                                            <h:panelGroup layout="block" styleClass="pure-button">
                                                <h:outputText id="totalUsuarioSelecionadoHabi"
                                                              value="Total selecionado: 0"/>
                                                <rich:spacer width="2%"/>
                                                <i class="fa-icon-caret-down"></i>
                                            </h:panelGroup>
                                        </f:facet>

                                        <c:forEach var="grupos"
                                                   items="#{MetaCRMControle.listaGrupoTipoColaborador}">

                                            <rich:menuGroup id="grupoParticipanteTipoColab" value="#{grupos.tipoColaborador_Apresentar}" style="text-align: left;text-transform: uppercase" styleClass="grupoPai">

                                                <%--SELECIONAR TODOS DO GRUPO PARTICIPANTE--%>
                                                <rich:menuItem style="border-bottom: 1px #000 solid; text-align: left; display: block;"
                                                               styleClass="gruposHabi grupoPaiHabi#{grupos.codigoTipoColaboradorOrdinal}"
                                                               submitMode="ajax"
                                                               id="testeGrupo1"
                                                               status="false"
                                                               onclick="selecionarTodos(#{grupos.codigoTipoColaboradorOrdinal});consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})">

                                                    <h:selectBooleanCheckbox
                                                            styleClass="selTodos#{grupos.codigoTipoColaboradorOrdinal}"
                                                            id="selTodosBIHabi${grupos.codigoTipoColaboradorOrdinal}"
                                                            onclick="selecionarTodosCheck(#{grupos.codigoTipoColaboradorOrdinal});consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})"
                                                            value="#{grupos.todosUsuariosSelecionados}">
                                                        <a4j:support status="false" event="onchange"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText style="padding-left: 2%"
                                                                  id="selecionarTodosHabi${grupos.codigoTipoColaboradorOrdinal}"
                                                                  value=""/>
                                                </rich:menuItem>

                                                <c:forEach var="usuario"
                                                           items="#{grupos.usuarios}">

                                                    <rich:menuItem submitMode="ajax"
                                                                   styleClass="grupoFilho"
                                                                   style="text-align: left;"
                                                                   status="false"
                                                                   onclick="selecionarColaborador(#{grupos.codigoTipoColaboradorOrdinal}#{usuario.codigo});consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})">

                                                        <h:selectBooleanCheckbox
                                                                styleClass="todosItens grupo#{grupos.codigoTipoColaboradorOrdinal} colabo#{grupos.codigoTipoColaboradorOrdinal}#{usuario.codigo}"
                                                                onclick="consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})"
                                                                value="#{usuario.selecionado}">
                                                            <a4j:support status="false" event="onchange"
                                                                         reRender="selecionarTodos"/>
                                                        </h:selectBooleanCheckbox>

                                                        <h:outputText style="padding-left: 2%"
                                                                      value="#{usuario.nome}"/>
                                                    </rich:menuItem>
                                                </c:forEach>
                                            </rich:menuGroup>
                                        </c:forEach>
                                    </rich:dropDownMenu>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panellistaGrupoColaboradorDesabi"
                                              rendered="#{!MetaCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">
                                    <rich:dropDownMenu id="listaGrupoColaborador"
                                                       styleClass="listaConsultorCRM pure-button pure-button-secundary"
                                                       selectItemClass="itemSelecionado">

                                        <f:facet name="label">
                                            <h:panelGroup layout="block" styleClass="pure-button">
                                                <h:outputText id="totalUsuarioSelecionado"
                                                              value="Total selecionado: 0"/>
                                                <rich:spacer width="2%"/>
                                                <i class="fa-icon-caret-down"></i>
                                            </h:panelGroup>
                                        </f:facet>

                                        <c:forEach var="grupos" items="#{MetaCRMControle.listaGrupoColaborador}"
                                                   varStatus="loop">
                                            <rich:menuGroup id="grupoParticipante" value="#{grupos.descricao}"
                                                            style="text-align: left" styleClass="grupoPai">

                                                <%-- PRIMEIRA LINHA DO SUBMENU � FIXA (SELECIONAR TODOS) --%>
                                                <rich:menuItem
                                                        style="border-bottom: 1px #000 solid; text-align: left; display: block; min-width: 400px; background-color: #d3daf1"
                                                        styleClass="grupos grupoPai#{grupos.codigo}"
                                                        submitMode="ajax"
                                                        status="false">
                                                    <div onclick="event.preventDefault(); event.stopPropagation(); selecionarTodos(${grupos.codigo}); consultarTotalSelecionado(); consultarTotalGrupo(${grupos.codigo});">
                                                            <%-- CHECKBOX --%>
                                                        <h:selectBooleanCheckbox styleClass="selTodos#{grupos.codigo}"
                                                                                 style="margin-left: 4px;"
                                                                                 id="selTodosBIDesa${grupos.codigo}"
                                                                                 onclick="selecionarTodosCheck(#{grupos.codigo}); consultarTotalSelecionado(); consultarTotalGrupo(#{grupos.codigo})"
                                                                                 value="#{grupos.todosParticipantesSelecionados}">
                                                            <a4j:support status="false" event="onchange"
                                                                         reRender="selecionarTodos"/>
                                                        </h:selectBooleanCheckbox>
                                                            <%-- NOME COLABORADOR --%>
                                                        <label for="selTodosBIDesa${grupos.codigo}"
                                                               style="width: 100%;">
                                                            <h:outputText styleClass="negrito" style="padding-left: 1%;"
                                                                          id="selecionarTodos${grupos.codigo}"
                                                                          value=""/>
                                                        </label>
                                                    </div>
                                                </rich:menuItem>

                                                <%-- DEMAIS LINHAS DO SUBMENU --%>
                                                <c:forEach var="grupoColaboradorParticipante"
                                                           items="#{grupos.grupoColaboradorParticipanteVOs}">
                                                    <c:set var="checkboxId"
                                                           value="selParticipante${grupoColaboradorParticipante.colaboradorParticipante.codigo}"/>
                                                    <rich:menuItem submitMode="none"
                                                                   id="itemMenu"
                                                                   style="text-align: left;"
                                                                   status="false"
                                                                   onclick="selecionarColaborador(#{grupoColaboradorParticipante.colaboradorParticipante.codigo}, event); consultarTotalGrupo(#{grupos.codigo}); this.blur();">
                                                        <%-- CHECKBOX --%>
                                                        <h:selectBooleanCheckbox
                                                                id="selParticipante${grupoColaboradorParticipante.colaboradorParticipante.codigo}"
                                                                styleClass="todosItens grupo#{grupos.codigo} colabo#{grupoColaboradorParticipante.colaboradorParticipante.codigo}"
                                                                style="margin-left: 4px;"
                                                                onclick="consultarTotalSelecionado(); consultarTotalGrupo(#{grupos.codigo})"
                                                                value="#{grupoColaboradorParticipante.grupoColaboradorParticipanteEscolhido}">
                                                            <a4j:support status="false" event="onchange"
                                                                         reRender="selecionarTodos"/>
                                                        </h:selectBooleanCheckbox>
                                                        <%-- NOME COLABORADOR --%>
                                                        <label for="${checkboxId}"
                                                               style="width: 100%; padding-left: 1%;"
                                                               onclick="event.preventDefault(); event.stopPropagation(); selecionarColaborador('${grupoColaboradorParticipante.colaboradorParticipante.codigo}'); consultarTotalSelecionado(); consultarTotalGrupo(${grupos.codigo});">
                                                            <h:outputText
                                                                    value="#{grupoColaboradorParticipante.colaboradorParticipante.pessoa_Apresentar}"/>
                                                        </label>
                                                    </rich:menuItem>
                                                </c:forEach>
                                            </rich:menuGroup>
                                        </c:forEach>
                                    </rich:dropDownMenu>
                                </h:panelGroup>


                                <rich:spacer width="3"/>

                                <a4j:commandLink id="consultarMetasCRM"
                                                 styleClass="pure-button pure-button-primary"
                                                 action="#{MetaCRMControle.consultarMetasUsuariosSelecionadosBtnFront}"
                                                 reRender="panelStatusMeta, statusMeta, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita, mdlMensagemMetaDiaria, mdlMensagemGenerica"
                                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                                 onclick="consultarTotalSelecionado()"
                                                 title="Buscar" accesskey="2">
                                    <i class="fa-icon-search"></i> Buscar
                                </a4j:commandLink>

                                <a4j:commandLink style="padding: 0; float: right; margin-left: 10px"
                                                 action="#{MetaCRMControle.abrirWpp}"
                                                 reRender="mdlWhatsapp" rendered="false"
                                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica};Richfaces.showModalPanel('mdlWhatsapp')">
                                    <img src="imagens\wpp.png" width="40px"/>
                                </a4j:commandLink>

                                <a4j:commandLink  id="abrirWpp" style="padding: 0; float: right; margin-left: 10px"
                                                  title="Clique aqui para abrir o Whatsapp Web"
                                                  action="#{MetaCRMControle.abrirWpp}"
                                                  reRender="mdlWhatsapp" rendered="true"
                                                  onclick="abrirPopup('https://web.whatsapp.com', 'WhatsApp', 1200, 750);return false;">
                                    <img src="imagens\wpp.png" width="40px"/>
                                </a4j:commandLink>

                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}como-enviar-mensagem-por-whatsapp-atraves-da-meta-diaria-crm/"
                                              title="Clique e saiba mais: WhatsApp Web" target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                            </h:panelGrid>

                            <h:panelGrid id="panelStatusMeta" style="float: right">

                                <h:panelGroup id="statusMeta" rendered="#{MetaCRMControle.mostrarInformacoesMeta}"
                                              layout="block" style="float: right">
                                    <h:outputText
                                            style="color: #0f4c6b; font-weight: bold; font-size: 12px"
                                            value="#{MetaCRMControle.metaDetalhadoVOSelecionado.fecharMeta.aberturaMetaVO.colaboradorResponsavel.nome} - "/>

                                    <h:outputText
                                            style="color: #0f4c6b; font-size: 12px"
                                            value="Meta do Dia: #{MetaCRMControle.historicoContatoVO.diaAbertura_Apresentar}"/>
                                    <h:outputText
                                            rendered="#{MetaCRMControle.historicoContatoVO.aberturaMetaEstaEmAberta}"
                                            style="color: #0f4c6b; font-weight: bold; font-size: 12px"
                                            value=" - Aberta"/>
                                    <h:outputText
                                            rendered="#{!MetaCRMControle.historicoContatoVO.aberturaMetaEstaEmAberta}"
                                            style="color: #0f4c6b; font-weight: bold; font-size: 12px"
                                            value=" - Fechada"/>
                                </h:panelGroup>

                            </h:panelGrid>

                        </h:panelGrid>

                        <rich:spacer width="100%" height="10px"/>

                        <%--DIVIS�O DAS 3 TELAS DO CRM--%>
                        <h:panelGrid id="telasCRM" width="100%" columns="3" columnClasses="w25,w25,w50"
                                     style="height: 95%; min-height: 600px">
                            <h:panelGroup layout="block" id="colunaCRMEsquerda" styleClass="colunaCRMEsquerda"
                                          style="height: 100%; background: #e6e6e6; display: none; min-height: 600px;">
                                <jsp:include page="include_panelgrid_left_tela_principalCRM.jsp" flush="true"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="colunaCRMCentro" styleClass="colunaCRMCentro"
                                          style="height: 100%; background: white !important; border-right: 1px #8A8A8A solid; display: none;">
                                <jsp:include page="include_panelgrid_center_tela_principalCRM.jsp" flush="true"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="colunaCRMDireita" styleClass="colunaCRMDireita"
                                          style="height: 100%; padding-left: 7px; background: white !important; display: none; min-height: 600px;">

                                <h:panelGroup rendered="#{not MetaCRMControle.abriuMetaHoje}">
                                    <jsp:include page="include_crm_nao_abriu_meta.jsp" flush="true"/>

                                    <a4j:jsFunction status="none" reRender="telasCRM"
                                                    name="processarMetasNaoAbertas"
                                                    oncomplete="#{MetaCRMControle.msgAlert}"
                                                    action="#{MetaCRMControle.processarMetas}"/>

                                    <script type="text/javascript">
                                        window.onload = function() {
                                            processarMetasNaoAbertas();
                                        }
                                    </script>
                                </h:panelGroup>

                                <jsp:include page="include_panelgrid_right_tela_principalCRM.jsp" flush="true"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGroup>
            </div>

        </h:panelGroup>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>


        <rich:modalPanel id="mdlWhatsapp" styleClass="novaModal" width="900" autosized="true"
                         shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="WhatsApp"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hidelinkmdlWhatsapp"/>
                    <rich:componentControl for="mdlWhatsapp" attachTo="hidelinkmdlWhatsapp"
                                           operation="hide"
                                           event="onclick"/>
                </h:panelGroup>
            </f:facet>

            <iframe src="${MetaCRMControle.urlWpp}" width="100%"
                    height="600px" onerror="alert('Failed')">
            </iframe>

            <div style="text-align: center; margin-top: 10px;">
                <a href="https://chrome.google.com/webstore/detail/ignore-x-frame-headers/gleekbfjekiniecknbkamfmkohkpodhe/related"
                   target="_blank">
                    Problemas para acessar o WhatsApp? Clique e instale o plugin para o Chrome.
                </a>
            </div>


        </rich:modalPanel>

        <c:if test="${SuperControle.menuZwUi}">
            <script>
                fecharMenu();
            </script>
        </c:if>

        </html>
    </h:form>


    <jsp:include page="include_modais_tela_principalCRM.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_metaDiaria.jsp" flush="true"/>
    <jsp:include page="include_menu_modalQuarentenaCRM.jsp" flush="true"/>

    <rich:modalPanel id="mdlConfirmarComparecimentoAulaExp" styleClass="novaModal" width="450" autosized="true"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmar comparecimento aula experimental"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkConfAulaExp"/>
                <rich:componentControl for="mdlConfirmarComparecimentoAulaExp" attachTo="hidelinkConfAulaExp"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

    </rich:modalPanel>

    <rich:modalPanel id="mdlEditarSimplesRegistro" width="600" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                Edi��o de Registro
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmdlEditarSimplesRegistro"/>
                <rich:componentControl for="mdlEditarSimplesRegistro" attachTo="hidelinkmdlEditarSimplesRegistro" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formEditarSimplesRegistro">
            <h:panelGroup>
                <h:inputTextarea styleClass="inputTextClean" value="#{MetaCRMControle.historicoContatoVOEdicao.observacao}"
                                 style="width: calc(100% - 1vw); margin-top:0.5vw; margin-left:0.5vw; height: 12vw;"/>
                <a4j:commandLink styleClass="botoes nvoBt" style="float: right; margin: 5px 0.5vw;"
                                 id="confirmarEdicaosimplesRegistro"
                                 action="#{MetaCRMControle.salvarSimplesRegistro}"
                                 oncomplete="#{MetaCRMControle.mensagemNotificar};Richfaces.hideModalPanel('mdlEditarSimplesRegistro');"
                                 reRender="form:panelGridRight, form:panelGridCenter">
                    Salvar
                </a4j:commandLink>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

</f:view>

<style>
    .grupoFilho {
        width: 450px;
    }

    .grupoFilho:hover,
    .grupoFilho:focus,
    .grupoFilho:active {
        width: 450px;
    }
</style>

<script type="text/javascript">

    document.querySelectorAll('.grupoFilho').forEach(function(item) {
        item.addEventListener('click', function(event) {
            event.preventDefault(); // Impede o comportamento padr�o
            this.blur(); // Remove o foco do item para evitar aumento de fonte
        });
    });

    document.querySelectorAll('.grupoFilho label').forEach(function(label) {
        label.addEventListener('mouseover', function() {
            // Remove qualquer altera��o de estilo ao passar o mouse
            label.style.width = '450px'; // Garante que o tamanho da fonte permane�a fixo
        });
    });

    function mostrarTodasTelas() {
        jQuery('.colunaCRMEsquerda').fadeIn(400, function () {
            jQuery('.colunaCRMCentro').fadeIn(400, function () {
                jQuery('.colunaCRMDireita').fadeIn(600);
            });
        });
    }

    function mostrarTelaCentroEDireita() {
        try{
            jQuery('.colunaCRMCentro').fadeIn(400, function () {
                jQuery('.colunaCRMDireita').fadeIn(600);
            });
        }catch (e) {
            console.log("ERRO mostrarTelaCentroEDireita: "+e);
        }
    }

    function mostrarTelaCentro() {
        jQuery('.colunaCRMCentro').fadeIn(400);
    }

    function mostrarTelaDireita() {
        jQuery('.colunaCRMDireita').fadeIn(600);
    }

    function bloquearEnter() {
        var tecla = window.event.keyCode;
        if (tecla == 13) {
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

    function tabenter(campo) {
        var tecla = window.event.keyCode;
        if (tecla == 13) {
            document.getElementById(campo).focus();
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

    function buscarMetas() {
        document.getElementById("form:consultarMetasCRM").click();
    }

    function selecionarTodosMeta(codigo) {
        var itens = jQuery('.grupo' + codigo);
        var elemento = jQuery('.selTodos' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            elemento.attr("checked", false);
            itens.attr("checked", false);
        } else {
            elemento.attr("checked", true);
            itens.attr("checked", true);
        }
    }

    function selecionarTodosCheckMeta(codigo) {
        var itens = jQuery('.grupo' + codigo);
        var elemento = jQuery('.selTodos' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            itens.attr("checked", true);
        } else {
            itens.attr("checked", false);
        }
    }

    function selecionarColaboradorMeta(codigo) {
        var elemento = jQuery('.colabo' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            elemento.attr("checked", false);
        } else {
            elemento.attr("checked", true);
        }
    }

    function consultarTotalSelecionadoMeta() {
        var itens = jQuery('.todosItens');

        var i = 0;
        var totalSelecionado = 0;
        for (i = 0; i < itens.length; i++) {
            var marcado = itens[i].checked;
            if (marcado) {
                totalSelecionado++;
            }
        }

        var inputTotal = document.getElementById('form:totalUsuariosSelecionados');
        inputTotal.value = totalSelecionado;
    }


    function selecionarTodos(codigo) {
        var itens = jQuery('.grupo' + codigo);
        var elemento = jQuery('.selTodos' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            elemento.attr("checked", false);
            itens.attr("checked", false);
        } else {
            elemento.attr("checked", true);
            itens.attr("checked", true);
        }
    }

    function selecionarTodosCheck(codigo) {
        var itens = jQuery('.grupo' + codigo);
        var elemento = jQuery('.selTodos' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            itens.attr("checked", true);
        } else {
            itens.attr("checked", false);
        }
    }

    function selecionarColaborador(codigo) {
        // Impede o comportamento padr�o do evento
        event.preventDefault();

        // Impede a propaga��o do evento
        event.stopPropagation();
        event.stopImmediatePropagation(); // Impede outros manipuladores de eventos

        // Monta o ID do checkbox
        var checkboxId = 'form:selParticipante' + codigo;
        var elemento = document.getElementById(checkboxId);

        if (!elemento) {
            console.error("Checkbox n�o encontrado para c�digo:", codigo);
            return; // Sai da fun��o se o elemento n�o foi encontrado
        }

        // Alterna o estado do checkbox
        elemento.checked = !elemento.checked;

        // Dispara o evento de mudan�a
        var changeEvent = new Event('change');
        elemento.dispatchEvent(changeEvent);

        console.log("Checkbox ap�s a altera��o: ", elemento.checked);
    }
    
    function consultarTotalSelecionado() {
        try {
            var itens = jQuery('.todosItens');

            var totalSelecionado = 0;
            for (var i = 0; i < itens.length; i++) {
                if (itens[i].checked) {
                    totalSelecionado++;
                }
            }

            if (jQuery(document.getElementById('form:totalUsuarioSelecionado')).is(':visible')) {
                document.getElementById('form:totalUsuarioSelecionado').innerHTML = "Total selecionado: " + totalSelecionado;
                consultarTotalSelecionadoPorGrupo();
            } else {
                document.getElementById('form:totalUsuarioSelecionadoHabi').innerHTML = "Total selecionado: " + totalSelecionado;
            }

            if (jQuery(document.getElementById('form:totalUsuarioSelecionadoHabi')).is(':visible')) {
                consultarTotalSelecionadoPorGrupoHabi();
            }
        }catch (e) {
            console.log("ERRO consultarTotalSelecionado: "+e);
        }
    }

    function consultarTotalSelecionadoPorGrupo() {
        try {
            var itens = jQuery('.grupos');

            for (var i = 0; i < itens.length; i++) {
                consultarTotalGrupo(itens[i].classList[3].substring(8));
            }
        }catch (e) {
            console.log("ERRO consultarTotalSelecionadoPorGrupo: "+e);
        }
    }
    
    function consultarTotalSelecionadoPorGrupoHabi() {
        try {
            var itens = jQuery('.gruposHabi');

            var i = 0;
            for (i = 0; i < itens.length; i++) {
                var valor = itens[i].classList[3];
                var grupo = valor.substring(12);
                consultarTotalGrupoHabi(grupo);
            }
        }catch (e) {
            console.log("ERRO consultarTotalSelecionadoPorGrupoHabi: "+e);
        }
    }

    function consultarTotalGrupo(codigo) {
        try {
            var itens = jQuery('.grupo' + codigo);

            var i = 0;
            var totalSelecionado = 0;
            for (i = 0; i < itens.length; i++) {
                var marcado = itens[i].checked;
                if (marcado) {
                    totalSelecionado++;
                }
            }
            var idVariavel = "form:selecionarTodos" + codigo;
            var mensagemSelecionado = "Selecionar todos <span style='float: right; margin-right: 2%'>Total selecionado: " + totalSelecionado + "</span>"
            var textHtml = jQuery(document.getElementById(idVariavel)).text();
            var quantidade = parseInt(textHtml.substring(24));

            if (totalSelecionado !== quantidade) {
                jQuery(document.getElementById(idVariavel)).html(mensagemSelecionado);
            }
        }catch (e) {
            console.log("ERRO consultarTotalGrupo: "+e);
        }
    }
    
    function consultarTotalGrupoHabi(codigo) {
        try {
            var itens = jQuery('.grupo' + codigo);

            var i = 0;
            var totalSelecionado = 0;
            for (i = 0; i < itens.length; i++) {
                var marcado = itens[i].checked;
                if (marcado) {
                    totalSelecionado++;
                }
            }
            var idVariavel = "form:selecionarTodosHabi" + codigo + "";
            var mensagemSelecionado = "Selecionar todos <span style='float: right; margin-right: 2%'>Total selecionado: " + totalSelecionado + "</span>"
            var textHtml = jQuery(document.getElementById(idVariavel)).text();
            var quantidade = parseInt(textHtml.substring(24));
            if (totalSelecionado !== quantidade) {
                jQuery(document.getElementById(idVariavel)).html(mensagemSelecionado);

            }
        }catch (e) {
            console.log("ERRO consultarTotalGrupoHabi: "+e);
        }
    }

    mostrarTodasTelas();
    consultarTotalSelecionado();
    var eVisivel = jQuery(document.getElementById('form:totalUsuarioSelecionado')).is(':visible');
    if(eVisivel){
        consultarTotalSelecionadoPorGrupo();
    }
    
    var eVisivelHabi = jQuery(document.getElementById('form:totalUsuarioSelecionadoHabi')).is(':visible');
    if(eVisivelHabi){
        consultarTotalSelecionadoPorGrupoHabi();
    }

    function initposgeracao() {
        mostrarTodasTelas();
        consultarTotalSelecionado();
        var eVisivel = jQuery(document.getElementById('form:totalUsuarioSelecionado')).is(':visible');
        if(eVisivel){
            consultarTotalSelecionadoPorGrupo();
        }

        var eVisivelHabi = jQuery(document.getElementById('form:totalUsuarioSelecionadoHabi')).is(':visible');
        if(eVisivelHabi){
            consultarTotalSelecionadoPorGrupoHabi();
        }
    }
</script>





