<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<!-- inicio box -->
<h:panelGroup layout="blokc" styleClass="menuLateral">
    <jsp:include page="includes/include_menulateral_acessorapido.jsp" flush="true" />
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Se&ccedil;&atilde;o
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a class="titulo3 linkFuncionalidade" href="indexBasico.jsp">Cadastros auxiliares</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares"
                          title="Clique e saiba mais: Cadastros Auxiliares" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{(not LoginControle.apresentarLinkZW) && LoginControle.apresentarLinkEstudio}">

            <a class="titulo3 linkFuncionalidade" href="indexPlano.jsp">Produtos e Ambientes</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT"
                          title="Clique e saiba mais: Produtos, Planos e Turmas" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{((not LoginControle.apresentarLinkZW) && LoginControle.apresentarLinkEstudio) == false}">

            <a class="titulo3 linkFuncionalidade" href="indexPlano.jsp">Produtos, Planos e Turmas</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT"
                          title="Clique e saiba mais: Produtos, Planos e Turmas" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a class="titulo3 linkFuncionalidade" href="indexFinanceiro.jsp">Config. Financeiras</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras"
                          title="Clique e saiba mais: Configurações Financeiras" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a class="titulo3 linkFuncionalidade" href="indexArquitetura.jsp">Acesso ao sistema</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Acesso_ao_Sistema"
                          title="Clique e saiba mais: Acesso ao Sistema" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.apresentarLinkZW}">

            <a class="titulo3 linkFuncionalidade" href="indexContrato.jsp">Config. de contrato</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._de_Contrato"
                          title="Clique e saiba mais: Conf. de Contrato" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>
<!-- fim box -->
