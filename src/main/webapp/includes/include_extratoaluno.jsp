<%@include file="imports.jsp" %>

<style>
    .extratoAluno .rich-table-row{
        height: 45px !important;
    }
</style>

<h:outputText value="Extrato do aluno" rendered="#{TelaClienteControle.integraProtheus}"
              style="margin-top: 20px; display: block;"
              styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

<h:panelGroup layout="block" id="tabelaMovParcelasExtrato" rendered="#{TelaClienteControle.integraProtheus}">

    <h:outputText value="Nenhuma parcela."
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-14 cinza pl20"
                  rendered="#{TelaClienteControle.listaParcelas.count <= 0}"/>

    <rich:dataTable styleClass="tabelaDados semZebra extratoAluno"
                    id="listaHistoricoParcelaExtrato"
                    value="#{TelaClienteControle.listaHistoricoParcelas}"
                    rendered="#{TelaClienteControle.listaParcelas.count > 0}"
                    var="historicoParcela">

        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
            </f:facet>
            <h:outputText id="listaHistoricoParcelaExtratoDescricao" styleClass="tooltipster"
                          value="#{historicoParcela.descricao}"
                          title="#{historicoParcela.toolTipsterParcelasRenegociadas}"/>
            <h:outputText rendered="#{not empty historicoParcela.cupomDesconto}" value=" - CUPOM #{historicoParcela.cupomDesconto}"
                          title="#{historicoParcela.toolTipsterParcelasRenegociadas}"/>
        </rich:column>


        <rich:column>
            <f:facet name="header">
                <a4j:commandLink reRender="tabelaMovParcelasExtrato"
                                 actionListener="#{TelaClienteControle.ordenarLista}"
                                 style="padding: 7px 0;font-size: 1em; color: #777; font-weight: bold; border: 0; text-transform: uppercase; background-color: white;">
                    <f:attribute name="tipo"
                                 value="LISTA_PARCELAS"/>
                    <f:attribute name="orderBy"
                                 value="dataVencimento"/>
                    <h:outputText
                            value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                    <i class="${TelaClienteControle.listaParcelas.icon}" style="transform: rotate(90deg);"></i>
                </a4j:commandLink>
            </f:facet>
            <h:outputText id="listaHistoricoParcelaExtratoVencimento" value="#{historicoParcela.dataVencimento_Apresentar}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="PAGAMENTO"/>
            </f:facet>
            <h:outputText value="#{historicoParcela.infoExtrato.pagamentoApresentar}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="MEIO"/>
            </f:facet>
            <h:graphicImage url="#{historicoParcela.infoExtrato.meio}"
                            rendered="#{not empty historicoParcela.infoExtrato.meio}"
                            style="width: 30px" title="#{historicoParcela.infoExtrato.fp}"
                            styleClass="tooltipster"/>
        </rich:column>


        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
            </f:facet>
            <h:outputText id="listaHistoricoParcelaExtratoParcela" value="#{historicoParcela.valorParcela}"
                          >
                <f:converter converterId="FormatadorNumerico" />
            </h:outputText>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Status" />
            </f:facet>
            <h:outputText id="listaHistoricoParcelaExtratoSituacao" value="#{historicoParcela.situacao eq 'EA' ? 'Pendente' : historicoParcela.situacao_Apresentar}" />
        </rich:column>

        <rich:column style="text-align: center">
            <f:facet name="header">
                <h:outputText value="Protheus" style="width: 100%; text-align: center; display: block"  />
            </f:facet>

            <a4j:commandLink action="#{TelaClienteControle.obterStatusProtheus}"
                             reRender="statusProtheus"
                             rendered="#{not empty historicoParcela.infoExtrato.statusProtheus}"
                             oncomplete="#{TelaClienteControle.msgAlert}"
                             style="display: inline; font-size: 14px">
                <h:outputText rendered="#{historicoParcela.infoExtrato.statusProtheus eq 'SUCESSO'}"
                              styleClass="fa-icon-ok-sign" style="color: #3cdb5c;"/>

                <h:outputText rendered="#{historicoParcela.infoExtrato.statusProtheus eq 'ERRO'}"
                              styleClass="fa-icon-ban-circle" style="color: #db394f;"/>

                <h:outputText rendered="#{historicoParcela.infoExtrato.statusProtheus eq 'AGUARDANDO'}"
                              styleClass="fa-icon-time" style="color: #6e6ddb;"/>
            </a4j:commandLink>

        </rich:column>
        <rich:column style="text-align: center">
            <f:facet name="header">
                <h:outputText value="A��es" style="width: 100%; text-align: center; display: block" />
            </f:facet>

            <h:outputLink rendered="#{not empty historicoParcela.infoExtrato.nota}" 
                          target="_blank" style="display: inline;"
                          title="Nota fiscal eletr�nica"
                          value="#{historicoParcela.infoExtrato.nota}">
                <h:graphicImage url="./imagens/nfe.png" style="width: 30px"/>
            </h:outputLink>

        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" width="100%" rendered="#{TelaClienteControle.listaParcelas.count > 0}" columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{TelaClienteControle.listaParcelas.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="tabelaMovParcelasExtrato"
                                             actionListener="#{TelaClienteControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left" id="primPagParcelas"></i>
                                <f:attribute name="tipo" value="LISTA_PARCELAS" />
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaMovParcelasExtrato"
                                             actionListener="#{TelaClienteControle.paginaAnterior}">
                                <i class="fa-icon-angle-left" id="pagAntParcelas"></i>
                                <f:attribute name="tipo" value="LISTA_PARCELAS" />
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaParcelas.paginaAtualApresentar}" rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="tabelaMovParcelasExtrato"
                                             actionListener="#{TelaClienteControle.proximaPagina}">
                                <i class="fa-icon-angle-right" id="proxPagParcela"></i>
                                <f:attribute name="tipo" value="LISTA_PARCELAS" />
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaMovParcelasExtrato"
                                             actionListener="#{TelaClienteControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right" id="ultPagParcelas"></i>
                                <f:attribute name="tipo" value="LISTA_PARCELAS" />
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por p�gina "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{TelaClienteControle.listaParcelas.limit}" id="qtdeItensPaginaParcExtrato">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="tabelaMovParcelasExtrato">
                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</h:panelGroup>