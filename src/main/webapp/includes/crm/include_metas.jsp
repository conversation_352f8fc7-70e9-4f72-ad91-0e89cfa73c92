
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>


<table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" >
    <tr>
        <td width="100%">
            <table width="95%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right: 10px; margin-bottom: 10px;">
                <tr>
                    <td><h:panelGrid id="panelIndicador" width="100%" cellpadding="0" cellspacing="0" columns="3" columnClasses="alinhamentoSuperior,tituloboxcentro2, tituloboxcentro2" style="margin-right:10px;margin-bottom:10px;">
                            <table width="100%" height="420" border="0" align="left" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50" /></td>
                                    <td width="100%" align="left" colspan="6" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding: 11px 0 0 0;">BI - Acompanhamento Metas
                                        <a href="${SuperControle.urlWikiCRM}Acompanhamento_de_Metas" class="linkWiki"  title="Clique e saiba mais: BI - Acompanhamento de Metas" target="_blank" >
                                            <img src="imagens/wiki_link2.gif" alt="Clique e saiba mais: Acompanhamento de Metas" class="linkWiki"/>
                                        </a></td>
                                    <td class="tituloboxcentro2">
                                        <rich:calendar id="dataBasePanelIndicadorDeRetencao" disabled="false" value="#{PendenciasCRMControle.dataPesquisa}" showInput="false" direction="bottom-left" zindex="1000" showWeeksBar="false">
                                            <a4j:support event="onchanged" ajaxSingle="true" action="#{PendenciasCRMControle.consultarPendencias}"  reRender="pendenciasCRM" />
                                        </rich:calendar></td>
                                    <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50" /></td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif" /></td>
                                    <td align="left" colspan="9" valign="top" bgcolor="#ffffff">

                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 10px;">
                                            <tr>
                                                <td>
                                                    <h:panelGrid id="pendenciasCRM" columnClasses="colunaEsquerda" columns="1" width="100%">
                                                        <h:panelGrid columnClasses="colunaCentralizada" columns="1" width="100%">
                                                            <h:panelGroup layout="block" id="panelDataSelecionadaRotatividade" style="text-align:right;">

                                                                <h:outputText styleClass="textverysmall" value="Data base: " style="text-align:right;color:#0f4c6b;" />
                                                                <h:outputText styleClass="textverysmall" value="#{PendenciasCRMControle.dataPesquisa}" style="text-align:right;color:#0f4c6b;">
                                                                    <f:convertDateTime type="date" dateStyle="short" locale="pt" timeZone="America/Sao_Paulo" pattern="dd/MM/yyyy" />
                                                                </h:outputText>
                                                            </h:panelGroup>



                                                        </h:panelGrid>

                                                        <!-- ----------------------- INICIO - TABLE DAS METAS ----------------------------------- -->
                                                        <h:panelGroup>
                                                            <p style="margin-bottom:6px;"><img style="vertical-align:middle;margin-right:6px;" src="images/arrow_news.gif">

                                                                <h:outputText styleClass="tituloCamposAzulGrande" value="#{PendenciasCRMControle.mensagemTitulo}"></h:outputText> </p>
                                                            </h:panelGroup>
                                                            <rich:spacer height="10px"/>
                                                            <h:panelGrid width="100%" columns="1" rowClasses="linhaImpar,linhaPar" columnClasses="classPadding">

                                                            <h:panelGroup style="width: 100%">
                                                                &nbsp;&nbsp;&nbsp;&nbsp;<a4j:commandLink id="fechadas" oncomplete="Richfaces.showModalPanel('panelMetas');"
                                                                                 reRender="panelMetas" actionListener="#{PendenciasCRMControle.exibicaoListener}"
                                                                                 styleClass="tituloCamposAzulComanddLink"
                                                                                 value="#{PendenciasCRMControle.totalMetasFechadas} ">
                                                                    <f:attribute name="codigoExibicao" value="2" />
                                                                </a4j:commandLink>
                                                                <h:outputText styleClass="tituloCamposAzulGrande" value=" - Metas fechadas"/>
                                                            </h:panelGroup>

                                                            <h:panelGroup style="width: 100%">
                                                                &nbsp;&nbsp;&nbsp;&nbsp;<a4j:commandLink id="abertasNaoFechadas" oncomplete="Richfaces.showModalPanel('panelMetas');"
                                                                                 reRender="panelMetas" actionListener="#{PendenciasCRMControle.exibicaoListener}"
                                                                                 styleClass="tituloCamposAzulComanddLink"
                                                                                 value="#{PendenciasCRMControle.totalMetasAbertasNaoFechadas} ">
                                                                    <f:attribute name="codigoExibicao" value="1" />
                                                                </a4j:commandLink>
                                                                <h:outputText styleClass="tituloCamposAzulGrande" value=" - Metas Abertas e n�o fechadas"/>
                                                            </h:panelGroup>

                                                            <h:panelGroup style="width: 100%">
                                                                &nbsp;&nbsp;&nbsp;&nbsp;<a4j:commandLink id="naoAbertas" oncomplete="Richfaces.showModalPanel('panelMetas');"
                                                                                 reRender="panelMetas"  actionListener="#{PendenciasCRMControle.exibicaoListener}"
                                                                                 styleClass="tituloCamposAzulComanddLink"
                                                                                 value="#{PendenciasCRMControle.totalMetasNaoAbertas} ">
                                                                    <f:attribute name="codigoExibicao" value="3" />
                                                                </a4j:commandLink>
                                                                <h:outputText styleClass="tituloCamposAzulGrande" value=" - Metas N�o Abertas"/>

                                                            </h:panelGroup>
                                                            <rich:spacer height="2px"/>
                                                            <a4j:commandLink id="todas" oncomplete="Richfaces.showModalPanel('panelMetas');"
                                                                             reRender="panelMetas"
                                                                             styleClass="tituloCamposAberturaMeta"
                                                                             actionListener="#{PendenciasCRMControle.exibicaoListener}"
                                                                             value="Ver todas">
                                                                <f:attribute name="codigoExibicao" value="4" />
                                                            </a4j:commandLink>

                                                        </h:panelGrid>




                                                        <!-- ----------------------- FIM - TABLE DAS METAS ----------------------------------- -->

                                                        <h:panelGrid id="panelListaColaboradorPendencia" columnClasses="colunaEsquerda" columns="1" width="100%">
                                                            <rich:dataTable id="listaColaboradorPendencia" width="100%" columnClasses="colunaEsquerda"
                                                                            styleClass="semBorda" value="#{PendenciasCRMControle.listaGrupoColaborador}" var="grupoColaborador">
                                                                <f:facet name="header">
                                                                    <h:panelGroup layout="block">
                                                                        <h:panelGroup layout="block" style="float: left">
                                                                            <h:panelGroup rendered="#{!PendenciasCRMControle.mostrarGruposAcompanhamento}" layout="block">
                                                                                <i class="fa-icon-angle-right"></i>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup rendered="#{PendenciasCRMControle.mostrarGruposAcompanhamento}" layout="block">
                                                                                <i class="fa-icon-angle-down"></i>
                                                                            </h:panelGroup>
                                                                            <a4j:commandLink styleClass="titulo3"
                                                                                             action="#{PendenciasCRMControle.toggleMostrarGruposAcompanhamento}"
                                                                                             reRender="panelIndicador">
                                                                                <h:outputText styleClass="titulo3" value="V�nculos de Carteiras: "/>
                                                                            </a4j:commandLink>
                                                                        </h:panelGroup>
                                                                        <h:panelGroup style="float: right"
                                                                                      layout="block">
                                                                            <h:selectBooleanCheckbox
                                                                                    id="selectUsuarioLogadoPendencia"
                                                                                    rendered="#{PendenciasCRMControle.mostrarCheckboxAcompanhamento}"
                                                                                    value="#{PendenciasCRMControle.marcarUsuarioAcompanhamento}">
                                                                                <a4j:support event="onclick"
                                                                                             action="#{PendenciasCRMControle.selecionarParticipante}"
                                                                                             reRender="panelIndicador"/>
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText styleClass="titulo3"
                                                                                          value="#{PendenciasCRMControle.usuarioLogado.nomeAbreviado}"/>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>
                                                                </f:facet>

                                                                <rich:column styleClass="semBorda" rendered="#{PendenciasCRMControle.mostrarGruposAcompanhamento}">
                                                                    <a4j:commandButton styleClass="botoes" title="Visualizar Participantes do Grupo"
                                                                                       action="#{PendenciasCRMControle.selecionarGrupoColaboradorParticipante}"
                                                                                       image="./imagensCRM/botaoAdicionarGrupos.png" reRender="panelListaColaboradorPendencia" />
                                                                    <rich:spacer width="5px;" />
                                                                    <a4j:commandLink styleClass="botoes" title="Visualizar Participantes do Grupo" reRender="panelListaColaboradorPendencia"
                                                                                     action="#{PendenciasCRMControle.selecionarGrupoColaboradorParticipante}">
                                                                        <h:outputText styleClass="tituloCamposAberturaMeta" value="#{grupoColaborador.descricao}" />
                                                                    </a4j:commandLink>
                                                                    <rich:dataGrid value="#{grupoColaborador.grupoColaboradorParticipanteVOs}" var="participante"
                                                                                   width="100%" columns="3" elements="#{grupoColaborador.totalParticipantes}" columnClasses="semBorda"
                                                                                   styleClass="semBorda" rendered="#{grupoColaborador.abrirSimpleTooglePanelPassivo}"
                                                                                   cellpadding="0" cellspacing="0">
                                                                        <h:panelGroup>
                                                                            <h:selectBooleanCheckbox value="#{participante.grupoColaboradorParticipanteEscolhido}">
                                                                                <a4j:support event="onclick" action="#{PendenciasCRMControle.selecionarParticipante}"
                                                                                             reRender="pendenciasCRM" />
                                                                            </h:selectBooleanCheckbox>
                                                                            <rich:spacer width="5"/>
                                                                            <h:outputText styleClass="titulo3" value="#{participante.colaboradorParticipante.pessoa.primeiroNomeConcatenado}" />
                                                                        </h:panelGroup>
                                                                    </rich:dataGrid>
                                                                </rich:column>
                                                            </rich:dataTable>
                                                        </h:panelGrid>
                                                        <h:panelGrid id="panelAtualizarLista" columnClasses="colunaCentralizada" columns="1" width="100%">
                                                            <a4j:commandButton id="atualizarBIMetas" styleClass="botoes" action="#{PendenciasCRMControle.consultarPendencias}" image="./imagensCRM/atualizar.png" reRender="panelIndicador">
                                                                <rich:toolTip followMouse="true" direction="top-right" style="width:150px; height:30px; " showDelay="500">
                                                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_descricaoBotaoAtualizarIndicadores}" />
                                                                </rich:toolTip>
                                                            </a4j:commandButton>
                                                        </h:panelGrid>
                                                    </h:panelGrid>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>

                                    <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif" /></td>
                                </tr>
                                <tr>
                                    <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                    <td align="left" colspan="9" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif" /></td>
                                    <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20" /></td>
                                </tr>
                            </table>
                        </h:panelGrid></td>
                </tr>
            </table>
        </td>
    </tr>
</table>
<a4j:commandButton id="atualizarPendencias" action="#{PendenciasCRMControle.consultarPendencias}" style="visibility: hidden;"
                   reRender="pendenciasCRM"  />
