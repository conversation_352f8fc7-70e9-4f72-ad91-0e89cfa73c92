<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText
            value="Lançamento de Cheque/Cartão de Crédito Avulso"/></title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="Lançamento de Cheque/Cartão de Crédito Avulso"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-realizar-o-lancamento-de-cheques-cartoes-avulsos/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
            <%-- INICIO CONTENT --%>
            <h:panelGroup>
                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-2-3 text-right">

                        <h:selectOneRadio id="mudarChequeCartao" tabindex="1"
                                          style="display: inline-block; line-height: 44px; margin-right: 10px;"
                                          onchange="document.getElementById('form:mudarPagina').click()"
                                          styleClass="tituloCampos" value="#{RecebivelAvulsoControle.tipo}">
                            <f:selectItem itemLabel="Cheque" itemValue="1"/>
                            <f:selectItem itemLabel="Cartão de Crédito" itemValue="2"/>

                        </h:selectOneRadio>
                        <h:commandButton style="display: none;" id="mudarPagina"
                                         action="#{RecebivelAvulsoControle.mudarTipo}"/>

                        <a4j:commandLink id="btnNovo"
                                         style=" margin-right: 5px; margin-top: 5px;"
                                         styleClass="pure-button pure-button-primary"
                                         action="#{RecebivelAvulsoControle.novo}"
                                         accesskey="1">
                            &nbsp ${msg_bt.btn_cadastrar_novo}
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <c:if test="${RecebivelAvulsoControle.tipo == 1}">
                    <table id="tblBanco" class="tabelaBanco pure-g-r pure-u-11-12 margin-0-auto">
                        <thead>
                        <th>CÓDIGO</th>
                        <th>CLIENTE</th>
                        <th>LANÇAMENTO</th>
                        <th>FATURAMENTO</th>
                        <th>COMPENSAÇÃO</th>
                        <th>BANCO</th>
                        <th>AGÊNCIA</th>
                        <th>CONTA</th>
                        <th>NÚMERO</th>
                        <th>VALOR</th>
                        <th>NOME NO CHEQUE</th>
                        </thead>
                        <tbody></tbody>
                    </table>
                </c:if>

                <c:if test="${RecebivelAvulsoControle.tipo == 2}">
                    <table id="tblCartao" class="tabelaCartao pure-g-r pure-u-11-12 margin-0-auto">
                        <thead>
                        <th>Código</th>
                        <th>Cliente</th>
                        <th>Lançamento</th>
                        <th>Faturamento</th>
                        <th>Compensação</th>
                        <th>Autorização</th>
                        <th>Número</th>
                        <th>Operadora</th>
                        <th>Valor</th>
                        <th>Nome no cartão</th>
                        </thead>
                        <tbody></tbody>
                    </table>
                </c:if>

                <a4j:jsFunction name="jsEditar" action="#{RecebivelAvulsoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{RecebivelAvulsoControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{RecebivelAvulsoControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty RecebivelAvulsoControle.mensagem}"
                              value=" #{RecebivelAvulsoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty RecebivelAvulsoControle.mensagemDetalhada}"
                              value=" #{RecebivelAvulsoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>

    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>
    <c:if test="${RecebivelAvulsoControle.tipo == 1}">
        <script>
            jQuery(window).on("load", function () {
                iniciarTabela("tabelaBanco", "${contexto}/prest/financeiro/chequeAvulso", 0, "asc", "", true);
            });
        </script>
    </c:if>

    <c:if test="${RecebivelAvulsoControle.tipo == 2}">
        <script>
            jQuery(window).on("load", function () {
                iniciarTabela("tabelaCartao", "${contexto}/prest/financeiro/cartaoAvulso", 0, "asc", "", true);
            });
        </script>
    </c:if>

</f:view>