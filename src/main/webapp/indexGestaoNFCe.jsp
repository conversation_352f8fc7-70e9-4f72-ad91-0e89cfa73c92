<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>



<head>

        <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
        <script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>
        <link href="./css/otimize.css" rel="stylesheet" type="text/css">
        <link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
        <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
        <link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
        <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<script>
    function carregarTooltipsterGestaoNotas(){
        carregarTooltipNota(jQuery('.tooltipster'));
    }
    function carregarTooltipNota(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>




<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <title>${msg_aplic.prt_gestao_nfce}</title>

    <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_gestao_nfce}"></c:set>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}como-emitir-nota-fiscal-pelo-gestao-de-notas-ou-pelo-gestao-de-nfc-e/"></c:set>

    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <style type="text/css">
        .divComBorda {
            border: solid 1px #C0C0C0;
        }
    </style>

    <h:form id="form" styleClass="pure-form pure-u-1" style="height: 100%;">
        <hr style="border-color: #e6e6e6;"/>
        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" style="margin-top: 10px"
                     columnClasses="classEsquerda, classDireita" width="100%">

            <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                <h:outputText styleClass="titulo3" value="#{msg_aplic.EMPRESA}:"/>
            </h:panelGroup>

            <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                <h:selectOneMenu id="comboEmpresa" styleClass="form" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{GestaoNFCeControle.empresaVO.codigo}">
                    <f:selectItems value="#{GestaoNFCeControle.listaEmpresas}"/>

                    <a4j:support event="onchange" reRender="form"
                                 action="#{GestaoNFCeControle.consultarDadosEmpresa}"></a4j:support>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos"
                          value="#{GestaoNFCeControle.labelPeriodo}"/>

            <h:panelGroup>
                <h:panelGroup id="pgDataInicio">
                    <rich:calendar id="dataInicio"
                                   value="#{GestaoNFCeControle.dataInicio}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id)"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                    <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                    <rich:jQuery id="mskDataInicio" selector=".rich-calendar-input" timing="onload"
                                 query="mask('99/99/9999')"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos"
                              style="vertical-align: middle" value=" #{msg_aplic.prt_ate} "/>

                <h:panelGroup id="pgDataFim">
                    <rich:calendar id="dataFim"
                                   value="#{GestaoNFCeControle.dataFim}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id)"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                    <h:message for="dataFim" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

            </h:panelGroup>

            <h:outputText styleClass="tituloCampos"
                          value="#{msg_aplic.prt_forma_pagamento} "/>
            <h:panelGroup>
                <h:selectOneMenu value="#{GestaoNFCeControle.formaPagamentoSelecionado}">
                    <f:selectItems value="#{GestaoNFCeControle.selectItemsFormaDePagamento}"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.NOME}"/>
            <h:panelGroup>
                <h:inputText id="nomeCliente" size="50"
                             maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>

                <rich:suggestionbox height="200" width="200"
                                    for="nomeCliente"
                                    fetchValue="#{result.nome}"
                                    suggestionAction="#{GestaoNFCeControle.executarAutocompleteConsultaPessoa}"
                                    minChars="1" rowClasses="20"
                                    status="statusHora"
                                    nothingLabel="#{msg_aplic.prt_nenhuma_pessoa_encontrada}"
                                    var="result" id="suggestionNomeCliente" reRender="mensagem">
                    <a4j:support event="onselect"
                                 action="#{GestaoNFCeControle.selecionarPessoaSuggestionBox}"/>
                    <h:column>
                        <h:outputText value="#{result.nome}"/>
                    </h:column>
                </rich:suggestionbox>
                <a4j:commandButton id="limparAluno"
                                   onclick="document.getElementById('form:nomeCliente').value = null;"
                                   image="/images/limpar.gif" title="#{msg_aplic.prt_limpar_aluno}."
                                   status="false"
                                   action="#{GestaoNFCeControle.limparAluno}"/>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Plano(s)"/>
            <h:panelGroup layout="block" id="filtroPlanos" style="padding: 5px 0px 5px 0px;">
                <a4j:commandLink reRender="panelGeralPlanos"
                                 onclick="Richfaces.showModalPanel('modalPlanos');"
                                 oncomplete="#{GestaoNFCeControle.onComplete};#{GestaoNFCeControle.mensagemNotificar}">
                    Selecionar planos ${GestaoNFCeControle.qtdPlanosSelecionados}
                </a4j:commandLink>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Valor desejado para emissão de notas "/>
            <h:panelGroup>
                <h:inputText id="valor" onfocus="focusinput(this);"
                             onkeypress="return formatar_moeda(this,'.',',',event);"
                             styleClass="form" value="#{GestaoNFCeControle.valorParaSelecionar}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>

                <a4j:commandLink action="#{GestaoNFCeControle.selecionarValor}"
                                 style="margin-left: 5px"
                                 reRender="dtableGestaoNotas, totalizadores,mensagem"
                                 styleClass="pure-button pure-button-small ">
                    <i class="fa-icon-check"></i> &nbsp ${msg_aplic.SELECIONAR}
                </a4j:commandLink>
            </h:panelGroup>

            <h:outputText/>

            <h:panelGroup>
                <h:panelGrid columns="1" width="100%">
                    <h:panelGroup>
                        <a4j:commandLink action="#{GestaoNFCeControle.consultarItens}" id="ConsultarItens"
                                         reRender="dtableGestaoNotas, totalizadores,mensagem"
                                         styleClass="pure-button pure-button-small pure-button-primary "
                                         oncomplete="#{GestaoNFCeControle.mensagemNotificar}">
                            <i class="fa-icon-check"></i> &nbsp ${msg_aplic.CONSULTAR}
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGrid>


        <h:panelGrid id="totalizadores" columns="3" width="100%" columnClasses="w50, w50, w50"
                     style="margin-top: 12px">
            <h:panelGroup>
                <rich:dataTable value="#{GestaoNFCeControle.totalizadores}" width="100%" var="totalizador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.TOTALIZADOR}"/>
                        </f:facet>
                        <h:outputText value="#{totalizador.nome}"/>
                    </rich:column>
                    <rich:column style="text-align: right">
                        <f:facet name="header">
                            <h:outputText value="Qtd"/>
                        </f:facet>
                        <h:outputText value="#{totalizador.quantidade}"/>
                    </rich:column>
                    <rich:column style="text-align: right">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_sifrao_dinheiro}"/>
                        </f:facet>
                        <h:outputText value="#{totalizador.valor_apresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 10px">
                <rich:dataTable value="#{GestaoNFCeControle.totalFormaPg}" width="100%" var="tot">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_forma_pagamento}"/>
                        </f:facet>
                        <h:outputText value="#{tot.nome}"/>
                    </rich:column>
                    <rich:column style="text-align: right">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_sifrao_dinheiro}"/>
                        </f:facet>
                        <h:outputText value="#{tot.valor_apresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGrid id="dtableGestaoNotas" columns="1" width="100%" style="text-align: center" cellpadding="0"
                     cellspacing="0"
                     styleClass="centralizado">
            <h:panelGrid width="100%" style="text-align: right">
                <h:panelGroup layout="block" style="margin-top: 10px">
                    <a4j:commandButton id="exportarExcel"
                                       image="/imagens/btn_excel.png"
                                       style="margin-left: 8px;"
                                       actionListener="#{ExportadorListaControle.exportar}"
                                       rendered="#{not empty GestaoNFCeControle.listaItensApresentar}"
                                       value="Excel"
                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','GestaoNotas', 800,200);#{ExportadorListaControle.msgAlert}"
                                       accesskey="2" styleClass="botoes">
                        <f:attribute name="lista" value="#{GestaoNFCeControle.listaItensApresentar}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos"
                                     value="nome=Nome,matricula=Matrícula,cpf=CPF,valor_apresentar=Valor a Emitir,notaNFSEEmitida=Item Enviado"/>
                        <f:attribute name="prefixo" value="GestaoNotas"/>
                    </a4j:commandButton>
                    <%--BOTÃO PDF--%>
                    <a4j:commandButton id="exportarPdf"
                                       style="margin-left: 8px;"
                                       image="/imagens/imprimir.png"
                                       actionListener="#{ExportadorListaControle.exportar}"
                                       rendered="#{not empty GestaoNFCeControle.listaItensApresentar}"
                                       value="PDF"
                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','GestaoNotas', 800,200);#{ExportadorListaControle.msgAlert}"
                                       accesskey="2" styleClass="botoes">
                        <f:attribute name="lista" value="#{GestaoNFCeControle.listaItensApresentar}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos"
                                     value="nome=Nome,matricula=Matrícula,cpf=CPF,valor_apresentar=Valor a Emitir,notaNFSEEmitida=Item Enviado"/>
                        <f:attribute name="prefixo" value="GestaoNotas"/>
                    </a4j:commandButton>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="margin-top: 5px">
                <rich:dataTable id="tblGestaNotas" width="100%"
                                columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaCentralizada,colunaEsquerda,colunaDireita,colunaDireita,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaEsquerda"
                                value="#{GestaoNFCeControle.listaItensApresentar}"
                                var="item" rowKeyVar="status" rows="30">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{item.matricula}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.MATRICULA}"/>
                        </f:facet>
                        <a4j:commandLink style="#{item.textStyle}" value="#{item.matricula}"
                                         action="#{GestaoNFCeControle.irParaTelaClienteColaborador}"
                                         oncomplete="#{GestaoNFCeControle.onComplete}">
                            <f:param name="state" value="AC"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column sortBy="#{item.nome}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.NOME}"/>
                        </f:facet>
                        <a4j:commandLink style="#{item.textStyle}" value="#{item.nome}"
                                         action="#{GestaoNFCeControle.irParaTelaClienteColaborador}"
                                         oncomplete="#{GestaoNFCeControle.onComplete}">
                            <f:param name="state" value="AC"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column sortBy="#{item.cpf}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="CPF"/>
                        </f:facet>
                        <a4j:commandLink style="#{item.textStyle}" value="#{item.cpf}"
                                         action="#{GestaoNFCeControle.irParaTelaClienteColaborador}"
                                         oncomplete="#{GestaoNFCeControle.onComplete}">
                            <f:param name="state" value="AC"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column sortBy="#{item.descricaoProdutosPagos_apresentar}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.PRODUTO}"/>
                        </f:facet>
                        <h:outputText escape="false" value="#{item.descricaoProdutosPagos_apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.reciboPagamentoVO.codigo}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="Recibo"/>
                        </f:facet>
                        <h:outputText escape="false" value="#{item.reciboPagamentoVO.codigo}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.valor}" style="#{item.style}" styleClass="col-text-align-right"
                                 headerClass="col-text-align-right">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_valor_emitir}"/>
                        </f:facet>
                        <h:outputText value="#{item.valor_apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{item.nfseemitida}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="Nota enviada"/>
                        </f:facet>
                        <h:panelGroup layout="block" style="display: inline-flex">
                            <h:panelGroup layout="block"
                                          style="margin-top: 1px;margin-right: 10px; width: 10px; height: 10px; background-color:#{item.cor}"
                                          id="teste"/>
                            <h:outputText value="#{item.notaNFSEEmitida}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column sortBy="#{item.id_NFCe}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="Id_NFCe"/>
                        </f:facet>
                        <h:outputText value="#{item.id_NFCe}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.notaFiscalVO.statusNotaApresentar}"
                                 style="#{item.style}"
                                 rendered="#{GestaoNFCeControle.empresaVO.usaEnotas}">
                        <f:facet name="header">
                            <h:outputText value="Status"/>
                        </f:facet>
                        <h:outputText value="#{item.notaFiscalVO.statusNotaApresentar}"
                                      styleClass="tooltipster"
                                      title="#{item.notaFiscalVO.statusNotaHint}"/>
                    </rich:column>


                    <rich:column style="#{item.style}">
                        <f:facet name="header">
                            <h:panelGroup layout="block" style="display: inline-flex;">
                                <h:outputText value="#{msg_aplic.ENVIAR}"/>

                                <h:selectBooleanCheckbox value="#{GestaoNFCeControle.marcarTodos}"
                                                         style="margin-left: 5px"
                                                         title="Marcar todos os itens de todas as páginas."
                                                         id="marcarTodos">
                                    <a4j:support event="onchange"
                                                 action="#{GestaoNFCeControle.acaoMarcarTodos}"
                                                 reRender="totalizadores,mensagem,tblGestaNotas"/>
                                </h:selectBooleanCheckbox>
                            </h:panelGroup>
                        </f:facet>
                        <h:selectBooleanCheckbox rendered="#{item.nfseemitida}" disabled="true" id="notaEnviada"
                                                 value="#{item.selecionado}"/>
                        <h:selectBooleanCheckbox rendered="#{!item.nfseemitida}" value="#{item.selecionado}"
                                                 id="notaNaoEnviada">
                            <a4j:support event="onchange"
                                         action="#{GestaoNFCeControle.calcularValorSelecionado}"
                                         reRender="totalizadores,mensagem"/>
                        </h:selectBooleanCheckbox>
                    </rich:column>
                    <rich:column style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.RETORNO}"/>
                        </f:facet>
                        <h:outputText id="retorno" value="#{item.retorno}"/>
                    </rich:column>
                    <rich:column style="#{item.style}">
                        <f:facet name="header">
                            <h:panelGroup layout="block" style="display: inline-flex;">
                                <h:outputText value="Desvincular"/>
                                <h:selectBooleanCheckbox value="#{GestaoNFCeControle.desvincularTodas}"
                                                         style="margin-left: 5px"
                                                         title="Marcar todos os itens de todas as páginas."
                                                         id="desvincularTodas">
                                    <a4j:support event="onchange"
                                                 action="#{GestaoNFCeControle.acaoDesvincularTodas}"
                                                 reRender="totalizadores,mensagem,tblGestaNotas"/>
                                </h:selectBooleanCheckbox>
                            </h:panelGroup>
                        </f:facet>
                        <h:selectBooleanCheckbox rendered="#{item.apresentarDesvincularNFCe}" value="#{item.selecionadoDesvincular}"
                                                 id="selecionadoDesvincular">
                        </h:selectBooleanCheckbox>
                    </rich:column>
                    <rich:column style="#{item.style}">
                        <f:facet name="header">
                            <h:panelGroup layout="block" style="display: inline-flex;">
                                <h:outputText value="Excluir"/>
                                <h:selectBooleanCheckbox value="#{GestaoNFCeControle.excluirTodas}"
                                                         style="margin-left: 5px"
                                                         title="Marcar todos os itens de todas as páginas."
                                                         id="excluirTodas">
                                    <a4j:support event="onchange"
                                                 action="#{GestaoNFCeControle.acaoExcluirTodas}"
                                                 reRender="totalizadores,mensagem,tblGestaNotas"/>
                                </h:selectBooleanCheckbox>
                            </h:panelGroup>
                        </f:facet>
                        <h:selectBooleanCheckbox rendered="#{item.apresentarExcluirNotaFiscalNFCe && LoginControle.permissaoAcessoMenuVO.permiteExcluirNotaFiscal}" value="#{item.selecionadoExcluir}"
                                                 id="selecionadoExcluir">
                        </h:selectBooleanCheckbox>
                    </rich:column>
                    <rich:column sortBy="#{item.tipoNota}" style="#{item.style}">
                        <f:facet name="header">
                            <h:outputText value="Tipo"/>
                        </f:facet>
                        <h:outputText value="#{item.tipoNota}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller for="tblGestaNotas" maxPages="20" page="#{GestaoNFCeControle.scrollerPage}"
                                   id="sc2"/>

                <script>
                    carregarTooltipsterGestaoNotas();
                </script>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="margin-top: 20px; text-align: center; margin-bottom: 10px;"
                      id="operacoes">

            <a4j:commandLink style="padding-right: 12px" action="#{GestaoNFCeControle.enviarNotas}"
                             styleClass="pure-button pure-button-primary tooltipster"
                             status="false" title="Enviar todas as notas selecionadas"
                             reRender="dtableGestaoNotas, totalizadores,mensagem,formMdlEnviandoNotas"
                             onclick="Richfaces.showModalPanel('modalLoadEnviandoNotas');"
                             oncomplete="Richfaces.hideModalPanel('modalLoadEnviandoNotas');#{GestaoNFCeControle.mensagemNotificar}">
                <i class="fa-icon-cloud-upload"></i> &nbsp ${msg_aplic.prt_enviar_selecionados}
            </a4j:commandLink>

            <a4j:commandLink action="#{GestaoNFCeControle.desvincularSelecionados}"
                             styleClass="pure-button pure-button-primary tooltipster"
                             status="false" style="margin-left: 10px;"
                             title="Desvincular todas as notas selecionadas"
                             reRender="dtableGestaoNotas, totalizadores,mensagem,formMdlEnviandoNotas"
                             oncomplete="#{GestaoNFCeControle.mensagemNotificar}">
                <i class="fa-icon-ban-circle"></i> &nbsp ${msg_aplic.prt_desvincular_selecionados}</a4j:commandLink>

            <a4j:commandLink action="#{GestaoNFCeControle.excluirSelecionados}"
                             styleClass="pure-button pure-button-primary tooltipster"
                             status="false" style="margin-left: 10px;"
                             title="Excluir todas as notas selecionadas"
                             reRender="dtableGestaoNotas, totalizadores,mensagem,formMdlEnviandoNotas"
                             oncomplete="#{GestaoNFCeControle.mensagemNotificar}">
                <i class="fa-icon-trash"></i> &nbsp ${msg_aplic.prt_excluir_selecionados}</a4j:commandLink>

            <a4j:commandLink action="#{GestaoNFCeControle.realizarConsultaLogObjetoSelecionado}"
                             reRender="form" style="margin-left: 12px"
                             styleClass="pure-button pure-button-secundary tooltipster"
                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                             title="#{msg_aplic.prt_visualizar_log}">
                <i class="fa-icon-list"></i> &nbsp ${msg_aplic.LOG}
            </a4j:commandLink>


            <h:panelGrid id="tabelaLegenda" columns="2" width="50%" columnClasses="w50, w50"
                         style="margin-top: 15px">

                <rich:dataTable value="#{GestaoNFCeControle.situacaoNFCe}" var="situacao" width="100%">
                    <f:facet name="header">
                        <h:outputText value="Legenda"/>
                    </f:facet>

                    <rich:column style="background-color:#{situacao.classe}">
                        <h:panelGroup layout="block"
                                      style="border:none; width: 10px; height: 10px; background-color:#{situacao.cor}"/>
                    </rich:column>

                    <rich:column>
                        <h:outputText value="#{situacao.descricao}"/>
                    </rich:column>

                    <rich:column>
                        <h:outputText value="#{situacao.hint}"/>
                    </rich:column>
                </rich:dataTable>

            </h:panelGrid>
        </h:panelGroup>

        <h:panelGrid id="mensagem" columns="1" width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem" value="#{GestaoNFCeControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoNFCeControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGrid>
        <rich:jQuery selector=".rich-calendar-input.MMyyyy" timing="onload" query="mask('99/9999')"/>
    </h:form>

    <rich:modalPanel id="modalLoadEnviandoNotas" styleClass="novaModal" width="450" autosized="true"
                     shadowOpacity="true">
        <h:form id="formMdlEnviandoNotas">
            <h:panelGroup id="pnlMdlEnviandoNotas" layout="block" style="display:flex; flex-direction: column; align-items: center;">
                <h:outputText id="msgEnviandoNotas" value="#{GestaoNFCeControle.msgEnviandoNotas}"
                              style="margin-top: 10px;" styleClass="texto-size-20-real"/>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel domElementAttachment="parent" id="modalPlanos"
                     styleClass="novaModal"
                     autosized="true" width="600"
                     height="150" shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Filtrar Planos"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hideLinkModalPlanos"/>
                <rich:componentControl for="modalPlanos" attachTo="hideLinkModalPlanos" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formPlanos">

            <h:outputText style="font-size: 14px;font-weight: bold;" value="#{GestaoNFCeControle.qtdPlanosSelecionados}"/>

            <h:panelGroup id="panelGeralPlanos"
                          layout="block"
                          style="display: inherit;max-width: 700px; padding-top: 10px">

                <rich:dataTable id="tblPlanos" value="#{GestaoNFCeControle.planos}" width="100%" var="plano" rows="20">
                    <rich:column style="text-align: center">
                        <f:facet name="header">
                            <h:selectBooleanCheckbox value="#{GestaoNFCeControle.selecionarTodosPlanos}">
                                <a4j:support action="#{GestaoNFCeControle.marcarTodosPlanos}"
                                             status="false"
                                             reRender="formPlanos, form:filtroPlanos" event="onclick"/>
                            </h:selectBooleanCheckbox>
                        </f:facet>
                        <h:selectBooleanCheckbox value="#{plano.selecionado}">
                            <a4j:support action="#{GestaoNFCeControle.calcularPlanosSelecionados}"
                                         status="false"
                                         reRender="formPlanos, form:filtroPlanos" event="onclick"/>
                        </h:selectBooleanCheckbox>
                    </rich:column>
                    <rich:column style="text-align: left">
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:outputText value="#{plano.descricao}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formPlanos:tblPlanos" id="scPlanos"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="text-align: center; width: 100%; padding: 10px">

                <a4j:commandLink reRender="panelGeralPlanos"
                                 onclick="Richfaces.hideModalPanel('modalPlanos');"
                                 value="Fechar"
                                 styleClass="pure-button pure-button-secundary"/>
            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>


    <jsp:include page="/includes/include_expiracao_alerta_popup.jsp" flush="true">
        <jsp:param name="apresentarModal" value="${SuporteControle.apresentarMensagemNFe}"/>
    </jsp:include>
</f:view>
<script>
    carregarTooltipsterGestaoNotas();
</script>
