<%@page pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<rich:modalPanel id="modalCentros" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Selecionar Centro de Custos"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelinkSelecionarCentro" />
            <rich:componentControl for="modalCentros"
                                   attachTo="hidelinkSelecionarCentro" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <!-- TREE VIEW -->
    <div id="content">

        <rich:panel id="centro">
            <h:panelGrid columns="2" columnClasses="top,top" width="100%">
                <rich:tree style="height: 350px; width: 420px; overflow: scroll;" id="treeViewCentro"
                           iconCollapsed="/images/expandTree.gif"
                           iconExpanded="/images/collapTree.gif"
                           nodeSelectListener="#{CentroCustosControle.processSelection}"
                           nodeFace="#{treeNode.parent.parent == null ? 'node' : 'leaf'}"
                           reRender="selectedNode, dados" ajaxSubmitSelection="true"
                           switchType="client" data="descricao"
                           value="#{CentroCustosControle.treeNode}" var="item">
                    <rich:treeNode id="tNodeC"   type="node" >
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formCentro:botaoSelecao','formCentro:codigoSelecao', #{item.codigo})"
                                         id="descricaoDetalhada">
                            <h:outputText styleClass="tituloCampos" value="#{item.descricaoCurta}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                    <rich:treeNode id="tLeafC"  type="leaf" dragType="pic">
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formCentro:botaoSelecao','formCentro:codigoSelecao', #{item.codigo})"
                                         id="codigoDescricao">
                            <h:outputText styleClass="tituloCampos" value="#{item.codigoCentro} - #{item.descricao}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                </rich:tree>
            </h:panelGrid>            
        </rich:panel>
        <a4j:form>
            <a4j:commandButton value="Atualizar" action="#{CentroCustosControle.loadTree}" reRender="treeViewCentro"
								style="width:100px; height:30px;"
            					image="../../../imagens/botaoAtualizarGrande.png"/>
        </a4j:form>
    </div>
    <!-- fim: TREE VIEW -->
    <!-- Controles para a tree view -->
    <h:form id="formCentro">
        <%--form:nomeCentroSelecionado usado no lançamento rápido--%>
        <%--formLanc:nomeCentroSelecionado usado contas a pagar e receber--%>
        <a4j:commandButton style="visibility: hidden;" reRender="formLanc:nomeCentroSelecionadoRateio, formLanc:nomeCentroSelecionado,formTelaLancRapido:pgCentroCustos, modalCentros, panelMensagem,modalIncluirRateio,formEdicaoCentroCustos,formDeposito, form:nomeCentroSelecionado, formLancarPagamento:definirPlanoContaCentroCustoValorSuperiorGeral, form:nomeCentroSelecionadoRateioPix"
                           id="botaoSelecao" action="#{CentroCustosControle.processSelection}" oncomplete="Richfaces.hideModalPanel('modalCentros');"></a4j:commandButton>
        <h:inputHidden id="codigoSelecao"
                       value="#{CentroCustosControle.codigoBancoCentroCustos}" /></h:form>
    <!-- fim: Controles para a tree view -->
</rich:modalPanel>
