<%-- 
    Document   : include_menu
    Created on : 14/05/2012, 14:26:28
    Author     : Waller
--%>
<head>
    <script type="text/javascript" src="../../../script/script.js">
    </script>
</head>

    <%@include file="../../../includes/imports.jsp" %>

<style type="text/css">
    .inputPesquisa {
        background: url("${contextoFinan}/images/nav_logo37.png") bottom !important;
    }
</style>
<tr>
    <td height="48" align="left" valign="top" class="bgmenu">
        <table width="100%" height="48" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td width="600" align="left" valign="top" class="bgmenuleft" style="padding:9px 0 0 80px;">
                    <div style="float:left;">
                        <ul class="btnazul">
                            <li>
                                <p class="btnleft"></p>

                                <p class="btnmiddle">
                                    <a4j:commandLink id="btnInicialFinan" disabled="#{!LoginControle.apresentarLinkFinanceiro}"
                                                     action="#{NavegacaoFinanceiro.abrirTelaInicial}" value="Inicial"/>
                                </p>

                                <p class="btnright"></p>
                            </li>
                            <li>
                                <p class="btnleft"></p>

                                <p class="btnmiddle">
                                    <a4j:commandLink id="btnCadFinan" disabled="#{!LoginControle.apresentarLinkFinanceiro}"
                                                     action="#{NavegacaoFinanceiro.abrirTelaCadastro}"
                                                     value="Cadastros"/>
                                </p>

                                <p class="btnright"></p>
                            </li>

                            <c:if test="${LoginControle.permissaoAcessoMenuVO.BIFinanceiro}">
                                <li>
                                    <p class="btnleft"></p>

                                    <p class="btnmiddle">
                                        <a4j:commandLink id="btnBIFinan" disabled="#{!LoginControle.apresentarLinkFinanceiro}"
                                                       action="#{BIFinanceiroControle.btnBIFinanceiroAction}"
                                                       value="Business Intelligence"/>
                                    </p>

                                    <p class="btnright"></p>
                                </li>
                            </c:if>
                        </ul>
                    </div>
                </td>

                <td width="50%" align="right" valign="top" style="padding:9px 13px 4px 80px;">
                    &nbsp;

                    <div id="divPesquisaFuncionalidade" style="position: relative;float: right;margin-top: 0;">

                        <h:inputText id="textfield2"

                                     size="220"
                                     maxlength="220"
                                     style="border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0px;
                             width:290px;
                             vertical-align: middle;"
                                     styleClass="form inputPesquisa" value="#{ConsultaClienteControle.valorConsultaParametrizada}"
                                     onfocus="if(this.value=='Palavra-chave')this.value=''"
                                     onblur="if(this.value=='')this.value='Palavra-chave'">
                        </h:inputText>
                        <rich:suggestionbox  height="200" width="299"

                                             for="textfield2"
                                             frequency="0"
                                             popupStyle="margin-left: 12px;background-color: #000066;"
                                             fetchValue="#{FuncionalidadeControle.rotulo}"
                                             suggestionAction="#{FuncionalidadeControle.executarAutocompleteFuncionalidade}"
                                             minChars="1" rowClasses="20"
                                             status="true"
                                             nothingLabel="Nenhuma funcionalidade Encontrada!"
                                             var="result" id="suggestionFuncionalidade">
                            <a4j:support event="onselect"
                                         action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                         actionListener="#{FuncionalidadeControle.abrirComAcaoListener}"
                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"

                                         focus="buscaFuncionalidade" reRender="textfield2,suggestionFuncionalidade,divPesquisaFuncionalidade">
                                <f:attribute name="paginaInicial" value="paginaInicial" />
                                <f:attribute name="tipoConsulta" value="parametrizado"/>
                                <c:if test="${modulo eq '4bf2add2267962ea87f029fef8f75a2f'}">
                                    <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                                </c:if>
                            </a4j:support>
                            <h:column>
                                <h:graphicImage rendered="#{!result.cliente && result.funcionalidadeSistemaEnum !=null}" style="width:28px;height:25px;" url="#{result.funcionalidadeSistemaEnum.iconeModulo}"/>

                                <a4j:mediaOutput     element="img" rendered="#{result.cliente}"
                                                     align="left" style="left:0px;width:25px;height:25px; border:none;border-radius:5px; "
                                                     cacheable="false" session="false"
                                                     title="#{result.clienteVO.pessoa.nome}"
                                                     createContent="#{FuncionalidadeControle.paintFoto}"
                                                     styleClass="shadow"
                                                     value="#{ImagemData}" mimeType="image/jpeg" >
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="25"/>
                                    <f:param name="altura" value="25"/>
                                    <f:param name="pessoa" value="#{result.clienteVO.codigo}"></f:param>
                                </a4j:mediaOutput>
                                <h:panelGroup layout="block" style="height: 25px" rendered="#{!result.cliente && result.funcionalidadeClienteEnum !=null}"/>
                            </h:column>

                            <h:column>

                                <h:panelGroup layout="block" rendered="#{!result.descricao}" style="width: 250px;text-align: left;">
                                    <h:outputText rendered="#{!result.cliente && result.funcionalidadeClienteEnum !=null}" style="font-size: 15px;text-align: right;" styleClass="fa-icon-arrow-right"/>
                                    <rich:spacer width="5"/>
                                    <h:outputText style="text-align: left" value="#{result.rotulo}"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" rendered="#{result.descricao}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
                                    <h:outputText style="text-align: left;font-size: 11px;font-weight: bold;color: #a09fa0" value="#{result.itemDescricao}"/>
                                </h:panelGroup>
                            </h:column>


                        </rich:suggestionbox>
                        <h:outputLabel id="botaoBuscar" styleClass="fa-icon-search"
                                       style="opacity:0.6;width:20px;height:20px;position:absolute;cursor:pointer;right:6px;top:4px;z-index:0">

                        </h:outputLabel>
                        <%--<script>--%>
                            <%--document.getElementById("form:textfield2").style.background = "url(${contextoFinan}/images/nav_logo37.png) bottom";--%>
                        <%--</script>--%>
                    </div>
                    <rich:modalPanel id="panelStatus1" autosized="true">
                        <h:panelGrid columns="3">
                            <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
                            <h:outputText styleClass="titulo3" value="Carregando..."/>
                        </h:panelGrid>
                    </rich:modalPanel>

                    <rich:spacer width="4"/>
                </td>
            </tr>
        </table>
    </td>
</tr>
