<%-- 
    Document   : gestaoMensagemClienteProdutoVencido
    Created on : 25/07/2012, 11:32:15
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }

    td.w48{
        width: 48%;
    }
    body{
        background-color: #fff !important;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
        <h:form id="form">
            <jsp:include page="include_head.jsp" flush="true" />
            <title>
                <h:outputText value="Gestão de mensagem de produto vencido"/>
            </title>
            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0"
                         columnClasses="colunaEsquerda">
                <c:set var="titulo" scope="session" value="O que deseja fazer com a mensagem?"/>
                <c:set var="urlWiki" scope="session" value="#"/>
                <f:facet name="header">
                    <jsp:include page="topoReduzido_material.jsp"/>
                </f:facet>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <a4j:form id="formGestaoMsgClienteProdutoVencido">
                    <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td height="30" align="center" valign="top">
                            </td>
                        </tr>
                        <tr>
                            <td height="50" align="center" valign="top">
                                <a4j:commandButton id="btnNaoBloquear" title="Não bloquear aluno"
                                                   value="#{msg_bt.btn_gestaoMsgProdVencNaoBloq}"
                                                   action="#{ClienteControle.desbloquearMsgProdutoVencido}"
                                                   styleClass="botoes nvoBt btSec"
                                                   oncomplete="fireElementFromParent('form:btnAtualizaCliente'); fecharJanela();"/>
                                <p style="color: #777777; padding: 0em 2em 3em 2em">
                                    Libera o aluno na catraca permitindo que o mesmo continue acessando normalmente mesmo com o produto pendente.
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td height="50" align="center" valign="top">
                                <a4j:commandButton id="btnApagarMsg" title="Apagar mensagem"
                                                   value="#{msg_bt.btn_gestaoMsgProdVencApagarMsg}"
                                                   action="#{ClienteControle.excluirClienteMensagemProdutoVencido}"
                                                   styleClass="botoes nvoBt btSec"
                                                   oncomplete="fireElementFromParent('form:btnAtualizaCliente'); fecharJanela();"/>
                                <p style="color: #777777; padding: 0em 2em 3em 2em">
                                    Esta opção apenas apaga a mensagem de alerta do cadastro do aluno no ZW e o aluno continuará sendo bloqueado na catraca até que renove o produto vencido.
                                    Para não bloquear o aluno, utilize a opção acima: "Não bloquear o aluno"
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td height="50" align="center" valign="top">
                                <a4j:commandButton id="btnRealizarContato" title="Realizar contato"
                                                 value="#{msg_bt.btn_gestaoMsgProdVencRealizarContato}"
                                                 styleClass="botoes nvoBt btSec"
                                                 action="#{NavegacaoControle.abrirRealizarContato}"/>
                                <p style="color: #777777; padding: 0em 2em 3em 2em">
                                    Realizar contato avulso com o cliente agora
                                </p>
                            </td>
                        </tr>
                    </table>
                </a4j:form>
            </h:panelGrid >
        </h:form>
</f:view>
