
<%@include file="pages/finan/includes/include_imports.jsp" %>
<link href="css/financeiro.css" rel="stylesheet" type="text/css">
<link href="css/otimize.css" rel="stylesheet" type="text/css">




<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:form id="form">
        <table border="1" width="100%" >
          <tr>
              <td width="5px">
                  <h:graphicImage url="images/logo_zillyon.jpg" ></h:graphicImage>
              </td>
              <td>
                  <center>
                    <h1 style="font-family: Trebuchet MS">Detalhamento do Cumprimento da Meta </h1>
                  </center>
              </td>
              <td>
                  <table width="100%" >
                    <tr>
                        <td>
                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_msg_data}:" ></h:outputText>
                        <h:outputText value="#{MetaFinanceiroBIControle.dataMetaAtualizada}" styleClass="tituloDemonstrativo">
			                				<f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>  
			            </h:outputText>
			            </td>
                    </tr>
                    <tr>
                        <td> 
                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_msg_usuario}:" ></h:outputText>
                        <h:outputText styleClass="tituloDemonstrativo" value="#{MetaFinanceiroBIControle.nomeUsuarioLogado}"/>
                        </td>
                    </tr>

                  </table>
              </td>

          </tr>

        </table>
        <rich:panel>
			<h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_filtros}: "></h:outputText>
			<h:outputText escape="false" styleClass="tituloDemonstrativo" value="#{MetaFinanceiroBIControle.filtros}"></h:outputText>
		</rich:panel>

      <%@include file="pages/finan/includes/include_detalhamentoMetaAtual.jsp" %>

        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="background-color:#FFFFFF;">
            <h:commandLink
                onclick="window.print();return false;">
                <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
            </h:commandLink>
        </h:panelGrid>

  </a4j:form>
</f:view>
